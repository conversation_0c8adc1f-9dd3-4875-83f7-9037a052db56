exports.id=40,exports.ids=[40],exports.modules={3815:(a,b,c)=>{Promise.resolve().then(c.bind(c,14329))},5221:(a,b,c)=>{Promise.resolve().then(c.bind(c,57347))},6673:(a,b,c)=>{Promise.resolve().then(c.bind(c,10042))},8099:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},10042:(a,b,c)=>{"use strict";c.d(b,{SessionProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\providers\\session-provider.tsx","useSession");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\providers\\session-provider.tsx","SessionProvider")},11301:(a,b,c)=>{Promise.resolve().then(c.bind(c,54413))},14329:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(60687);c(43210);var e=c(85814),f=c.n(e),g=c(42613),h=c(93613),i=c(78122),j=c(32192),k=c(48730);function l({error:a,reset:b}){let c=a.message.includes("fetch")?{type:"Erro de Conex\xe3o",description:"Problema de conectividade com o servidor",icon:"\uD83C\uDF10"}:a.message.includes("auth")?{type:"Erro de Autentica\xe7\xe3o",description:"Problema com credenciais ou sess\xe3o expirada",icon:"\uD83D\uDD10"}:a.message.includes("permission")?{type:"Erro de Permiss\xe3o",description:"Voc\xea n\xe3o tem permiss\xe3o para acessar este recurso",icon:"\uD83D\uDEAB"}:{type:"Erro Interno",description:"Erro inesperado na aplica\xe7\xe3o",icon:"⚠️"};return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,d.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6",children:(0,d.jsx)(h.A,{className:"h-12 w-12 text-red-600"})}),(0,d.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Ops! Algo deu errado"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"Ocorreu um erro inesperado. Nossa equipe foi notificada."})]})}),(0,d.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-2xl",children:(0,d.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("span",{className:"text-2xl",children:c.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:c.type}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:c.description})]})]})}),(0,d.jsxs)("div",{className:"mb-8 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Detalhes T\xe9cnicos:"}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Erro:"})," ",a.message]}),a.digest&&(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"ID do Erro:"})," ",a.digest]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Timestamp:"})," ",new Date().toLocaleString("pt-BR")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"URL:"})," ","N/A"]})]})]}),!1,(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)(g.$n,{onClick:b,variant:"primary",className:"flex-1",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Tentar Novamente"]}),(0,d.jsx)(f(),{href:"/dashboard",className:"flex-1",children:(0,d.jsxs)(g.$n,{variant:"outline",className:"w-full",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Ir para Dashboard"]})})]}),"Erro de Autentica\xe7\xe3o"===c.type&&(0,d.jsx)(f(),{href:"/login",className:"block",children:(0,d.jsx)(g.$n,{variant:"outline",className:"w-full",children:"Fazer Login Novamente"})}),"Erro de Conex\xe3o"===c.type&&(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Verifique sua conex\xe3o com a internet e tente novamente."}),(0,d.jsx)(g.$n,{onClick:()=>window.location.reload(),variant:"outline",size:"sm",children:"Recarregar P\xe1gina"})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-6 mt-6",children:(0,d.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,d.jsx)("p",{className:"mb-2",children:"Se o problema persistir, entre em contato com o suporte t\xe9cnico."}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-xs",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[(0,d.jsx)("strong",{children:"Sistema RLPONTO"})," - Controle de Ponto Eletr\xf4nico"]})]})]})})]})})]})}},21191:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},32075:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},42613:(a,b,c)=>{"use strict";c.d(b,{$n:()=>f,pd:()=>g,WI:()=>l.SearchIcon});var d=c(60687);c(43210);var e=c(21191);let f=({children:a,className:b,variant:c="primary",size:f="md",disabled:g=!1,loading:h=!1,type:i="button",onClick:j,...k})=>(0,d.jsxs)("button",{type:i,className:(0,e.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[c],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[f],b),disabled:g||h,onClick:j,...k,children:[h&&(0,d.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]}),g=({className:a,type:b="text",placeholder:c,value:f,defaultValue:g,disabled:h=!1,required:i=!1,error:j,label:k,id:l,name:m,onChange:n,onBlur:o,onFocus:p,...q})=>{let r=l||m;return(0,d.jsxs)("div",{className:"w-full",children:[k&&(0,d.jsxs)("label",{htmlFor:r,className:"block text-sm font-medium text-gray-700 mb-1",children:[k,i&&(0,d.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,d.jsx)("input",{type:b,id:r,name:m,style:{color:"#000000",backgroundColor:"#ffffff",fontSize:"16px",fontWeight:"600"},className:(0,e.cn)("flex h-12 w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-base font-semibold placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",j&&"border-red-500 focus:ring-red-500 focus:border-red-500",a),placeholder:c,value:f,defaultValue:g,disabled:h,required:i,onChange:n,onBlur:o,onFocus:p,...q}),j&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j})]})};var h=c(5336),i=c(35071),j=c(93613),k=c(96882);h.A,i.A,j.A,k.A;var l=c(98316)},43983:(a,b,c)=>{Promise.resolve().then(c.bind(c,54431))},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\01projeto\\\\my-app\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx","default")},54431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\01projeto\\\\my-app\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx","default")},57347:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q});var d=c(60687),e=c(85814),f=c.n(e),g=c(43210),h=c(42613),i=c(32192),j=c(41312),k=c(48730),l=c(10022),m=c(25541),n=c(43649),o=c(28559),p=c(99270);function q(){let[a,b]=(0,g.useState)(!1),c=[{name:"Dashboard",href:"/dashboard",icon:i.A,description:"Voltar ao painel principal"},{name:"Funcion\xe1rios",href:"/funcionarios",icon:j.A,description:"Gerenciar funcion\xe1rios"},{name:"Ponto Biom\xe9trico",href:"/ponto/biometrico",icon:k.A,description:"Registrar ponto"},{name:"Relat\xf3rios",href:"/relatorios",icon:l.A,description:"Visualizar relat\xf3rios"},{name:"Estat\xedsticas",href:"/estatisticas",icon:m.A,description:"An\xe1lises e KPIs"}];return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,d.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6",children:(0,d.jsx)(n.A,{className:"h-12 w-12 text-red-600"})}),(0,d.jsx)("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"P\xe1gina n\xe3o encontrada"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"A p\xe1gina que voc\xea est\xe1 procurando n\xe3o existe ou foi movida."})]})}),(0,d.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-2xl",children:(0,d.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,d.jsxs)("div",{className:"mb-8 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:"Informa\xe7\xf5es de Debug:"}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"URL solicitada:"})," ","N/A"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Timestamp:"})," ",a?new Date().toLocaleString("pt-BR"):"Carregando..."]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Sistema:"})," RLPONTO v1.0"]})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"flex-1",children:(0,d.jsxs)(h.$n,{variant:"primary",className:"w-full",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Ir para Dashboard"]})}),(0,d.jsxs)(h.$n,{variant:"outline",onClick:()=>window.history.back(),className:"flex-1",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Acesso R\xe1pido"}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:c.map(a=>(0,d.jsx)(f(),{href:a.href,className:"group relative rounded-lg border border-gray-300 bg-white px-6 py-4 shadow-sm hover:border-blue-500 hover:shadow-md transition-all duration-200",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-blue-600 group-hover:text-blue-700"})}),(0,d.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 group-hover:text-blue-700",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:a.description})]})]})},a.href))})]}),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3 text-sm text-gray-600",children:[(0,d.jsx)(p.A,{className:"h-5 w-5"}),(0,d.jsxs)("span",{children:["N\xe3o encontrou o que procura?",(0,d.jsx)(f(),{href:"/dashboard",className:"text-blue-600 hover:text-blue-800 ml-1",children:"Explore o dashboard"})]})]})}),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,d.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,d.jsx)("p",{children:"Se o problema persistir, entre em contato com o suporte t\xe9cnico."}),(0,d.jsxs)("p",{className:"mt-1",children:[(0,d.jsx)("strong",{children:"Sistema RLPONTO"})," - Controle de Ponto Eletr\xf4nico"]})]})})]})]})})]})}},61135:()=>{},64488:(a,b,c)=>{"use strict";c.d(b,{SessionProvider:()=>g});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0);function g({children:a}){let[b,c]=(0,e.useState)(null),[g,h]=(0,e.useState)(!0),[i,j]=(0,e.useState)(!1);return(0,d.jsx)(f.Provider,{value:{user:b,isLoading:g||!i,login:a=>{c(a)},logout:()=>{c(null)},isAuthenticated:!!b&&i},children:a})}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},85389:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(37413),e=c(22376),f=c.n(e),g=c(68726),h=c.n(g);c(61135);let i={name:"My App",description:"A professional Next.js application",author:"Your Name",url:"http://************"};var j=c(10042);let k={title:{default:i.name,template:`%s | ${i.name}`},description:i.description,keywords:["Next.js","React","TypeScript","Tailwind CSS"],authors:[{name:i.author}],creator:i.author,metadataBase:new URL(i.url),openGraph:{type:"website",locale:"en_US",url:i.url,title:i.name,description:i.description,siteName:i.name},twitter:{card:"summary_large_image",title:i.name,description:i.description},robots:{index:!0,follow:!0}};function l({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsx)(j.SessionProvider,{children:a})})})}},88177:(a,b,c)=>{Promise.resolve().then(c.bind(c,64488))},98316:(a,b,c)=>{"use strict";c.d(b,{SearchIcon:()=>g});var d=c(60687),e=c(99270),f=c(21191);function g({className:a}){return(0,d.jsx)(e.A,{className:(0,f.cn)("h-4 w-4 text-gray-500",a)})}}};