// Global types for the application

export interface User {
  id: string;
  email: string;
  name: string;
  usuario: string;
  avatar?: string;
  role: 'admin' | 'user';
  nivelAcesso: 'master' | 'admin' | 'hr' | 'manager' | 'usuario' | 'readonly';
  ativo: boolean;
  bloqueado: boolean;
  ultimoLogin?: Date;
  tentativasLogin: number;
  forcarTrocaSenha: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = unknown> {
  data: T;
  message: string;
  success: boolean;
  errors?: string[];
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

// Navigation Types
export interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType;
  children?: NavItem[];
}

// Theme Types
export type Theme = 'light' | 'dark' | 'system';

// Loading States
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Auth Types
export interface LoginCredentials {
  usuario: string;
  senha: string;
}

export interface LoginFormData {
  usuario: string;
  senha: string;
  lembrarMe?: boolean;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  error?: string;
  requiresPasswordChange?: boolean;
}

export interface AuthSession {
  user: User;
  expires: string;
}

// Funcionários Types
export interface Funcionario {
  id: string;
  nomeCompleto: string;
  cpf: string;
  rg?: string;
  email?: string;
  telefone?: string;
  celular?: string;
  endereco?: {
    cep?: string;
    logradouro?: string;
    numero?: string;
    complemento?: string;
    bairro?: string;
    cidade?: string;
    uf?: string;
  };
  dadosProfissionais: {
    matricula: string;
    cargo: string;
    setor: string;
    dataAdmissao: Date;
    salario?: number;
    cargaHoraria: number;
    horarioTrabalho: {
      entrada: string;
      saida: string;
      intervaloInicio?: string;
      intervaloFim?: string;
    };
  };
  foto?: string;
  biometria?: {
    cadastrada: boolean;
    dataUltimoCadastro?: Date;
    templates?: number;
  };
  status: 'ativo' | 'inativo' | 'desligado';
  observacoes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface FuncionarioFormData {
  nomeCompleto: string;
  cpf: string;
  rg?: string;
  email?: string;
  telefone?: string;
  celular?: string;
  cep?: string;
  logradouro?: string;
  numero?: string;
  complemento?: string;
  bairro?: string;
  cidade?: string;
  uf?: string;
  matricula: string;
  cargo: string;
  setor: string;
  dataAdmissao: string;
  salario?: number;
  cargaHoraria: number;
  horarioEntrada: string;
  horarioSaida: string;
  intervaloInicio?: string;
  intervaloFim?: string;
  observacoes?: string;
}

// Generic utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
