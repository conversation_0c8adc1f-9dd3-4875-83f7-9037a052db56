# 🔐 MÓDULO LOGIN - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável pela autenticação e controle de acesso ao sistema RLPONTO-WEB.

## 🎯 Funcionalidades
- Autenticação de usuários
- Controle de sessão
- Recuperação de senha
- Níveis de acesso (admin, usuario, readonly, status)
- Logs de auditoria de login

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   │   └── page.tsx           # Página de login
│   │   ├── forgot-password/
│   │   │   └── page.tsx           # Recuperação de senha
│   │   └── layout.tsx             # Layout de autenticação
│   └── api/
│       └── auth/
│           ├── [...nextauth]/
│           │   └── route.ts       # NextAuth configuration
│           ├── login/
│           │   └── route.ts       # API de login
│           └── logout/
│               └── route.ts       # API de logout
├── components/
│   └── auth/
│       ├── login-form.tsx         # Formulário de login
│       ├── logout-button.tsx      # Botão de logout
│       └── auth-guard.tsx         # Proteção de rotas
├── lib/
│   ├── auth.ts                    # Configuração NextAuth
│   └── validations/
│       └── auth.ts                # Validações de autenticação
└── types/
    └── auth.ts                    # Tipos TypeScript
```

## 🔧 Implementação Técnica

### 🎨 Página de Login (page.tsx)
```typescript
// app/(auth)/login/page.tsx
import { Metadata } from 'next';
import { LoginForm } from '@/components/auth/login-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Login - RLPONTO',
  description: 'Acesse o sistema de controle de ponto',
};

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">
            RLPONTO
          </CardTitle>
          <CardDescription>
            Sistema de Controle de Ponto Eletrônico
          </CardDescription>
        </CardHeader>
        <CardContent>
          <LoginForm />
        </CardContent>
      </Card>
    </div>
  );
}
```

### 🔐 Formulário de Login (login-form.tsx)
```typescript
// components/auth/login-form.tsx
'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff } from 'lucide-react';

const loginSchema = z.object({
  usuario: z.string().min(1, 'Usuário é obrigatório'),
  senha: z.string().min(1, 'Senha é obrigatória'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await signIn('credentials', {
        usuario: data.usuario,
        senha: data.senha,
        redirect: false,
      });

      if (result?.error) {
        setError('Usuário ou senha inválidos');
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      setError('Erro interno do servidor');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="usuario">Usuário</Label>
        <Input
          id="usuario"
          type="text"
          placeholder="Digite seu usuário"
          {...register('usuario')}
          disabled={isLoading}
        />
        {errors.usuario && (
          <p className="text-sm text-red-600">{errors.usuario.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="senha">Senha</Label>
        <div className="relative">
          <Input
            id="senha"
            type={showPassword ? 'text' : 'password'}
            placeholder="Digite sua senha"
            {...register('senha')}
            disabled={isLoading}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
            disabled={isLoading}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        </div>
        {errors.senha && (
          <p className="text-sm text-red-600">{errors.senha.message}</p>
        )}
      </div>

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        Entrar
      </Button>

      <div className="text-center">
        <Button variant="link" className="text-sm">
          Esqueceu sua senha?
        </Button>
      </div>
    </form>
  );
}
```

## 🔒 Configuração de Autenticação

### 🛡️ NextAuth Configuration (auth.ts)
```typescript
// lib/auth.ts
import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { prisma } from '@/lib/db';
import { compare } from 'bcryptjs';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        usuario: { label: 'Usuário', type: 'text' },
        senha: { label: 'Senha', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.usuario || !credentials?.senha) {
          return null;
        }

        const user = await prisma.usuario.findUnique({
          where: { 
            usuario: credentials.usuario,
            ativo: true 
          }
        });

        if (!user) {
          return null;
        }

        const isPasswordValid = await compare(credentials.senha, user.senhaHash);

        if (!isPasswordValid) {
          // Incrementar tentativas de login
          await prisma.usuario.update({
            where: { id: user.id },
            data: { 
              tentativasLogin: { increment: 1 }
            }
          });
          return null;
        }

        // Reset tentativas e atualizar último login
        await prisma.usuario.update({
          where: { id: user.id },
          data: { 
            tentativasLogin: 0,
            ultimoLogin: new Date()
          }
        });

        return {
          id: user.id.toString(),
          name: user.nome,
          email: user.email,
          role: user.nivelAcesso,
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 3600, // 1 hora
  },
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
};
```

## 📊 Banco de Dados

### 🗄️ Schema Prisma
```prisma
model Usuario {
  id              Int      @id @default(autoincrement())
  usuario         String   @unique
  senhaHash       String   @map("senha_hash")
  nome            String
  email           String?
  nivelAcesso     NivelAcesso @map("nivel_acesso")
  ultimoLogin     DateTime? @map("ultimo_login")
  tentativasLogin Int      @default(0) @map("tentativas_login")
  ativo           Boolean  @default(true)
  criadoEm        DateTime @default(now()) @map("criado_em")
  atualizadoEm    DateTime @updatedAt @map("atualizado_em")

  @@map("usuarios")
}

enum NivelAcesso {
  admin
  usuario
  readonly
  status
}
```

## 🎨 Estilos e Design

### 🎭 Layout de Autenticação
```typescript
// app/(auth)/layout.tsx
export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}
```

## 🧪 Testes

### ✅ Teste do Formulário de Login
```typescript
// __tests__/auth/login-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { LoginForm } from '@/components/auth/login-form';
import { signIn } from 'next-auth/react';

jest.mock('next-auth/react');
jest.mock('next/navigation');

describe('LoginForm', () => {
  it('should submit form with valid credentials', async () => {
    const mockSignIn = signIn as jest.MockedFunction<typeof signIn>;
    mockSignIn.mockResolvedValue({ error: null } as any);

    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/usuário/i), {
      target: { value: 'admin' }
    });
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });

    fireEvent.click(screen.getByRole('button', { name: /entrar/i }));

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('credentials', {
        usuario: 'admin',
        senha: 'password123',
        redirect: false,
      });
    });
  });
});
```

## 📝 Checklist de Implementação

### ✅ Tarefas Principais
- [ ] Criar página de login responsiva
- [ ] Implementar formulário com validação
- [ ] Configurar NextAuth.js
- [ ] Implementar proteção de rotas
- [ ] Criar sistema de níveis de acesso
- [ ] Implementar logs de auditoria
- [ ] Adicionar recuperação de senha
- [ ] Criar testes unitários
- [ ] Implementar rate limiting
- [ ] Configurar CSRF protection

### 🔧 Configurações Adicionais
- [ ] Configurar variáveis de ambiente
- [ ] Implementar middleware de autenticação
- [ ] Configurar cookies seguros
- [ ] Implementar timeout de sessão
- [ ] Adicionar 2FA (opcional)

## 🚀 Próximos Passos
Após completar este módulo, prosseguir para:
1. **Módulo Principal** - Dashboard e navegação
2. **Módulo Funcionários** - Gestão de funcionários
3. **Módulo Ponto** - Registro de ponto biométrico e manual
