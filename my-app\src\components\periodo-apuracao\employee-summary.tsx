'use client';

import { useState, useEffect } from 'react';
import {
  User,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Download
} from 'lucide-react';
import { Button, Input, SearchIcon } from '@/components/ui';

interface EmployeeSummary {
  funcionario: {
    id: string;
    nome: string;
    matricula: string;
    cargo: string;
    setor: string;
  };
  horasTrabalhadas: number;
  horasExtras: number;
  diasPresentes: number;
  diasAusentes: number;
  atrasos: number;
  frequencia: number;
  saldoHoras: number;
  alertas: number;
  status: 'regular' | 'atencao' | 'critico';
}

interface EmployeeSummaryProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function EmployeeSummary({ period }: EmployeeSummaryProps) {
  const [employees, setEmployees] = useState<EmployeeSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'nome' | 'frequencia' | 'horas'>('nome');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    fetchEmployeeSummary();
  }, [period]);

  const fetchEmployeeSummary = async () => {
    try {
      setLoading(true);
      
      // Simulação de dados
      const mockData: EmployeeSummary[] = [
        {
          funcionario: {
            id: 'EMP001',
            nome: 'João Silva Santos',
            matricula: 'EMP001',
            cargo: 'Analista de Sistemas',
            setor: 'TI'
          },
          horasTrabalhadas: 168,
          horasExtras: 12,
          diasPresentes: 21,
          diasAusentes: 1,
          atrasos: 2,
          frequencia: 95.5,
          saldoHoras: 4,
          alertas: 1,
          status: 'regular'
        },
        {
          funcionario: {
            id: 'EMP002',
            nome: 'Maria Oliveira Costa',
            matricula: 'EMP002',
            cargo: 'Gerente de Vendas',
            setor: 'Vendas'
          },
          horasTrabalhadas: 176,
          horasExtras: 25,
          diasPresentes: 22,
          diasAusentes: 0,
          atrasos: 0,
          frequencia: 100,
          saldoHoras: 8,
          alertas: 1,
          status: 'atencao'
        },
        {
          funcionario: {
            id: 'EMP003',
            nome: 'Carlos Roberto Lima',
            matricula: 'EMP003',
            cargo: 'Operador de Produção',
            setor: 'Produção'
          },
          horasTrabalhadas: 160,
          horasExtras: 5,
          diasPresentes: 20,
          diasAusentes: 2,
          atrasos: 5,
          frequencia: 90.9,
          saldoHoras: -4,
          alertas: 2,
          status: 'atencao'
        },
        {
          funcionario: {
            id: 'EMP004',
            nome: 'Ana Paula Silva',
            matricula: 'EMP004',
            cargo: 'Analista de RH',
            setor: 'RH'
          },
          horasTrabalhadas: 172,
          horasExtras: 8,
          diasPresentes: 21,
          diasAusentes: 1,
          atrasos: 1,
          frequencia: 95.5,
          saldoHoras: 2,
          alertas: 0,
          status: 'regular'
        },
        {
          funcionario: {
            id: 'EMP005',
            nome: 'Pedro Henrique Souza',
            matricula: 'EMP005',
            cargo: 'Assistente Administrativo',
            setor: 'Administração'
          },
          horasTrabalhadas: 155,
          horasExtras: 2,
          diasPresentes: 19,
          diasAusentes: 3,
          atrasos: 8,
          frequencia: 86.4,
          saldoHoras: -8,
          alertas: 3,
          status: 'critico'
        }
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setEmployees(mockData);
    } catch (error) {
      console.error('Erro ao buscar resumo dos funcionários:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAndSortedEmployees = employees
    .filter(emp => 
      emp.funcionario.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.funcionario.matricula.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.funcionario.cargo.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'nome':
          aValue = a.funcionario.nome;
          bValue = b.funcionario.nome;
          break;
        case 'frequencia':
          aValue = a.frequencia;
          bValue = b.frequencia;
          break;
        case 'horas':
          aValue = a.horasTrabalhadas;
          bValue = b.horasTrabalhadas;
          break;
        default:
          aValue = a.funcionario.nome;
          bValue = b.funcionario.nome;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      return sortOrder === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'regular':
        return 'bg-green-100 text-green-800';
      case 'atencao':
        return 'bg-yellow-100 text-yellow-800';
      case 'critico':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'regular':
        return 'Regular';
      case 'atencao':
        return 'Atenção';
      case 'critico':
        return 'Crítico';
      default:
        return 'N/A';
    }
  };

  const handleSort = (field: 'nome' | 'frequencia' | 'horas') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-6 gap-4 pb-2 border-b">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="grid grid-cols-6 gap-4 py-3">
            {[...Array(6)].map((_, j) => (
              <div key={j} className="h-4 bg-gray-200 rounded animate-pulse" />
            ))}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Controles */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2" />
            <Input
              placeholder="Buscar funcionário..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
        </div>
        
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Exportar
        </Button>
      </div>

      {/* Tabela */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th 
                className="text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900"
                onClick={() => handleSort('nome')}
              >
                <div className="flex items-center space-x-1">
                  <span>Funcionário</span>
                  {sortBy === 'nome' && (
                    sortOrder === 'asc' ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th 
                className="text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900"
                onClick={() => handleSort('frequencia')}
              >
                <div className="flex items-center space-x-1">
                  <span>Frequência</span>
                  {sortBy === 'frequencia' && (
                    sortOrder === 'asc' ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th 
                className="text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900"
                onClick={() => handleSort('horas')}
              >
                <div className="flex items-center space-x-1">
                  <span>Horas</span>
                  {sortBy === 'horas' && (
                    sortOrder === 'asc' ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th className="text-left py-3 px-4 font-medium text-gray-700">Extras</th>
              <th className="text-left py-3 px-4 font-medium text-gray-700">Saldo</th>
              <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
            </tr>
          </thead>
          <tbody>
            {filteredAndSortedEmployees.map((employee) => (
              <tr key={employee.funcionario.id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{employee.funcionario.nome}</div>
                      <div className="text-sm text-gray-500">
                        {employee.funcionario.matricula} • {employee.funcionario.cargo}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <span className={`font-medium ${
                      employee.frequencia >= 95 ? 'text-green-600' : 
                      employee.frequencia >= 90 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {employee.frequencia.toFixed(1)}%
                    </span>
                    <div className="text-sm text-gray-500">
                      ({employee.diasPresentes}/{employee.diasPresentes + employee.diasAusentes})
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{employee.horasTrabalhadas}h</span>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <span className={`font-medium ${
                    employee.horasExtras > 20 ? 'text-red-600' : 
                    employee.horasExtras > 10 ? 'text-yellow-600' : 'text-gray-900'
                  }`}>
                    {employee.horasExtras}h
                  </span>
                </td>
                <td className="py-3 px-4">
                  <span className={`font-medium ${
                    employee.saldoHoras > 0 ? 'text-green-600' : 
                    employee.saldoHoras < 0 ? 'text-red-600' : 'text-gray-900'
                  }`}>
                    {employee.saldoHoras > 0 ? '+' : ''}{employee.saldoHoras}h
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(employee.status)}`}>
                      {getStatusLabel(employee.status)}
                    </span>
                    {employee.alertas > 0 && (
                      <div className="flex items-center space-x-1">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span className="text-xs text-red-600">{employee.alertas}</span>
                      </div>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredAndSortedEmployees.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <User className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">Nenhum funcionário encontrado</p>
        </div>
      )}
    </div>
  );
}

