#!/bin/bash

# 📊 Script de Backup e Monitoramento - Sistema RLPONTO
# Backup automático do banco de dados e monitoramento do sistema

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
BACKUP_DIR="/opt/rlponto/backups"
DB_NAME="rlponto"
DB_USER="rlponto_user"
DB_PASS="RLPonto@DB2024#"
RETENTION_DAYS=30

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# Função de backup do banco de dados
backup_database() {
    log "Iniciando backup do banco de dados..."
    
    # Criar diretório de backup se não existir
    mkdir -p ${BACKUP_DIR}/database
    
    # Nome do arquivo de backup
    BACKUP_FILE="${BACKUP_DIR}/database/rlponto_$(date +%Y%m%d_%H%M%S).sql.gz"
    
    # Executar backup
    mysqldump -u ${DB_USER} -p${DB_PASS} ${DB_NAME} | gzip > ${BACKUP_FILE}
    
    if [ $? -eq 0 ]; then
        log "✅ Backup do banco criado: ${BACKUP_FILE}"
        
        # Verificar tamanho do backup
        BACKUP_SIZE=$(du -h ${BACKUP_FILE} | cut -f1)
        log "Tamanho do backup: ${BACKUP_SIZE}"
    else
        error "❌ Falha ao criar backup do banco de dados"
    fi
}

# Função de backup dos uploads
backup_uploads() {
    log "Iniciando backup dos uploads..."
    
    UPLOADS_DIR="/opt/rlponto/shared/uploads"
    
    if [ -d "${UPLOADS_DIR}" ]; then
        mkdir -p ${BACKUP_DIR}/uploads
        
        UPLOADS_BACKUP="${BACKUP_DIR}/uploads/uploads_$(date +%Y%m%d_%H%M%S).tar.gz"
        
        tar -czf ${UPLOADS_BACKUP} -C ${UPLOADS_DIR} .
        
        if [ $? -eq 0 ]; then
            log "✅ Backup dos uploads criado: ${UPLOADS_BACKUP}"
            
            UPLOADS_SIZE=$(du -h ${UPLOADS_BACKUP} | cut -f1)
            log "Tamanho do backup: ${UPLOADS_SIZE}"
        else
            warning "⚠️ Falha ao criar backup dos uploads"
        fi
    else
        log "Diretório de uploads não encontrado, pulando backup"
    fi
}

# Função de limpeza de backups antigos
cleanup_old_backups() {
    log "Limpando backups antigos (>${RETENTION_DAYS} dias)..."
    
    # Limpar backups de banco antigos
    find ${BACKUP_DIR}/database -name "*.sql.gz" -mtime +${RETENTION_DAYS} -delete 2>/dev/null || true
    
    # Limpar backups de uploads antigos
    find ${BACKUP_DIR}/uploads -name "*.tar.gz" -mtime +${RETENTION_DAYS} -delete 2>/dev/null || true
    
    log "✅ Limpeza de backups concluída"
}

# Função de monitoramento do sistema
monitor_system() {
    log "Verificando status do sistema..."
    
    # Verificar se aplicação está rodando
    if pm2 list | grep -q "rlponto.*online"; then
        log "✅ Aplicação RLPONTO está online"
    else
        error "❌ Aplicação RLPONTO não está rodando!"
    fi
    
    # Verificar MySQL
    if systemctl is-active --quiet mysql; then
        log "✅ MySQL está rodando"
    else
        error "❌ MySQL não está rodando!"
    fi
    
    # Verificar Nginx
    if systemctl is-active --quiet nginx; then
        log "✅ Nginx está rodando"
    else
        error "❌ Nginx não está rodando!"
    fi
    
    # Verificar espaço em disco
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ ${DISK_USAGE} -gt 80 ]; then
        warning "⚠️ Uso de disco alto: ${DISK_USAGE}%"
    else
        log "✅ Uso de disco: ${DISK_USAGE}%"
    fi
    
    # Verificar memória
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ ${MEMORY_USAGE} -gt 80 ]; then
        warning "⚠️ Uso de memória alto: ${MEMORY_USAGE}%"
    else
        log "✅ Uso de memória: ${MEMORY_USAGE}%"
    fi
    
    # Verificar conectividade da aplicação
    if curl -f http://localhost:3000/health >/dev/null 2>&1; then
        log "✅ Aplicação respondendo corretamente"
    else
        error "❌ Aplicação não está respondendo!"
    fi
}

# Função de relatório de status
generate_status_report() {
    log "Gerando relatório de status..."
    
    REPORT_FILE="${BACKUP_DIR}/status_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > ${REPORT_FILE} << EOF
========================================
RELATÓRIO DE STATUS - SISTEMA RLPONTO
========================================
Data: $(date)
Servidor: $(hostname)
IP: $(hostname -I | awk '{print $1}')

SERVIÇOS:
$(systemctl is-active mysql nginx)

APLICAÇÃO:
$(pm2 list | grep rlponto || echo "Não encontrada")

RECURSOS:
Uso de CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%
Uso de Memória: $(free | awk 'NR==2{printf "%.0f", $3*100/$2}')%
Uso de Disco: $(df / | awk 'NR==2 {print $5}')

LOGS RECENTES:
$(tail -n 10 /var/log/rlponto/combined.log 2>/dev/null || echo "Logs não encontrados")

BACKUPS:
$(ls -la ${BACKUP_DIR}/database/ | tail -n 5)
EOF

    log "✅ Relatório gerado: ${REPORT_FILE}"
}

# Menu principal
case "${1:-backup}" in
    "backup")
        echo -e "${BLUE}📦 Executando Backup Completo${NC}"
        backup_database
        backup_uploads
        cleanup_old_backups
        ;;
    "monitor")
        echo -e "${BLUE}📊 Executando Monitoramento${NC}"
        monitor_system
        ;;
    "report")
        echo -e "${BLUE}📋 Gerando Relatório${NC}"
        generate_status_report
        ;;
    "full")
        echo -e "${BLUE}🔄 Executando Rotina Completa${NC}"
        backup_database
        backup_uploads
        cleanup_old_backups
        monitor_system
        generate_status_report
        ;;
    *)
        echo "Uso: $0 {backup|monitor|report|full}"
        echo ""
        echo "Opções:"
        echo "  backup  - Executar backup do banco e uploads"
        echo "  monitor - Verificar status dos serviços"
        echo "  report  - Gerar relatório de status"
        echo "  full    - Executar todas as operações"
        exit 1
        ;;
esac

log "✅ Operação concluída!"
