# 👑 Usuário Master do Sistema RLPONTO

## 📋 Visão Geral

O Sistema RLPONTO possui um usuário master (super administrador) que é criado automaticamente durante a instalação inicial do sistema. Este usuário tem privilégios especiais e não pode ser removido ou ter seus privilégios reduzidos.

## 🔐 Credenciais do Usuário Master

### Informações de Acesso
- **Usuário**: `admin`
- **Senha**: `200381`
- **Nível de Acesso**: `master` (super administrador)
- **Status**: Sempre ativo e não pode ser bloqueado

### Características Especiais
- ✅ **Não pode ser deletado**: O usuário master é protegido contra exclusão
- ✅ **Não pode se auto-deletar**: Mesmo logado como master, não pode deletar a própria conta
- ✅ **Privilégios totais**: Acesso completo a todas as funcionalidades do sistema
- ✅ **Bypass de restrições**: Pode acessar o sistema mesmo com configurações restritivas
- ✅ **Auditoria especial**: Todas as ações são logadas com prioridade alta

## 🛡️ Permissões e Privilégios

### Acesso Total
O usuário master tem acesso irrestrito a:

#### 🏢 **Administração da Empresa**
- Configurar dados da empresa principal
- Definir políticas de ponto
- Configurar tolerâncias e regras
- Gerenciar configurações globais

#### 👥 **Gestão de Usuários**
- Criar, editar e excluir usuários
- Definir níveis de acesso
- Resetar senhas de qualquer usuário
- Bloquear/desbloquear contas
- Visualizar logs de auditoria de usuários

#### 👨‍💼 **Gestão de Funcionários**
- Cadastrar novos funcionários
- Editar informações de funcionários
- Gerenciar funcionários desligados
- Configurar horários de trabalho
- Definir escalas e turnos

#### 📊 **Relatórios e Análises**
- Gerar todos os tipos de relatórios
- Acessar dados históricos completos
- Exportar dados em qualquer formato
- Visualizar estatísticas avançadas

#### ⚙️ **Configurações do Sistema**
- Modificar configurações de segurança
- Configurar integrações
- Gerenciar backups
- Acessar logs do sistema
- Configurar notificações

## 🔒 Segurança e Proteções

### Proteções Implementadas

#### **1. Proteção contra Auto-Exclusão**
```typescript
// Validação no backend
if (userToDelete.id === currentUser.id && currentUser.role === 'master') {
  throw new Error('Usuário master não pode deletar a própria conta');
}

if (userToDelete.role === 'master' && userToDelete.usuario === 'admin') {
  throw new Error('Usuário master do sistema não pode ser deletado');
}
```

#### **2. Proteção contra Alteração de Privilégios**
```typescript
// Validação para mudança de role
if (userToUpdate.usuario === 'admin' && userToUpdate.role === 'master') {
  if (newRole !== 'master') {
    throw new Error('Não é possível alterar o nível do usuário master');
  }
}
```

#### **3. Proteção contra Bloqueio**
```typescript
// Validação para bloqueio
if (userToBlock.usuario === 'admin' && userToBlock.role === 'master') {
  throw new Error('Usuário master não pode ser bloqueado');
}
```

### Auditoria Especial
Todas as ações do usuário master são registradas com:
- **Prioridade**: CRITICAL
- **Categoria**: MASTER_ACTION
- **Detalhamento**: Completo de todas as operações
- **Retenção**: Permanente (não pode ser excluído)

## 🚀 Implementação Técnica

### Estrutura no Banco de Dados
```sql
-- Usuário master no banco
INSERT INTO usuarios (
  usuario,
  senha_hash,
  nome,
  email,
  nivel_acesso,
  ativo,
  bloqueado,
  forcar_troca_senha,
  criado_em,
  atualizado_em
) VALUES (
  'admin',
  '$2a$10$hash_da_senha_200381',
  'Administrador Master',
  '<EMAIL>',
  'master',
  true,
  false,
  false,
  NOW(),
  NOW()
);
```

### Validações de Segurança
```typescript
// Enum de níveis de acesso
enum UserRole {
  MASTER = 'master',
  ADMIN = 'admin',
  HR = 'hr',
  MANAGER = 'manager',
  USER = 'user',
  READONLY = 'readonly'
}

// Verificação de usuário master
const isMasterUser = (user: User): boolean => {
  return user.usuario === 'admin' && user.nivelAcesso === 'master';
};

// Proteção contra operações perigosas
const canDeleteUser = (currentUser: User, targetUser: User): boolean => {
  // Master não pode se deletar
  if (isMasterUser(targetUser)) {
    return false;
  }
  
  // Usuário não pode deletar a si mesmo
  if (currentUser.id === targetUser.id) {
    return false;
  }
  
  return currentUser.nivelAcesso === 'master' || currentUser.nivelAcesso === 'admin';
};
```

## 📝 Procedimentos de Emergência

### Recuperação de Acesso Master
Em caso de perda de acesso ao usuário master:

1. **Acesso direto ao banco de dados**
2. **Reset da senha via script**
3. **Verificação de integridade**
4. **Auditoria de segurança**

### Script de Recuperação
```sql
-- Reset da senha do usuário master
UPDATE usuarios 
SET senha_hash = '$2a$10$hash_da_nova_senha',
    forcar_troca_senha = false,
    tentativas_login = 0,
    bloqueado = false
WHERE usuario = 'admin' AND nivel_acesso = 'master';
```

## ⚠️ Considerações de Segurança

### Boas Práticas
- 🔐 **Alterar senha padrão**: Embora seja 200381, recomenda-se alteração
- 🔒 **Acesso restrito**: Apenas pessoal autorizado deve conhecer as credenciais
- 📝 **Monitoramento**: Todas as ações devem ser monitoradas
- 🔄 **Rotação de senhas**: Política de troca periódica
- 🛡️ **2FA**: Implementar autenticação de dois fatores quando disponível

### Alertas de Segurança
- 🚨 **Login master**: Notificação imediata para administradores
- 🚨 **Ações críticas**: Alertas para operações sensíveis
- 🚨 **Tentativas de acesso**: Monitoramento de tentativas falhadas
- 🚨 **Alterações de configuração**: Notificação de mudanças importantes

## 📊 Monitoramento e Logs

### Eventos Monitorados
- Login/logout do usuário master
- Criação/edição/exclusão de usuários
- Alterações de configurações críticas
- Acesso a dados sensíveis
- Exportação de relatórios
- Modificações de permissões

### Formato dos Logs
```json
{
  "timestamp": "2025-01-26T10:30:00Z",
  "user": "admin",
  "role": "master",
  "action": "USER_DELETE",
  "target": "usuario123",
  "ip": "*************",
  "userAgent": "Mozilla/5.0...",
  "severity": "CRITICAL",
  "details": {
    "targetUser": "funcionario.teste",
    "reason": "Funcionário desligado"
  }
}
```

---

**⚠️ IMPORTANTE**: Este documento contém informações sensíveis de segurança. Acesso restrito apenas a administradores autorizados.
