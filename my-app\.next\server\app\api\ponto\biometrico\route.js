(()=>{var a={};a.id=652,a.ids=[652],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30328:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{GET:()=>x,POST:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190);let v=[{id:"1",funcionarioId:"EMP001",tipo:"entrada",metodo:"fingerprint",horario:"08:00:15",data:new Date().toLocaleDateString("pt-BR"),timestamp:new Date,status:"sucesso"},{id:"2",funcionarioId:"EMP002",tipo:"intervalo_inicio",metodo:"facial",horario:"12:00:32",data:new Date().toLocaleDateString("pt-BR"),timestamp:new Date,status:"sucesso"}];async function w(a){try{let b=await a.json();if(!b.funcionarioId||!b.tipo||!b.biometricType||!b.timestamp)return u.NextResponse.json({success:!1,error:"Dados obrigat\xf3rios n\xe3o fornecidos"},{status:400});if(!["EMP001","EMP002","EMP003","EMP004"].includes(b.funcionarioId))return u.NextResponse.json({success:!1,error:"Funcion\xe1rio n\xe3o encontrado"},{status:404});let c=v.filter(a=>a.funcionarioId===b.funcionarioId).sort((a,b)=>b.timestamp.getTime()-a.timestamp.getTime())[0],d=function(a,b){let c={undefined:["entrada"],entrada:["intervalo_inicio","saida"],intervalo_inicio:["intervalo_fim"],intervalo_fim:["saida","intervalo_inicio"],saida:["entrada"]}[a||"undefined"]||[];return c.includes(b)?{valido:!0}:{valido:!1,erro:`Sequ\xeancia inv\xe1lida. Ap\xf3s ${z(a||"")}, esperado: ${c.map(z).join(" ou ")}`}}(c?.tipo,b.tipo);if(!d.valido)return u.NextResponse.json({success:!1,error:d.erro},{status:400});let e=new Date(b.timestamp),f={id:Date.now().toString(),funcionarioId:b.funcionarioId,tipo:b.tipo,metodo:b.biometricType,horario:e.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",second:"2-digit"}),data:e.toLocaleDateString("pt-BR"),timestamp:e,deviceId:b.deviceId,status:"sucesso"};v.push(f);let g=y(b.funcionarioId);return u.NextResponse.json({success:!0,message:"Ponto registrado com sucesso",registro:{id:f.id,funcionario:g,tipo:z(b.tipo),horario:f.horario,data:f.data,metodo:A(b.biometricType)}})}catch(a){return console.error("Erro ao registrar ponto:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function x(a){try{let{searchParams:b}=new URL(a.url),c=b.get("funcionarioId"),d=parseInt(b.get("limit")||"10"),e=[...v];c&&(e=e.filter(a=>a.funcionarioId===c)),e.sort((a,b)=>b.timestamp.getTime()-a.timestamp.getTime());let f=(e=e.slice(0,d)).map(a=>({id:a.id,funcionario:y(a.funcionarioId),tipo:a.tipo,tipoLabel:z(a.tipo),metodo:a.metodo,metodoLabel:A(a.metodo),horario:a.horario,data:a.data,status:a.status}));return u.NextResponse.json({success:!0,registros:f,total:e.length})}catch(a){return console.error("Erro ao buscar registros:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}function y(a){return({EMP001:{nome:"Jo\xe3o Silva Santos",matricula:"EMP001",cargo:"Analista de Sistemas"},EMP002:{nome:"Maria Oliveira Costa",matricula:"EMP002",cargo:"Gerente de Vendas"},EMP003:{nome:"Carlos Roberto Lima",matricula:"EMP003",cargo:"Operador de Produ\xe7\xe3o"},EMP004:{nome:"Ana Paula Silva",matricula:"EMP004",cargo:"Analista de RH"}})[a]||null}function z(a){return({entrada:"Entrada",saida:"Sa\xedda",intervalo_inicio:"In\xedcio Intervalo",intervalo_fim:"Fim Intervalo"})[a]||a}function A(a){return({fingerprint:"Biometria Digital",facial:"Reconhecimento Facial",manual:"Manual"})[a]||a}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/ponto/biometrico/route",pathname:"/api/ponto/biometrico",filename:"route",bundlePath:"app/api/ponto/biometrico/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\biometrico\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/ponto/biometrico/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,55],()=>b(b.s=30328));module.exports=c})();