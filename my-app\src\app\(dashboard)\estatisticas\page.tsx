import { <PERSON>ada<PERSON> } from 'next';
import { Suspense } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  AlertTriangle,
  Calendar,
  Activity,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Estatísticas - RLPONTO',
  description: 'Análises estatísticas e KPIs do sistema de ponto',
};

// Dados mockados para demonstração
const kpiData = {
  pontualidade: { value: 94.2, change: 2.1, trend: 'up' },
  absenteismo: { value: 3.8, change: -0.5, trend: 'down' },
  horasExtras: { value: 127.5, change: 15.2, trend: 'up' },
  produtividade: { value: 87.6, change: 0.0, trend: 'stable' }
};

const departmentStats = [
  { name: 'Vendas', funcionarios: 25, pontualidade: 96.2, absenteismo: 2.1 },
  { name: 'TI', funcionarios: 18, pontualidade: 91.8, absenteismo: 4.2 },
  { name: 'RH', funcionarios: 8, pontualidade: 98.1, absenteismo: 1.5 },
  { name: '<PERSON>iro', funcionarios: 12, pontualidade: 93.7, absenteismo: 3.8 },
  { name: 'Operações', funcionarios: 35, pontualidade: 89.4, absenteismo: 5.2 }
];

function KPICard({ title, value, unit, change, trend, icon: Icon }: {
  title: string;
  value: number;
  unit: string;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<{ className?: string }>;
}) {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return <ArrowUp className="h-4 w-4 text-green-600" />;
      case 'down': return <ArrowDown className="h-4 w-4 text-red-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Icon className="h-6 w-6 text-blue-600" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <p className="text-2xl font-semibold text-gray-900">
              {value}{unit}
            </p>
          </div>
        </div>
        <div className={`flex items-center ${getTrendColor()}`}>
          {getTrendIcon()}
          <span className="ml-1 text-sm font-medium">
            {change > 0 ? '+' : ''}{change}{unit}
          </span>
        </div>
      </div>
    </div>
  );
}

export default function EstatisticasPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Estatísticas</h1>
                <p className="text-gray-600">Análises e KPIs do sistema de ponto</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option>Últimos 30 dias</option>
                <option>Últimos 90 dias</option>
                <option>Último ano</option>
              </select>
            </div>
          </div>

          {/* KPIs Principais */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <KPICard
              title="Pontualidade"
              value={kpiData.pontualidade.value}
              unit="%"
              change={kpiData.pontualidade.change}
              trend={kpiData.pontualidade.trend as "up" | "down" | "stable"}
              icon={Target}
            />
            <KPICard
              title="Absenteísmo"
              value={kpiData.absenteismo.value}
              unit="%"
              change={kpiData.absenteismo.change}
              trend={kpiData.absenteismo.trend as "up" | "down" | "stable"}
              icon={AlertTriangle}
            />
            <KPICard
              title="Horas Extras"
              value={kpiData.horasExtras.value}
              unit="h"
              change={kpiData.horasExtras.change}
              trend={kpiData.horasExtras.trend as "up" | "down" | "stable"}
              icon={Clock}
            />
            <KPICard
              title="Produtividade"
              value={kpiData.produtividade.value}
              unit="%"
              change={kpiData.produtividade.change}
              trend={kpiData.produtividade.trend as "up" | "down" | "stable"}
              icon={TrendingUp}
            />
          </div>

          {/* Módulos de Análise */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Análise de Produtividade */}
            <Link href="/estatisticas/produtividade">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Produtividade</h3>
                      <p className="text-sm text-gray-500">Análise detalhada</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Métricas de produtividade por funcionário, departamento e período.
                      Inclui análise de eficiência e comparativos.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Análise de Absenteísmo */}
            <Link href="/estatisticas/absenteismo">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-red-100 rounded-lg">
                      <AlertTriangle className="h-8 w-8 text-red-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Absenteísmo</h3>
                      <p className="text-sm text-gray-500">Faltas e ausências</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Análise de padrões de absenteísmo, identificação de tendências
                      e impacto na produtividade.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Análise de Tendências */}
            <Link href="/estatisticas/tendencias">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <Activity className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Tendências</h3>
                      <p className="text-sm text-gray-500">Análise temporal</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Identificação de tendências temporais, sazonalidade e
                      previsões baseadas em dados históricos.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Comparativos */}
            <Link href="/estatisticas/comparativos">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-yellow-100 rounded-lg">
                      <BarChart3 className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Comparativos</h3>
                      <p className="text-sm text-gray-500">Benchmarking</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Comparação entre departamentos, períodos e funcionários.
                      Benchmarking e análise competitiva.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Heatmap de Frequência */}
            <Link href="/estatisticas/heatmap">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-indigo-100 rounded-lg">
                      <Calendar className="h-8 w-8 text-indigo-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Heatmap</h3>
                      <p className="text-sm text-gray-500">Mapa de calor</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Visualização de padrões de frequência por horário, dia
                      da semana e período do ano.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Insights Automáticos */}
            <Link href="/estatisticas/insights">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-orange-100 rounded-lg">
                      <Users className="h-8 w-8 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Insights</h3>
                      <p className="text-sm text-gray-500">Análise inteligente</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Insights automáticos baseados em IA, alertas inteligentes
                      e recomendações de melhoria.
                    </p>
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* Estatísticas por Departamento */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Estatísticas por Departamento</h2>
            </div>
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Departamento
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Funcionários
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pontualidade
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Absenteísmo
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {departmentStats.map((dept, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {dept.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.funcionarios}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            dept.pontualidade >= 95 ? 'bg-green-100 text-green-800' :
                            dept.pontualidade >= 90 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.pontualidade}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            dept.absenteismo <= 2 ? 'bg-green-100 text-green-800' :
                            dept.absenteismo <= 4 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.absenteismo}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

