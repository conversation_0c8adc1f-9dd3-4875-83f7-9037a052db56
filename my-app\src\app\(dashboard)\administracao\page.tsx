import { Metadata } from 'next';
import { 
  Settings, 
  Users, 
  Shield, 
  Database, 
  Activity, 
  Bell,
  Key,
  FileText,
  Clock,
  Building
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Administração - RLPONTO',
  description: 'Painel de administração do sistema',
};

export default function AdministracaoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Settings className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Administração</h1>
              <p className="text-gray-600">Configurações e gerenciamento do sistema</p>
            </div>
          </div>

          {/* Estatísticas Rápidas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-blue-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Usuários Ativos</p>
                  <p className="text-2xl font-semibold text-gray-900">127</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Activity className="w-4 h-4 text-green-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Sistema</p>
                  <p className="text-2xl font-semibold text-gray-900">Online</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Database className="w-4 h-4 text-yellow-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Backup</p>
                  <p className="text-2xl font-semibold text-gray-900">Hoje</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <Shield className="w-4 h-4 text-red-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Segurança</p>
                  <p className="text-2xl font-semibold text-gray-900">Alta</p>
                </div>
              </div>
            </div>
          </div>

          {/* Módulos de Administração */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Gestão de Usuários */}
            <Link href="/administracao/usuarios">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Users className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Gestão de Usuários</h3>
                      <p className="text-sm text-gray-500">Usuários e permissões</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Gerencie usuários do sistema, perfis de acesso, permissões e 
                      configurações de segurança.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Configurações do Sistema */}
            <Link href="/administracao/sistema">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <Settings className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Configurações</h3>
                      <p className="text-sm text-gray-500">Sistema e parâmetros</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Configure parâmetros gerais do sistema, horários de trabalho, 
                      feriados e políticas da empresa.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Segurança e Auditoria */}
            <Link href="/administracao/seguranca">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-red-100 rounded-lg">
                      <Shield className="h-8 w-8 text-red-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Segurança</h3>
                      <p className="text-sm text-gray-500">Auditoria e logs</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Monitore acessos, visualize logs de auditoria e configure 
                      políticas de segurança do sistema.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Backup e Manutenção */}
            <Link href="/administracao/backup">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <Database className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Backup</h3>
                      <p className="text-sm text-gray-500">Dados e manutenção</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Configure backups automáticos, restauração de dados e 
                      manutenção preventiva do sistema.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Notificações */}
            <Link href="/administracao/notificacoes">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-yellow-100 rounded-lg">
                      <Bell className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">Notificações</h3>
                      <p className="text-sm text-gray-500">Alertas e comunicação</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Configure notificações por email, SMS e alertas do sistema 
                      para diferentes eventos.
                    </p>
                  </div>
                </div>
              </div>
            </Link>

            {/* Licenças e API */}
            <Link href="/administracao/api">
              <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-indigo-100 rounded-lg">
                      <Key className="h-8 w-8 text-indigo-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">API e Licenças</h3>
                      <p className="text-sm text-gray-500">Integrações e chaves</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      Gerencie chaves de API, integrações com sistemas externos 
                      e licenças do software.
                    </p>
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* Atividade Recente */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Atividade Recente do Sistema</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-green-600" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">João Silva</span> fez login no sistema
                    </p>
                    <p className="text-xs text-gray-500">Há 5 minutos</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">
                      Relatório mensal de frequência foi gerado
                    </p>
                    <p className="text-xs text-gray-500">Há 1 hora</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Database className="w-4 h-4 text-yellow-600" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">
                      Backup automático executado com sucesso
                    </p>
                    <p className="text-xs text-gray-500">Há 2 horas</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <Settings className="w-4 h-4 text-purple-600" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">
                      Configurações de horário de trabalho atualizadas
                    </p>
                    <p className="text-xs text-gray-500">Ontem às 14:30</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

