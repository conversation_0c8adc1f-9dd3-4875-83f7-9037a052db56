# 👥 MÓDULO GERENCIAR USUÁRIOS - Sistema RLPONTO

## 📋 Visão Geral
Mó<PERSON><PERSON> responsável pela gestão completa de usuários do sistema, incluindo criação, edição, controle de acesso e auditoria.

## 🎯 Funcionalidades
- CRUD completo de usuários
- Controle de níveis de acesso
- Gestão de senhas e segurança
- Logs de auditoria de usuários
- Controle de sessões ativas
- Relatórios de acesso
- Bloqueio/desbloqueio de usuários
- Força troca de senha

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── usuarios/
│           ├── page.tsx                    # Lista de usuários
│           ├── novo/
│           │   └── page.tsx                # Criar usuário
│           ├── [id]/
│           │   ├── page.tsx                # Editar usuário
│           │   └── sessoes/
│           │       └── page.tsx            # Sessões do usuário
│           └── components/
│               ├── user-form.tsx           # Formulário de usuário
│               ├── user-table.tsx          # Tabela de usuários
│               ├── user-permissions.tsx    # Permissões
│               └── session-manager.tsx     # Gerenciador de sessões
├── components/
│   └── usuarios/
│       ├── user-card.tsx                  # Card de usuário
│       ├── role-badge.tsx                 # Badge de função
│       ├── password-generator.tsx         # Gerador de senha
│       └── access-log.tsx                 # Log de acesso
└── api/
    └── usuarios/
        ├── route.ts                       # API principal
        ├── [id]/
        │   ├── route.ts                   # CRUD usuário
        │   ├── reset-password/
        │   │   └── route.ts               # Reset senha
        │   └── sessions/
        │       └── route.ts               # Sessões
        └── bulk/
            └── route.ts                   # Operações em lote
```

## 🔧 Implementação Técnica

### 👥 Lista de Usuários (page.tsx)
```typescript
// app/(dashboard)/usuarios/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { UserTable } from './components/user-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Plus, Search, Filter } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Gerenciar Usuários - RLPONTO',
  description: 'Gestão de usuários do sistema',
};

interface UsuariosPageProps {
  searchParams: {
    search?: string;
    role?: string;
    status?: string;
    page?: string;
  };
}

export default function UsuariosPage({ searchParams }: UsuariosPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gerenciar Usuários</h1>
            <p className="text-gray-600">Gerencie usuários e permissões do sistema</p>
          </div>
        </div>
        <Button asChild>
          <Link href="/usuarios/novo">
            <Plus className="h-4 w-4 mr-2" />
            Novo Usuário
          </Link>
        </Button>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por nome, email ou usuário..."
                  className="pl-10"
                  defaultValue={searchParams.search}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select className="px-3 py-2 border border-gray-300 rounded-md">
                <option value="">Todas as funções</option>
                <option value="admin">Administrador</option>
                <option value="usuario">Usuário</option>
                <option value="readonly">Somente Leitura</option>
                <option value="status">Status</option>
              </select>
              <select className="px-3 py-2 border border-gray-300 rounded-md">
                <option value="">Todos os status</option>
                <option value="ativo">Ativo</option>
                <option value="inativo">Inativo</option>
                <option value="bloqueado">Bloqueado</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filtrar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Usuários */}
      <Card>
        <CardContent className="p-0">
          <Suspense fallback={<UserTableSkeleton />}>
            <UserTable searchParams={searchParams} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function UserTableSkeleton() {
  return (
    <div className="p-6 space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}
```

### 📋 Tabela de Usuários (user-table.tsx)
```typescript
// app/(dashboard)/usuarios/components/user-table.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Lock, Unlock, Trash2, Eye, Key } from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Usuario {
  id: number;
  usuario: string;
  nome: string;
  email: string;
  nivelAcesso: 'admin' | 'usuario' | 'readonly' | 'status';
  ultimoLogin: string | null;
  tentativasLogin: number;
  ativo: boolean;
  bloqueado: boolean;
  criadoEm: string;
}

interface UserTableProps {
  searchParams: {
    search?: string;
    role?: string;
    status?: string;
    page?: string;
  };
}

export function UserTable({ searchParams }: UserTableProps) {
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchUsuarios();
  }, [searchParams]);

  const fetchUsuarios = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchParams.search) params.append('search', searchParams.search);
      if (searchParams.role) params.append('role', searchParams.role);
      if (searchParams.status) params.append('status', searchParams.status);
      if (searchParams.page) params.append('page', searchParams.page);

      const response = await fetch(`/api/usuarios?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setUsuarios(data.usuarios);
        setTotal(data.total);
      }
    } catch (error) {
      console.error('Erro ao buscar usuários:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      admin: { label: 'Administrador', variant: 'destructive' as const },
      usuario: { label: 'Usuário', variant: 'default' as const },
      readonly: { label: 'Somente Leitura', variant: 'secondary' as const },
      status: { label: 'Status', variant: 'outline' as const },
    };

    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.usuario;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getStatusBadge = (usuario: Usuario) => {
    if (usuario.bloqueado) {
      return <Badge variant="destructive">Bloqueado</Badge>;
    }
    if (!usuario.ativo) {
      return <Badge variant="secondary">Inativo</Badge>;
    }
    return <Badge variant="default" className="bg-green-100 text-green-800">Ativo</Badge>;
  };

  const handleToggleStatus = async (id: number, ativo: boolean) => {
    try {
      const response = await fetch(`/api/usuarios/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ativo: !ativo }),
      });

      if (response.ok) {
        fetchUsuarios();
      }
    } catch (error) {
      console.error('Erro ao alterar status:', error);
    }
  };

  const handleToggleBlock = async (id: number, bloqueado: boolean) => {
    try {
      const response = await fetch(`/api/usuarios/${id}/block`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bloqueado: !bloqueado }),
      });

      if (response.ok) {
        fetchUsuarios();
      }
    } catch (error) {
      console.error('Erro ao bloquear/desbloquear usuário:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50 border-b">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Usuário
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Função
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Último Login
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Tentativas
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ações
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {usuarios.map((usuario) => (
            <tr key={usuario.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={`/avatars/${usuario.id}.jpg`} />
                    <AvatarFallback>
                      {usuario.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">{usuario.nome}</div>
                    <div className="text-sm text-gray-500">{usuario.email}</div>
                    <div className="text-xs text-gray-400">@{usuario.usuario}</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getRoleBadge(usuario.nivelAcesso)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getStatusBadge(usuario)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {usuario.ultimoLogin ? (
                  <div>
                    <div>{new Date(usuario.ultimoLogin).toLocaleDateString('pt-BR')}</div>
                    <div className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(usuario.ultimoLogin), { 
                        addSuffix: true, 
                        locale: ptBR 
                      })}
                    </div>
                  </div>
                ) : (
                  <span className="text-gray-400">Nunca</span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`text-sm ${usuario.tentativasLogin > 0 ? 'text-red-600' : 'text-gray-500'}`}>
                  {usuario.tentativasLogin}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/usuarios/${usuario.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        Visualizar
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/usuarios/${usuario.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/usuarios/${usuario.id}/sessoes`}>
                        <Key className="mr-2 h-4 w-4" />
                        Sessões
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleToggleStatus(usuario.id, usuario.ativo)}
                    >
                      {usuario.ativo ? (
                        <>
                          <Lock className="mr-2 h-4 w-4" />
                          Desativar
                        </>
                      ) : (
                        <>
                          <Unlock className="mr-2 h-4 w-4" />
                          Ativar
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleToggleBlock(usuario.id, usuario.bloqueado)}
                      className={usuario.bloqueado ? 'text-green-600' : 'text-red-600'}
                    >
                      {usuario.bloqueado ? (
                        <>
                          <Unlock className="mr-2 h-4 w-4" />
                          Desbloquear
                        </>
                      ) : (
                        <>
                          <Lock className="mr-2 h-4 w-4" />
                          Bloquear
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Excluir
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {usuarios.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum usuário encontrado</h3>
          <p className="mt-1 text-sm text-gray-500">
            Comece criando um novo usuário.
          </p>
          <div className="mt-6">
            <Button asChild>
              <Link href="/usuarios/novo">
                <Plus className="mr-2 h-4 w-4" />
                Novo Usuário
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
```

### ➕ Formulário de Usuário (user-form.tsx)
```typescript
// app/(dashboard)/usuarios/components/user-form.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Save, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

const userSchema = z.object({
  usuario: z.string().min(3, 'Usuário deve ter pelo menos 3 caracteres'),
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  senha: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres').optional(),
  nivelAcesso: z.enum(['admin', 'usuario', 'readonly', 'status']),
  ativo: z.boolean(),
  forcarTrocaSenha: z.boolean(),
});

type UserFormData = z.infer<typeof userSchema>;

interface UserFormProps {
  initialData?: Partial<UserFormData>;
  isEditing?: boolean;
  userId?: number;
}

export function UserForm({ initialData, isEditing = false, userId }: UserFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty },
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      usuario: '',
      nome: '',
      email: '',
      senha: '',
      nivelAcesso: 'usuario',
      ativo: true,
      forcarTrocaSenha: true,
      ...initialData,
    },
  });

  const ativo = watch('ativo');
  const forcarTrocaSenha = watch('forcarTrocaSenha');

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setValue('senha', password);
  };

  const onSubmit = async (data: UserFormData) => {
    setIsLoading(true);
    setMessage(null);

    try {
      const url = isEditing ? `/api/usuarios/${userId}` : '/api/usuarios';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao salvar usuário');
      }

      setMessage({ 
        type: 'success', 
        text: isEditing ? 'Usuário atualizado com sucesso!' : 'Usuário criado com sucesso!' 
      });

      if (!isEditing) {
        setTimeout(() => {
          router.push('/usuarios');
        }, 2000);
      }
    } catch (error) {
      console.error('Erro ao salvar usuário:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Erro ao salvar usuário' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Message Alert */}
      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Dados Básicos */}
      <Card>
        <CardHeader>
          <CardTitle>Dados Básicos</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="usuario">Nome de Usuário</Label>
              <Input
                id="usuario"
                {...register('usuario')}
                placeholder="usuario.sistema"
              />
              {errors.usuario && (
                <p className="text-sm text-red-600 mt-1">{errors.usuario.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="nome">Nome Completo</Label>
              <Input
                id="nome"
                {...register('nome')}
                placeholder="Nome completo do usuário"
              />
              {errors.nome && (
                <p className="text-sm text-red-600 mt-1">{errors.nome.message}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Senha */}
      <Card>
        <CardHeader>
          <CardTitle>Senha</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="senha">
              {isEditing ? 'Nova Senha (deixe em branco para manter a atual)' : 'Senha'}
            </Label>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Input
                  id="senha"
                  type={showPassword ? 'text' : 'password'}
                  {...register('senha')}
                  placeholder="Digite a senha"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={generatePassword}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Gerar
              </Button>
            </div>
            {errors.senha && (
              <p className="text-sm text-red-600 mt-1">{errors.senha.message}</p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="forcarTrocaSenha">Forçar Troca de Senha</Label>
              <p className="text-sm text-gray-600">Usuário deve trocar a senha no próximo login</p>
            </div>
            <Switch
              id="forcarTrocaSenha"
              checked={forcarTrocaSenha}
              onCheckedChange={(checked) => setValue('forcarTrocaSenha', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Permissões */}
      <Card>
        <CardHeader>
          <CardTitle>Permissões e Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="nivelAcesso">Nível de Acesso</Label>
            <Select onValueChange={(value) => setValue('nivelAcesso', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o nível de acesso" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Administrador - Acesso total</SelectItem>
                <SelectItem value="usuario">Usuário - Acesso limitado</SelectItem>
                <SelectItem value="readonly">Somente Leitura - Apenas visualização</SelectItem>
                <SelectItem value="status">Status - Apenas dashboard</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="ativo">Usuário Ativo</Label>
              <p className="text-sm text-gray-600">Usuário pode fazer login no sistema</p>
            </div>
            <Switch
              id="ativo"
              checked={ativo}
              onCheckedChange={(checked) => setValue('ativo', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Botões */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancelar
        </Button>
        <Button type="submit" disabled={!isDirty || isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          {isEditing ? 'Atualizar' : 'Criar'} Usuário
        </Button>
      </div>
    </form>
  );
}
```

## 🔌 API Routes

### 👥 API Principal de Usuários (route.ts)
```typescript
// app/api/usuarios/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { hash } from 'bcryptjs';
import { userSchema } from '@/lib/validations/usuarios';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 20;

    const where: any = {};

    if (search) {
      where.OR = [
        { nome: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { usuario: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role) {
      where.nivelAcesso = role;
    }

    if (status === 'ativo') {
      where.ativo = true;
      where.bloqueado = false;
    } else if (status === 'inativo') {
      where.ativo = false;
    } else if (status === 'bloqueado') {
      where.bloqueado = true;
    }

    const [usuarios, total] = await Promise.all([
      prisma.usuario.findMany({
        where,
        select: {
          id: true,
          usuario: true,
          nome: true,
          email: true,
          nivelAcesso: true,
          ultimoLogin: true,
          tentativasLogin: true,
          ativo: true,
          bloqueado: true,
          criadoEm: true,
        },
        orderBy: { nome: 'asc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.usuario.count({ where }),
    ]);

    return NextResponse.json({
      usuarios,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error('Erro ao buscar usuários:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = userSchema.parse(body);

    // Verificar se usuário já existe
    const existingUser = await prisma.usuario.findFirst({
      where: {
        OR: [
          { usuario: validatedData.usuario },
          { email: validatedData.email },
        ],
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Usuário ou email já existe' },
        { status: 400 }
      );
    }

    // Hash da senha
    const senhaHash = await hash(validatedData.senha!, 12);

    // Criar usuário
    const novoUsuario = await prisma.usuario.create({
      data: {
        usuario: validatedData.usuario,
        nome: validatedData.nome,
        email: validatedData.email,
        senhaHash,
        nivelAcesso: validatedData.nivelAcesso,
        ativo: validatedData.ativo,
        forcarTrocaSenha: validatedData.forcarTrocaSenha,
        criadoPor: parseInt(session.user.id),
      },
      select: {
        id: true,
        usuario: true,
        nome: true,
        email: true,
        nivelAcesso: true,
        ativo: true,
      },
    });

    // Log da criação
    await prisma.logAuditoria.create({
      data: {
        acao: 'usuario_criado',
        usuarioId: parseInt(session.user.id),
        detalhes: JSON.stringify({
          usuarioCriado: novoUsuario.id,
          nome: novoUsuario.nome,
          nivelAcesso: novoUsuario.nivelAcesso,
        }),
        timestamp: new Date(),
      },
    });

    return NextResponse.json(novoUsuario, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Schema do Banco de Dados

### 👤 Modelo Prisma
```prisma
model Usuario {
  id                Int       @id @default(autoincrement())
  usuario           String    @unique
  senhaHash         String    @map("senha_hash")
  nome              String
  email             String    @unique
  nivelAcesso       String    @map("nivel_acesso") // 'admin', 'usuario', 'readonly', 'status'
  ultimoLogin       DateTime? @map("ultimo_login")
  tentativasLogin   Int       @default(0) @map("tentativas_login")
  ativo             Boolean   @default(true)
  bloqueado         Boolean   @default(false)
  forcarTrocaSenha  Boolean   @default(true) @map("forcar_troca_senha")
  criadoEm          DateTime  @default(now()) @map("criado_em")
  atualizadoEm      DateTime  @updatedAt @map("atualizado_em")
  criadoPor         Int       @map("criado_por")

  // Relacionamentos
  criador           Usuario   @relation("UsuarioCriador", fields: [criadoPor], references: [id])
  usuariosCriados   Usuario[] @relation("UsuarioCriador")
  
  // Logs e auditoria
  logsAuditoria     LogAuditoria[]
  sessoes           SessaoUsuario[]

  @@map("usuarios")
}

model SessaoUsuario {
  id          String   @id @default(cuid())
  usuarioId   Int      @map("usuario_id")
  token       String   @unique
  ipAddress   String   @map("ip_address")
  userAgent   String   @map("user_agent")
  criadoEm    DateTime @default(now()) @map("criado_em")
  expiraEm    DateTime @map("expira_em")
  ativo       Boolean  @default(true)

  // Relacionamentos
  usuario     Usuario  @relation(fields: [usuarioId], references: [id], onDelete: Cascade)

  @@map("sessoes_usuario")
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Lista de usuários com filtros
- [ ] Criar novo usuário
- [ ] Editar usuário existente
- [ ] Controle de permissões
- [ ] Bloqueio/desbloqueio
- [ ] Reset de senha
- [ ] Gestão de sessões
- [ ] Logs de auditoria

### 🔒 Segurança
- [ ] Hash seguro de senhas
- [ ] Controle de tentativas de login
- [ ] Força troca de senha
- [ ] Logs de todas as ações
- [ ] Validação de permissões

## 🚀 Próximos Passos
1. **Configurações de Biometria** - Parâmetros biométricos
2. **Relatórios de Acesso** - Relatórios de usuários
3. **Backup de Usuários** - Backup e restore
