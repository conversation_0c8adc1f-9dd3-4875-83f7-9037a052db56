import { <PERSON>ada<PERSON> } from 'next';
import { Suspense } from 'react';
import { ReportForm } from '@/components/relatorios/report-form';
import { User, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Relatório Individual - RLPONTO',
  description: 'Gerar relatório individual de funcionário',
};

export default function RelatorioFuncionarioPage() {
  const reportConfig = {
    type: 'funcionario',
    title: 'Relatório Individual',
    description: 'Gere um relatório detalhado de um funcionário específico',
    color: 'blue',
    fields: [
      {
        name: 'funcionario',
        label: 'Funcionário',
        type: 'select' as const,
        required: true,
        placeholder: 'Selecione um funcionário',
        options: [
          { value: 'EMP001', label: '<PERSON> - EMP001' },
          { value: 'EMP002', label: '<PERSON> - EMP002' },
          { value: 'EMP003', label: '<PERSON> - EMP003' },
          { value: 'EMP004', label: '<PERSON> - <PERSON>MP004' },
          { value: 'EMP005', label: '<PERSON> - EMP005' },
          { value: 'E<PERSON>006', label: 'Juliana Ferreira Alves - EMP006' },
          { value: 'EMP007', label: 'Roberto Carlos Mendes - EMP007' },
          { value: 'EMP008', label: 'Fernanda Lima Santos - EMP008' }
        ]
      },
      {
        name: 'periodo',
        label: 'Período',
        type: 'daterange' as const,
        required: true,
        defaultValue: {
          start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          end: new Date()
        }
      },
      {
        name: 'incluirGraficos',
        label: 'Incluir Gráficos',
        type: 'checkbox' as const,
        defaultValue: true,
        description: 'Adicionar gráficos de frequência e horas trabalhadas'
      },
      {
        name: 'incluirDetalhes',
        label: 'Incluir Detalhes Diários',
        type: 'checkbox' as const,
        defaultValue: false,
        description: 'Mostrar registros detalhados dia a dia'
      },
      {
        name: 'incluirHorasExtras',
        label: 'Incluir Horas Extras',
        type: 'checkbox' as const,
        defaultValue: true,
        description: 'Adicionar seção de horas extras'
      },
      {
        name: 'formato',
        label: 'Formato de Saída',
        type: 'radio' as const,
        required: true,
        defaultValue: 'PDF',
        options: [
          { value: 'PDF', label: 'PDF', description: 'Melhor para visualização e impressão' },
          { value: 'Excel', label: 'Excel', description: 'Melhor para análise de dados' },
          { value: 'CSV', label: 'CSV', description: 'Para importação em outros sistemas' }
        ]
      }
    ],
    sections: [
      {
        title: 'Dados Básicos',
        description: 'Informações pessoais e profissionais do funcionário',
        included: true
      },
      {
        title: 'Resumo do Período',
        description: 'Totalizadores de horas, frequência e estatísticas',
        included: true
      },
      {
        title: 'Registros de Ponto',
        description: 'Histórico detalhado de entradas e saídas',
        included: true
      },
      {
        title: 'Análise de Frequência',
        description: 'Gráficos e análises de presença',
        included: true,
        optional: true
      },
      {
        title: 'Horas Extras',
        description: 'Detalhamento de horas extras trabalhadas',
        included: true,
        optional: true
      },
      {
        title: 'Observações',
        description: 'Registros manuais e justificativas',
        included: false,
        optional: true
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/relatorios">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <User className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Relatório Individual</h1>
                  <p className="text-gray-600">Configure e gere relatório de funcionário específico</p>
                </div>
              </div>
            </div>
          </div>

          {/* Formulário */}
          <div className="bg-white rounded-lg shadow">
            <Suspense fallback={<FormSkeleton />}>
              <ReportForm config={reportConfig} />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}

function FormSkeleton() {
  return (
    <div className="p-6 space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/4" />
            <div className="h-10 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded w-1/4" />
        <div className="h-24 bg-gray-200 rounded" />
      </div>
      <div className="h-10 bg-gray-200 rounded w-32" />
    </div>
  );
}
