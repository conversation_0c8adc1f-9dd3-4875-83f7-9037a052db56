'use client';

import { useState, useEffect } from 'react';
import { 
  AlertTriangle, 
  Clock, 
  User, 
  Calendar,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';

interface Alert {
  id: string;
  tipo: 'inconsistencia' | 'atraso' | 'ausencia' | 'horas_extras' | 'aprovacao';
  severidade: 'baixa' | 'media' | 'alta';
  funcionario: {
    nome: string;
    matricula: string;
  };
  titulo: string;
  descricao: string;
  data: string;
  status: 'pendente' | 'resolvido' | 'ignorado';
  criadoEm: Date;
}

interface AlertsPanelProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function AlertsPanel({ period }: AlertsPanelProps) {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'todos' | 'pendente' | 'resolvido'>('todos');

  useEffect(() => {
    fetchAlerts();
  }, [period]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      
      // Simulação de dados
      const mockAlerts: Alert[] = [
        {
          id: '1',
          tipo: 'inconsistencia',
          severidade: 'alta',
          funcionario: {
            nome: 'João Silva Santos',
            matricula: 'EMP001'
          },
          titulo: 'Registro de saída sem entrada',
          descricao: 'Funcionário registrou saída às 17:30 sem ter registrado entrada no dia 15/01',
          data: '15/01/2024',
          status: 'pendente',
          criadoEm: new Date()
        },
        {
          id: '2',
          tipo: 'horas_extras',
          severidade: 'media',
          funcionario: {
            nome: 'Maria Oliveira Costa',
            matricula: 'EMP002'
          },
          titulo: 'Excesso de horas extras',
          descricao: 'Funcionário acumulou 25h extras no período, acima do limite de 20h',
          data: '20/01/2024',
          status: 'pendente',
          criadoEm: new Date()
        },
        {
          id: '3',
          tipo: 'aprovacao',
          severidade: 'baixa',
          funcionario: {
            nome: 'Carlos Roberto Lima',
            matricula: 'EMP003'
          },
          titulo: 'Ponto manual pendente',
          descricao: 'Registro manual de entrada aguardando aprovação há 2 dias',
          data: '18/01/2024',
          status: 'pendente',
          criadoEm: new Date()
        },
        {
          id: '4',
          tipo: 'atraso',
          severidade: 'baixa',
          funcionario: {
            nome: 'Ana Paula Silva',
            matricula: 'EMP004'
          },
          titulo: 'Atrasos recorrentes',
          descricao: 'Funcionário apresentou 5 atrasos nos últimos 10 dias',
          data: '22/01/2024',
          status: 'resolvido',
          criadoEm: new Date()
        }
      ];

      await new Promise(resolve => setTimeout(resolve, 800));
      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Erro ao buscar alertas:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severidade: string) => {
    switch (severidade) {
      case 'alta':
        return 'text-red-600 bg-red-100';
      case 'media':
        return 'text-yellow-600 bg-yellow-100';
      case 'baixa':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityIcon = (severidade: string) => {
    switch (severidade) {
      case 'alta':
        return <XCircle className="h-4 w-4" />;
      case 'media':
        return <AlertTriangle className="h-4 w-4" />;
      case 'baixa':
        return <Info className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'inconsistencia':
        return <AlertTriangle className="h-4 w-4" />;
      case 'atraso':
        return <Clock className="h-4 w-4" />;
      case 'ausencia':
        return <User className="h-4 w-4" />;
      case 'horas_extras':
        return <Clock className="h-4 w-4" />;
      case 'aprovacao':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    if (filter === 'todos') return true;
    return alert.status === filter;
  });

  const alertCounts = {
    total: alerts.length,
    pendente: alerts.filter(a => a.status === 'pendente').length,
    resolvido: alerts.filter(a => a.status === 'resolvido').length,
    alta: alerts.filter(a => a.severidade === 'alta').length
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-start space-x-3 p-3 border rounded animate-pulse">
            <div className="w-5 h-5 bg-gray-200 rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="w-3/4 h-4 bg-gray-200 rounded" />
              <div className="w-1/2 h-3 bg-gray-200 rounded" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Resumo */}
      <div className="grid grid-cols-2 gap-3">
        <div className="text-center p-3 bg-red-50 rounded-lg">
          <div className="text-lg font-bold text-red-700">{alertCounts.pendente}</div>
          <div className="text-xs text-red-600">Pendentes</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-lg font-bold text-green-700">{alertCounts.resolvido}</div>
          <div className="text-xs text-green-600">Resolvidos</div>
        </div>
      </div>

      {/* Filtros */}
      <div className="flex space-x-2">
        {['todos', 'pendente', 'resolvido'].map((filterOption) => (
          <button
            key={filterOption}
            onClick={() => setFilter(filterOption as 'todos' | 'pendente' | 'resolvido')}
            className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
              filter === filterOption
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
          </button>
        ))}
      </div>

      {/* Lista de Alertas */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {filteredAlerts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <CheckCircle className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Nenhum alerta encontrado</p>
          </div>
        ) : (
          filteredAlerts.map((alert) => (
            <div
              key={alert.id}
              className={`p-3 border rounded-lg transition-colors ${
                alert.status === 'pendente' 
                  ? 'border-red-200 bg-red-50' 
                  : alert.status === 'resolvido'
                  ? 'border-green-200 bg-green-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-start space-x-3">
                {/* Ícone do tipo */}
                <div className={`p-1 rounded ${getSeverityColor(alert.severidade)}`}>
                  {getSeverityIcon(alert.severidade)}
                </div>

                <div className="flex-1 min-w-0">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {alert.titulo}
                    </h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      alert.status === 'pendente' 
                        ? 'bg-red-100 text-red-800'
                        : alert.status === 'resolvido'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {alert.status === 'pendente' ? 'Pendente' : 
                       alert.status === 'resolvido' ? 'Resolvido' : 'Ignorado'}
                    </span>
                  </div>

                  {/* Descrição */}
                  <p className="text-xs text-gray-600 mb-2">
                    {alert.descricao}
                  </p>

                  {/* Footer */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-2">
                      <User className="h-3 w-3" />
                      <span>{alert.funcionario.nome}</span>
                      <span>({alert.funcionario.matricula})</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{alert.data}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Ação */}
      {alertCounts.pendente > 0 && (
        <div className="pt-3 border-t border-gray-200">
          <button className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium">
            Ver todos os alertas ({alertCounts.total})
          </button>
        </div>
      )}
    </div>
  );
}
