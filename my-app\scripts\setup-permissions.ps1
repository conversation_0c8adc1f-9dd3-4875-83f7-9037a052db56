# PowerShell script para configurar permissões dos scripts no Windows

Write-Host "🔧 Configurando permissões dos scripts..." -ForegroundColor Blue

# Verificar se estamos no diretório correto
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Execute este script a partir do diretório raiz do projeto" -ForegroundColor Red
    exit 1
}

# Lista de scripts para configurar
$scripts = @(
    "scripts/setup-complete.sh",
    "scripts/setup-production.sh", 
    "scripts/configure-server.sh",
    "scripts/deploy.sh",
    "scripts/backup-and-monitor.sh"
)

Write-Host "Configurando permissões para scripts bash..." -ForegroundColor Green

foreach ($script in $scripts) {
    if (Test-Path $script) {
        # No Windows, vamos apenas verificar se o arquivo existe
        Write-Host "✅ $script - OK" -ForegroundColor Green
    } else {
        Write-Host "❌ $script - Não encontrado" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📋 Scripts configurados:" -ForegroundColor Blue
Write-Host "  setup-complete.sh    - Script principal (executa tudo)"
Write-Host "  setup-production.sh  - Configura SSH"
Write-Host "  configure-server.sh  - Configura servidor"
Write-Host "  deploy.sh           - Deploy da aplicação"
Write-Host "  backup-and-monitor.sh - Backup e monitoramento"
Write-Host ""
Write-Host "🚀 Para executar o setup completo:" -ForegroundColor Yellow
Write-Host "  bash scripts/setup-complete.sh"
Write-Host ""
Write-Host "💡 Certifique-se de ter:" -ForegroundColor Cyan
Write-Host "  - Git Bash ou WSL instalado"
Write-Host "  - SSH client disponível"
Write-Host "  - Acesso ao servidor ************"
Write-Host ""
Write-Host "✅ Configuração de permissões concluída!" -ForegroundColor Green
