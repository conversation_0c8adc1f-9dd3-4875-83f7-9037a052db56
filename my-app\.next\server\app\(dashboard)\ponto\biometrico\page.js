(()=>{var a={};a.id=509,a.ids=[509],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11302:(a,b,c)=>{"use strict";c.d(b,{PontoStatus:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call PontoStatus() from the server but PontoStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\ponto-status.tsx","PontoStatus")},13137:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,72294)),Promise.resolve().then(c.bind(c,73822)),Promise.resolve().then(c.bind(c,11302)),Promise.resolve().then(c.bind(c,58570))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24828:(a,b,c)=>{"use strict";c.d(b,{PontoStatus:()=>j});var d=c(60687),e=c(43210),f=c(5336),g=c(48730),h=c(93613),i=c(58869);function j(){let[a,b]=(0,e.useState)(null),[c,j]=(0,e.useState)(!0),[k,l]=(0,e.useState)(new Date);return c?(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"})]})]})}):a?(0,d.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.A,{className:"h-6 w-6 text-blue-600"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Status do Ponto"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:k.toLocaleDateString("pt-BR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:k.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Hor\xe1rio atual"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Funcion\xe1rio"}),(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:a.funcionario.nome}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[a.funcionario.matricula," • ",a.funcionario.cargo]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Horas Trabalhadas"}),(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:a.horasTrabalhadas}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Hoje"})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Saldo de Horas"}),(0,d.jsx)("div",{className:`text-lg font-semibold ${a.saldoHoras.startsWith("+")?"text-green-600":"text-red-600"}`,children:a.saldoHoras}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Acumulado"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)("div",{className:"flex items-center space-x-3",children:(0,d.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-1 rounded-full border ${(a=>{switch(a){case"entrada":return"bg-green-100 text-green-800 border-green-200";case"saida":return"bg-blue-100 text-blue-800 border-blue-200";case"intervalo_inicio":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"intervalo_fim":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(a.statusAtual)}`,children:[(a=>{switch(a){case"entrada":case"saida":return(0,d.jsx)(f.A,{className:"h-5 w-5"});case"intervalo_inicio":case"intervalo_fim":return(0,d.jsx)(g.A,{className:"h-5 w-5"});default:return(0,d.jsx)(h.A,{className:"h-5 w-5"})}})(a.statusAtual),(0,d.jsxs)("span",{className:"text-sm font-medium",children:["Pr\xf3ximo: ",a.proximoRegistro]})]})}),a.ultimoRegistro&&(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["\xdaltimo: ",a.ultimoRegistro.tipo]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[a.ultimoRegistro.horario," - ",a.ultimoRegistro.data]})]})]})]})}):(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"text-center text-gray-500",children:[(0,d.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4"}),(0,d.jsx)("p",{children:"Erro ao carregar status do ponto"})]})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34982:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["ponto",{children:["biometrico",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,51392)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\biometrico\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\biometrico\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/ponto/biometrico/page",pathname:"/ponto/biometrico",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/ponto/biometrico/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},40520:(a,b,c)=>{"use strict";c.d(b,{BiometricScanner:()=>o});var d=c(60687),e=c(43210),f=c(42613),g=c(94200),h=c(51361),i=c(41862),j=c(5336),k=c(35071),l=c(58869),m=c(48730),n=c(43649);function o({type:a}){let[b,c]=(0,e.useState)("idle"),[o,p]=(0,e.useState)(null),[q,r]=(0,e.useState)(0),s="fingerprint"===a,t=s?g.A:h.A,u=s?"Posicione seu dedo no leitor biom\xe9trico":"Posicione seu rosto na c\xe2mera",v=async()=>{c("scanning"),r(0),p(null);let a=setInterval(()=>{r(b=>b>=100?(clearInterval(a),100):b+10)},200);try{if(await new Promise(a=>setTimeout(a,2e3)),Date.now()%5!=0){let a={funcionario:{nome:"Jo\xe3o Silva Santos",matricula:"EMP001",cargo:"Analista de Sistemas"},tipoRegistro:"Entrada",horario:new Date().toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}),message:"Ponto registrado com sucesso!"};p(a),c("success"),await w(a)}else{let a=["not_found","error"][Date.now()%2];c(a),p({tipoRegistro:"",horario:"",message:"not_found"===a?"Biometria n\xe3o encontrada. Verifique o cadastro.":"Erro na leitura. Tente novamente."})}}catch(a){c("error"),p({tipoRegistro:"",horario:"",message:"Erro de conex\xe3o. Tente novamente."})}clearInterval(a),r(100)},w=async b=>{try{if(!(await fetch("/api/ponto/biometrico",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({funcionarioId:b.funcionario?.matricula,tipo:b.tipoRegistro.toLowerCase(),biometricType:a,timestamp:new Date().toISOString()})})).ok)throw Error("Erro ao registrar ponto");console.log("Ponto registrado com sucesso")}catch(a){console.error("Erro ao registrar ponto:",a)}},x=()=>{c("idle"),p(null),r(0)};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${(()=>{switch(b){case"scanning":return"border-blue-300 bg-blue-50";case"success":return"border-green-300 bg-green-50";case"error":case"not_found":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-white"}})()}`,children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(()=>{switch(b){case"scanning":return(0,d.jsx)(i.A,{className:"h-12 w-12 text-blue-600 animate-spin"});case"success":return(0,d.jsx)(j.A,{className:"h-12 w-12 text-green-600"});case"error":case"not_found":return(0,d.jsx)(k.A,{className:"h-12 w-12 text-red-600"});default:return(0,d.jsx)(t,{className:"h-12 w-12 text-gray-500"})}})(),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:(()=>{switch(b){case"scanning":return"Escaneando...";case"success":return o?.message||"Sucesso!";case"error":case"not_found":return o?.message||"Erro na leitura";default:return u}})()}),"scanning"===b&&(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-4",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${q}%`}})})]})]}),o&&"success"===b&&o.funcionario&&(0,d.jsxs)("div",{className:"mt-6 p-4 bg-white rounded-lg border border-green-200",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-green-600"}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:o.funcionario.nome}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[o.funcionario.matricula," • ",o.funcionario.cargo]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-gray-500"}),(0,d.jsx)("span",{className:"text-gray-600",children:"Tipo:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:o.tipoRegistro})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Hor\xe1rio:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:o.horario})]})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:["idle"===b&&(0,d.jsxs)(f.$n,{onClick:v,className:"flex-1",variant:"primary",children:[(0,d.jsx)(t,{className:"h-4 w-4 mr-2"}),"Iniciar ",s?"Biometria Digital":"Reconhecimento Facial"]}),"scanning"===b&&(0,d.jsx)(f.$n,{onClick:x,className:"flex-1",variant:"outline",children:"Cancelar"}),("success"===b||"error"===b||"not_found"===b)&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.$n,{onClick:x,className:"flex-1",variant:"outline",children:"Novo Scan"}),"success"===b&&(0,d.jsx)(f.$n,{onClick:v,className:"flex-1",variant:"primary",children:"Registrar Novamente"})]})]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,d.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Instru\xe7\xf5es:"}),(0,d.jsx)("ul",{className:"space-y-1 text-xs",children:s?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("li",{children:"• Posicione o dedo firmemente no sensor"}),(0,d.jsx)("li",{children:"• Mantenha o dedo im\xf3vel durante o scan"}),(0,d.jsx)("li",{children:"• Certifique-se de que o dedo esteja limpo"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("li",{children:"• Posicione o rosto centralizado na c\xe2mera"}),(0,d.jsx)("li",{children:"• Mantenha-se im\xf3vel durante o scan"}),(0,d.jsx)("li",{children:"• Certifique-se de ter boa ilumina\xe7\xe3o"})]})})]})]})})]})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51361:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},51392:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r,metadata:()=>q});var d=c(37413),e=c(61120),f=c(72294),g=c(11302),h=c(73822),i=c(51465),j=c(26373);let k=(0,j.A)("fingerprint",[["path",{d:"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4",key:"1nerag"}],["path",{d:"M14 13.12c0 2.38 0 6.38-1 8.88",key:"o46ks0"}],["path",{d:"M17.29 21.02c.12-.6.43-2.3.5-3.02",key:"ptglia"}],["path",{d:"M2 12a10 10 0 0 1 18-6",key:"ydlgp0"}],["path",{d:"M2 16h.01",key:"1gqxmh"}],["path",{d:"M21.8 16c.2-2 .131-5.354 0-6",key:"drycrb"}],["path",{d:"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2",key:"1tidbn"}],["path",{d:"M8.65 22c.21-.66.45-1.32.57-2",key:"13wd9y"}],["path",{d:"M9 6.8a6 6 0 0 1 9 5.2v2",key:"1fr1j5"}]]),l=(0,j.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var m=c(53148),n=c(75243),o=c(4536),p=c.n(o);let q={title:"Ponto Biom\xe9trico - RLPONTO",description:"Registro de ponto atrav\xe9s de biometria"};function r(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(p(),{href:"/dashboard",children:(0,d.jsxs)(n.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(k,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Ponto Biom\xe9trico"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Registre seu ponto usando biometria digital ou facial"})]})]})]})}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(s,{}),children:(0,d.jsx)(g.PontoStatus,{})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,d.jsx)(k,{className:"h-6 w-6 text-blue-600"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Biometria Digital"})]}),(0,d.jsx)(f.BiometricScanner,{type:"fingerprint"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,d.jsx)(l,{className:"h-6 w-6 text-green-600"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Reconhecimento Facial"})]}),(0,d.jsx)(f.BiometricScanner,{type:"facial"})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,d.jsx)(m.A,{className:"h-6 w-6 text-gray-600"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Registros Recentes"})]}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(t,{}),children:(0,d.jsx)(h.HistoricoRecente,{})})]})]})})})}function s(){return(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg animate-pulse"})})}function t(){return(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-1/3"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded animate-pulse"})]},b))})}},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},59315:(a,b,c)=>{"use strict";c.d(b,{HistoricoRecente:()=>o});var d=c(60687),e=c(43210),f=c(62688);let g=(0,f.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);var h=c(40083);let i=(0,f.A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);var j=c(48730),k=c(94200),l=c(51361),m=c(5336),n=c(93613);function o(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[o,p]=(0,e.useState)(!1);return c?(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg animate-pulse",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded"})]},b))}):0===a.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(j.A,{className:"h-12 w-12 text-gray-500 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum registro encontrado"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Os registros de ponto aparecer\xe3o aqui"})]}):(0,d.jsxs)("div",{className:"space-y-3",children:[a.map(a=>(0,d.jsxs)("div",{className:`flex items-center space-x-4 p-4 border rounded-lg transition-colors ${"sucesso"===a.status?"border-gray-200 hover:bg-gray-50":"border-red-200 bg-red-50"}`,children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center",children:(a=>{switch(a){case"entrada":return(0,d.jsx)(g,{className:"h-5 w-5 text-green-600"});case"saida":return(0,d.jsx)(h.A,{className:"h-5 w-5 text-red-600"});case"intervalo_inicio":case"intervalo_fim":return(0,d.jsx)(i,{className:"h-5 w-5 text-yellow-600"});default:return(0,d.jsx)(j.A,{className:"h-5 w-5 text-gray-600"})}})(a.tipo)})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:a.funcionario.nome}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["(",a.funcionario.matricula,")"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,d.jsx)("div",{className:"flex items-center space-x-1",children:(0,d.jsx)("span",{children:(a=>{switch(a){case"entrada":return"Entrada";case"saida":return"Sa\xedda";case"intervalo_inicio":return"In\xedcio Intervalo";case"intervalo_fim":return"Fim Intervalo";default:return a}})(a.tipo)})}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(a=>{switch(a){case"fingerprint":return(0,d.jsx)(k.A,{className:"h-4 w-4 text-blue-600"});case"facial":return(0,d.jsx)(l.A,{className:"h-4 w-4 text-green-600"});default:return(0,d.jsx)(j.A,{className:"h-4 w-4 text-gray-600"})}})(a.metodo),(0,d.jsx)("span",{children:(a=>{switch(a){case"fingerprint":return"Biometria Digital";case"facial":return"Reconhecimento Facial";case"manual":return"Manual";default:return a}})(a.metodo)})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(a=>{switch(a){case"sucesso":return(0,d.jsx)(m.A,{className:"h-4 w-4 text-green-600"});case"erro":return(0,d.jsx)(n.A,{className:"h-4 w-4 text-red-600"});default:return(0,d.jsx)(j.A,{className:"h-4 w-4 text-gray-600"})}})(a.status),(0,d.jsx)("span",{className:"sucesso"===a.status?"text-green-600":"text-red-600",children:"sucesso"===a.status?"Sucesso":"Erro"})]})]})]}),(0,d.jsxs)("div",{className:"flex-shrink-0 text-right",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.horario}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:a.data})]})]},a.id)),(0,d.jsx)("div",{className:"text-center pt-4",children:(0,d.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Ver hist\xf3rico completo"})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72294:(a,b,c)=>{"use strict";c.d(b,{BiometricScanner:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call BiometricScanner() from the server but BiometricScanner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\biometric-scanner.tsx","BiometricScanner")},73822:(a,b,c)=>{"use strict";c.d(b,{HistoricoRecente:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call HistoricoRecente() from the server but HistoricoRecente is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\historico-recente.tsx","HistoricoRecente")},78289:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,40520)),Promise.resolve().then(c.bind(c,59315)),Promise.resolve().then(c.bind(c,24828)),Promise.resolve().then(c.bind(c,98316))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94200:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("fingerprint",[["path",{d:"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4",key:"1nerag"}],["path",{d:"M14 13.12c0 2.38 0 6.38-1 8.88",key:"o46ks0"}],["path",{d:"M17.29 21.02c.12-.6.43-2.3.5-3.02",key:"ptglia"}],["path",{d:"M2 12a10 10 0 0 1 18-6",key:"ydlgp0"}],["path",{d:"M2 16h.01",key:"1gqxmh"}],["path",{d:"M21.8 16c.2-2 .131-5.354 0-6",key:"drycrb"}],["path",{d:"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2",key:"1tidbn"}],["path",{d:"M8.65 22c.21-.66.45-1.32.57-2",key:"13wd9y"}],["path",{d:"M9 6.8a6 6 0 0 1 9 5.2v2",key:"1fr1j5"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,327,556,40,121],()=>b(b.s=34982));module.exports=c})();