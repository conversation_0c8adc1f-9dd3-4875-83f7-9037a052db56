import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Rotas que requerem autenticação
const protectedRoutes = [
  '/dashboard',
  '/funcionarios',
  '/ponto',
  '/relatorios',
  '/administracao',
  '/estatisticas',
  '/periodo-apuracao'
];

// Rotas públicas (não requerem autenticação)
const publicRoutes = [
  '/login',
  '/api/auth',
  '/api/auth/signin',
  '/api/auth/callback',
  '/api/auth/session'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Permitir acesso a arquivos estáticos e API routes do NextAuth
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api/auth') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    publicRoutes.some(route => pathname.startsWith(route))
  ) {
    return NextResponse.next();
  }

  // Verificar se é uma rota protegida
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  );

  if (isProtectedRoute) {
    // Verificar token JWT do NextAuth
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    console.log('Middleware - Rota protegida:', pathname);
    console.log('Middleware - Token encontrado:', !!token);

    if (!token) {
      // Redirecionar para login se não estiver autenticado
      console.log('Middleware - Redirecionando para login');
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }

    console.log('Middleware - Usuário autenticado:', token.usuario);
  }

  // Redirecionar root para dashboard se autenticado, senão para login
  if (pathname === '/') {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    if (token) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } else {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
