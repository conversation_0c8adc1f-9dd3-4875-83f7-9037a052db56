import { NextRequest, NextResponse } from 'next/server';

interface RegistroManualRequest {
  funcionarioId: string;
  tipo: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim';
  timestamp: string;
  justificativa: string;
  localizacao?: {
    latitude: number;
    longitude: number;
    endereco: string;
  };
  foto?: string;
}

interface RegistroManual {
  id: string;
  funcionarioId: string;
  tipo: string;
  horario: string;
  data: string;
  timestamp: Date;
  justificativa: string;
  localizacao?: {
    latitude: number;
    longitude: number;
    endereco: string;
  };
  foto?: string;
  status: 'pendente' | 'aprovado' | 'rejeitado';
  aprovadoPor?: string;
  dataAprovacao?: Date;
  observacaoAprovacao?: string;
  criadoEm: Date;
}

// Simulação de banco de dados em memória
const registrosManuais: RegistroManual[] = [
  {
    id: '1',
    funcionarioId: 'EMP001',
    tipo: 'entrada',
    horario: '08:15:00',
    data: new Date().toLocaleDateString('pt-BR'),
    timestamp: new Date(),
    justificativa: 'Problema no leitor biométrico da entrada principal',
    status: 'aprovado',
    aprovadoPor: '<EMAIL>',
    dataAprovacao: new Date(),
    criadoEm: new Date()
  },
  {
    id: '2',
    funcionarioId: 'EMP003',
    tipo: 'saida',
    horario: '17:45:00',
    data: new Date(Date.now() - 86400000).toLocaleDateString('pt-BR'),
    timestamp: new Date(Date.now() - 86400000),
    justificativa: 'Trabalho externo - visita a cliente',
    localizacao: {
      latitude: -23.5505,
      longitude: -46.6333,
      endereco: 'São Paulo, SP'
    },
    status: 'pendente',
    criadoEm: new Date(Date.now() - 86400000)
  }
];

export async function POST(request: NextRequest) {
  try {
    const body: RegistroManualRequest = await request.json();
    
    // Validar dados de entrada
    if (!body.funcionarioId || !body.tipo || !body.timestamp || !body.justificativa?.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Dados obrigatórios não fornecidos (funcionário, tipo, horário e justificativa)',
        },
        { status: 400 }
      );
    }

    // Validar funcionário
    const funcionariosValidos = ['EMP001', 'EMP002', 'EMP003', 'EMP004', 'EMP005', 'EMP006', 'EMP007', 'EMP008'];
    if (!funcionariosValidos.includes(body.funcionarioId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Funcionário não encontrado',
        },
        { status: 404 }
      );
    }

    // Validar justificativa (mínimo 10 caracteres)
    if (body.justificativa.trim().length < 10) {
      return NextResponse.json(
        {
          success: false,
          error: 'Justificativa deve ter pelo menos 10 caracteres',
        },
        { status: 400 }
      );
    }

    // Validar horário (não pode ser futuro)
    const timestamp = new Date(body.timestamp);
    const agora = new Date();
    
    if (timestamp > agora) {
      return NextResponse.json(
        {
          success: false,
          error: 'Não é possível registrar ponto no futuro',
        },
        { status: 400 }
      );
    }

    // Validar se não há registro muito antigo (máximo 7 dias)
    const seteDiasAtras = new Date(agora.getTime() - 7 * 24 * 60 * 60 * 1000);
    if (timestamp < seteDiasAtras) {
      return NextResponse.json(
        {
          success: false,
          error: 'Não é possível registrar ponto com mais de 7 dias de atraso',
        },
        { status: 400 }
      );
    }

    // Verificar se já existe registro manual pendente para o mesmo funcionário no mesmo dia
    const dataRegistro = timestamp.toLocaleDateString('pt-BR');
    const registroExistente = registrosManuais.find(
      r => r.funcionarioId === body.funcionarioId && 
           r.data === dataRegistro && 
           r.tipo === body.tipo &&
           r.status === 'pendente'
    );

    if (registroExistente) {
      return NextResponse.json(
        {
          success: false,
          error: `Já existe um registro manual pendente para ${body.tipo} nesta data`,
        },
        { status: 409 }
      );
    }

    // Criar novo registro
    const novoRegistro: RegistroManual = {
      id: Date.now().toString(),
      funcionarioId: body.funcionarioId,
      tipo: body.tipo,
      horario: timestamp.toLocaleTimeString('pt-BR'),
      data: dataRegistro,
      timestamp,
      justificativa: body.justificativa.trim(),
      localizacao: body.localizacao,
      foto: body.foto,
      status: 'pendente',
      criadoEm: new Date()
    };

    // Salvar registro
    registrosManuais.push(novoRegistro);

    // Buscar dados do funcionário
    const funcionario = getFuncionarioById(body.funcionarioId);

    // Simular envio de notificação para supervisor
    await enviarNotificacaoSupervisor(novoRegistro, funcionario || { nome: 'Funcionário' });

    return NextResponse.json({
      success: true,
      message: 'Registro manual enviado para aprovação',
      registro: {
        id: novoRegistro.id,
        funcionario,
        tipo: getTipoLabel(body.tipo),
        horario: novoRegistro.horario,
        data: novoRegistro.data,
        justificativa: novoRegistro.justificativa,
        status: 'Pendente de Aprovação',
        localizacao: novoRegistro.localizacao
      }
    });

  } catch (error) {
    console.error('Erro ao registrar ponto manual:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const funcionarioId = searchParams.get('funcionarioId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');

    let registrosFiltrados = [...registrosManuais];

    // Filtrar por funcionário se especificado
    if (funcionarioId) {
      registrosFiltrados = registrosFiltrados.filter(
        r => r.funcionarioId === funcionarioId
      );
    }

    // Filtrar por status se especificado
    if (status) {
      registrosFiltrados = registrosFiltrados.filter(
        r => r.status === status
      );
    }

    // Ordenar por data de criação (mais recente primeiro)
    registrosFiltrados.sort((a, b) => b.criadoEm.getTime() - a.criadoEm.getTime());

    // Limitar resultados
    registrosFiltrados = registrosFiltrados.slice(0, limit);

    // Enriquecer com dados do funcionário
    const registrosEnriquecidos = registrosFiltrados.map(registro => ({
      id: registro.id,
      funcionario: getFuncionarioById(registro.funcionarioId),
      tipo: registro.tipo,
      tipoLabel: getTipoLabel(registro.tipo),
      horario: registro.horario,
      data: registro.data,
      justificativa: registro.justificativa,
      status: registro.status,
      statusLabel: getStatusLabel(registro.status),
      localizacao: registro.localizacao,
      aprovadoPor: registro.aprovadoPor,
      dataAprovacao: registro.dataAprovacao,
      observacaoAprovacao: registro.observacaoAprovacao,
      criadoEm: registro.criadoEm
    }));

    return NextResponse.json({
      success: true,
      registros: registrosEnriquecidos,
      total: registrosFiltrados.length
    });

  } catch (error) {
    console.error('Erro ao buscar registros manuais:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

// Funções auxiliares
function getFuncionarioById(id: string) {
  const funcionarios: Record<string, unknown> = {
    'EMP001': { nome: 'João Silva Santos', matricula: 'EMP001', cargo: 'Analista de Sistemas' },
    'EMP002': { nome: 'Maria Oliveira Costa', matricula: 'EMP002', cargo: 'Gerente de Vendas' },
    'EMP003': { nome: 'Carlos Roberto Lima', matricula: 'EMP003', cargo: 'Operador de Produção' },
    'EMP004': { nome: 'Ana Paula Silva', matricula: 'EMP004', cargo: 'Analista de RH' },
    'EMP005': { nome: 'Pedro Henrique Souza', matricula: 'EMP005', cargo: 'Assistente Administrativo' },
    'EMP006': { nome: 'Juliana Ferreira Alves', matricula: 'EMP006', cargo: 'Coordenadora de Marketing' },
    'EMP007': { nome: 'Roberto Carlos Mendes', matricula: 'EMP007', cargo: 'Técnico de Manutenção' },
    'EMP008': { nome: 'Fernanda Lima Santos', matricula: 'EMP008', cargo: 'Analista Financeiro' }
  };

  return funcionarios[id] || null;
}

function getTipoLabel(tipo: string) {
  const labels: Record<string, string> = {
    'entrada': 'Entrada',
    'saida': 'Saída',
    'intervalo_inicio': 'Início Intervalo',
    'intervalo_fim': 'Fim Intervalo'
  };

  return labels[tipo] || tipo;
}

function getStatusLabel(status: string) {
  const labels: Record<string, string> = {
    'pendente': 'Pendente de Aprovação',
    'aprovado': 'Aprovado',
    'rejeitado': 'Rejeitado'
  };

  return labels[status] || status;
}

async function enviarNotificacaoSupervisor(registro: RegistroManual, funcionario: { nome?: string }) {
  // Simular envio de notificação
  console.log(`Notificação enviada para supervisor: Registro manual de ${funcionario?.nome || 'Funcionário'} aguardando aprovação`);
  
  // Aqui seria implementada a lógica real de notificação:
  // - Email para supervisor
  // - Notificação push
  // - Webhook para sistema externo
  
  return true;
}
