#!/bin/bash

# 🚀 Script de Configuração do Ambiente de Produção - Sistema RLPONTO
# Configura container LXC Ubuntu 22.04 para deploy do sistema

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações do servidor
SERVER_IP="************"
SERVER_USER="root"
SSH_KEY_NAME="rl-ponto-next"
PROJECT_NAME="rlponto-system"

echo -e "${BLUE}🚀 Configurando Ambiente de Produção - Sistema RLPONTO${NC}"
echo -e "${BLUE}=================================================${NC}"

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# 1. Gerar chave SSH se não existir
log "1. Configurando chave SSH..."

if [ ! -f ~/.ssh/${SSH_KEY_NAME} ]; then
    log "Gerando nova chave SSH: ${SSH_KEY_NAME}"
    ssh-keygen -t ed25519 -f ~/.ssh/${SSH_KEY_NAME} -N "" -C "rlponto-deploy@$(hostname)"
    log "Chave SSH gerada com sucesso!"
else
    log "Chave SSH já existe: ~/.ssh/${SSH_KEY_NAME}"
fi

# 2. Configurar SSH config
log "2. Configurando SSH config..."

SSH_CONFIG_ENTRY="
# Sistema RLPONTO - Servidor de Produção
Host rlponto-prod
    HostName ${SERVER_IP}
    User ${SERVER_USER}
    IdentityFile ~/.ssh/${SSH_KEY_NAME}
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
"

# Verificar se entrada já existe
if ! grep -q "Host rlponto-prod" ~/.ssh/config 2>/dev/null; then
    echo "${SSH_CONFIG_ENTRY}" >> ~/.ssh/config
    log "Configuração SSH adicionada ao ~/.ssh/config"
else
    log "Configuração SSH já existe no ~/.ssh/config"
fi

# Definir permissões corretas
chmod 600 ~/.ssh/${SSH_KEY_NAME}
chmod 644 ~/.ssh/${SSH_KEY_NAME}.pub
chmod 600 ~/.ssh/config 2>/dev/null || touch ~/.ssh/config && chmod 600 ~/.ssh/config

log "3. Copiando chave pública para o servidor..."

# Copiar chave pública para o servidor (usando senha inicial)
echo -e "${YELLOW}Será solicitada a senha do servidor para configurar acesso SSH sem senha${NC}"
echo -e "${YELLOW}Senha: @Ric6109${NC}"

# Usar ssh-copy-id para copiar a chave
ssh-copy-id -i ~/.ssh/${SSH_KEY_NAME}.pub ${SERVER_USER}@${SERVER_IP} || {
    warning "ssh-copy-id falhou, tentando método manual..."
    
    # Método manual caso ssh-copy-id não funcione
    cat ~/.ssh/${SSH_KEY_NAME}.pub | ssh ${SERVER_USER}@${SERVER_IP} "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"
}

log "4. Testando conexão SSH sem senha..."

# Testar conexão
if ssh -o ConnectTimeout=10 rlponto-prod "echo 'Conexão SSH funcionando!'" >/dev/null 2>&1; then
    log "✅ Conexão SSH sem senha configurada com sucesso!"
else
    error "❌ Falha na configuração SSH. Verifique as configurações."
fi

log "5. Exibindo informações da configuração..."

echo -e "${BLUE}"
echo "================================================="
echo "🔑 CONFIGURAÇÃO SSH CONCLUÍDA"
echo "================================================="
echo "Servidor: ${SERVER_IP}"
echo "Usuário: ${SERVER_USER}"
echo "Chave SSH: ~/.ssh/${SSH_KEY_NAME}"
echo "Alias SSH: rlponto-prod"
echo ""
echo "Para conectar ao servidor:"
echo "  ssh rlponto-prod"
echo ""
echo "Chave pública (para backup):"
cat ~/.ssh/${SSH_KEY_NAME}.pub
echo -e "${NC}"

log "✅ Configuração SSH concluída com sucesso!"
