import { Metadata } from 'next';
import { Calendar, ArrowLeft, Plus, Clock, Mail, Play, Pause } from 'lucide-react';
import Link from 'next/link';
import { ScheduleManager } from '@/components/relatorios/schedule-manager';

export const metadata: Metadata = {
  title: 'Agendamento de Relatórios - RLPONTO',
  description: 'Configure relatórios automáticos e agendamentos por email',
};

export default function AgendamentosRelatoriosPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/relatorios">
                <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <ArrowLeft className="h-4 w-4 mr-2 inline" />
                  Voltar
                </button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-indigo-600 rounded-lg">
                  <Calendar className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Agendamento de Relatórios</h1>
                  <p className="text-gray-600">Configure relatórios automáticos e envios por email</p>
                </div>
              </div>
            </div>
            
            {/* Actions */}
            <div className="flex items-center space-x-3">
              <button className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
                <Plus className="h-4 w-4 mr-2 inline" />
                Novo Agendamento
              </button>
            </div>
          </div>

          {/* Descrição */}
          <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-indigo-900 mb-3">Automatização de Relatórios</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-indigo-700">
              <div>
                <strong>⏰ Agendamento Flexível</strong>
                <p className="mt-1">Configure execuções diárias, semanais, mensais ou personalizadas</p>
              </div>
              <div>
                <strong>📧 Envio Automático</strong>
                <p className="mt-1">Relatórios enviados automaticamente por email para destinatários</p>
              </div>
              <div>
                <strong>🔄 Monitoramento</strong>
                <p className="mt-1">Acompanhe execuções, falhas e histórico de envios</p>
              </div>
            </div>
          </div>

          {/* Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <Calendar className="w-4 h-4 text-indigo-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Agendamentos Ativos</p>
                  <p className="text-2xl font-semibold text-gray-900">12</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Mail className="w-4 h-4 text-green-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Emails Enviados Hoje</p>
                  <p className="text-2xl font-semibold text-gray-900">47</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-blue-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Próxima Execução</p>
                  <p className="text-2xl font-semibold text-gray-900">2h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-yellow-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Taxa de Sucesso</p>
                  <p className="text-2xl font-semibold text-gray-900">98.5%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Agendamentos Ativos */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Agendamentos Ativos</h2>
              <p className="text-sm text-gray-500">
                Gerencie seus relatórios automáticos e agendamentos
              </p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* Agendamento 1 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-medium text-gray-900">Relatório Mensal de Frequência</h3>
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          Ativo
                        </span>
                      </div>
                      <div className="mt-2 text-sm text-gray-600">
                        <p>📅 Todo dia 1º do mês às 08:00</p>
                        <p>📧 Enviado para: <EMAIL>, <EMAIL></p>
                        <p>📊 Última execução: 01/01/2025 às 08:00 - Sucesso</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded">
                        <Play className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-yellow-600 hover:bg-yellow-50 rounded">
                        <Pause className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Agendamento 2 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-medium text-gray-900">Dashboard Executivo Semanal</h3>
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          Ativo
                        </span>
                      </div>
                      <div className="mt-2 text-sm text-gray-600">
                        <p>📅 Toda segunda-feira às 07:00</p>
                        <p>📧 Enviado para: <EMAIL></p>
                        <p>📊 Última execução: 20/01/2025 às 07:00 - Sucesso</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded">
                        <Play className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-yellow-600 hover:bg-yellow-50 rounded">
                        <Pause className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Agendamento 3 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-medium text-gray-900">Relatório de Horas Extras</h3>
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                          Pausado
                        </span>
                      </div>
                      <div className="mt-2 text-sm text-gray-600">
                        <p>📅 Todo dia 15 do mês às 18:00</p>
                        <p>📧 Enviado para: <EMAIL></p>
                        <p>📊 Última execução: 15/12/2024 às 18:00 - Sucesso</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded">
                        <Play className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-yellow-600 hover:bg-yellow-50 rounded">
                        <Pause className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Schedule Manager Component */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Configurar Novo Agendamento</h2>
              <p className="text-sm text-gray-500">
                Configure um novo relatório automático
              </p>
            </div>
            <div className="p-6">
              <ScheduleManager />
            </div>
          </div>

          {/* Informações Adicionais */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recursos de Agendamento</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Tipos de Agendamento</h4>
                <ul className="space-y-1">
                  <li>• Diário - Execução todos os dias em horário específico</li>
                  <li>• Semanal - Execução em dias da semana selecionados</li>
                  <li>• Mensal - Execução em dias específicos do mês</li>
                  <li>• Personalizado - Configuração avançada com cron</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Recursos Avançados</h4>
                <ul className="space-y-1">
                  <li>• Múltiplos destinatários de email</li>
                  <li>• Filtros dinâmicos por período</li>
                  <li>• Formatos de exportação configuráveis</li>
                  <li>• Notificações de falha e sucesso</li>
                  <li>• Histórico completo de execuções</li>
                  <li>• Retry automático em caso de falha</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
