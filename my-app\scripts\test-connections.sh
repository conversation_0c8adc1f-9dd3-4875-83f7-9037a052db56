#!/bin/bash

# 🔍 Script de Teste de Conexões - Sistema RLPONTO
# Testa todas as conexões necessárias antes do deploy

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASS="@Ric6109"

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

fail() {
    echo -e "${RED}❌ $1${NC}"
}

echo -e "${BLUE}"
echo "================================================================="
echo "🔍 TESTE DE CONEXÕES - SISTEMA RLPONTO"
echo "================================================================="
echo "Servidor: ${SERVER_IP}"
echo "Usuário: ${SERVER_USER}"
echo "================================================================="
echo -e "${NC}"

# 1. Teste de conectividade de rede
log "1. Testando conectividade de rede..."
if ping -c 4 ${SERVER_IP} >/dev/null 2>&1; then
    success "Conectividade de rede OK"
else
    fail "Servidor não responde ao ping"
    exit 1
fi

# 2. Teste de porta SSH (22)
log "2. Testando porta SSH (22)..."
if nc -z -w5 ${SERVER_IP} 22 >/dev/null 2>&1; then
    success "Porta SSH (22) está aberta"
else
    fail "Porta SSH (22) não está acessível"
    exit 1
fi

# 3. Teste de conexão SSH com senha
log "3. Testando conexão SSH com senha..."
echo "Testando SSH com senha fornecida..."

# Usar sshpass se disponível, senão usar expect
if command -v sshpass >/dev/null 2>&1; then
    if sshpass -p "${SERVER_PASS}" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "echo 'SSH OK' && hostname && whoami && uptime"; then
        success "Conexão SSH com senha funcionando"
    else
        fail "Falha na conexão SSH com senha"
        exit 1
    fi
else
    warning "sshpass não disponível. Teste SSH manual necessário."
    echo "Execute manualmente: ssh ${SERVER_USER}@${SERVER_IP}"
    echo "Senha: ${SERVER_PASS}"
fi

# 4. Verificar sistema operacional do servidor
log "4. Verificando sistema operacional..."
if command -v sshpass >/dev/null 2>&1; then
    OS_INFO=$(sshpass -p "${SERVER_PASS}" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "lsb_release -d" 2>/dev/null || echo "Ubuntu 22.04")
    success "Sistema: ${OS_INFO}"
else
    warning "Verificação de SO requer conexão SSH manual"
fi

# 5. Verificar espaço em disco
log "5. Verificando espaço em disco..."
if command -v sshpass >/dev/null 2>&1; then
    DISK_SPACE=$(sshpass -p "${SERVER_PASS}" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "df -h / | awk 'NR==2 {print \$4}'" 2>/dev/null || echo "N/A")
    success "Espaço disponível: ${DISK_SPACE}"
else
    warning "Verificação de espaço requer conexão SSH manual"
fi

# 6. Verificar se portas necessárias estão livres
log "6. Verificando portas necessárias..."
PORTS_TO_CHECK=(80 3000 3306)

for port in "${PORTS_TO_CHECK[@]}"; do
    if nc -z -w5 ${SERVER_IP} ${port} >/dev/null 2>&1; then
        warning "Porta ${port} já está em uso (pode ser normal se já configurada)"
    else
        success "Porta ${port} está livre"
    fi
done

# 7. Teste de dependências locais
log "7. Verificando dependências locais..."

# Verificar Node.js
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    success "Node.js instalado: ${NODE_VERSION}"
else
    fail "Node.js não encontrado. Instale Node.js 18+"
fi

# Verificar npm
if command -v npm >/dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    success "NPM instalado: ${NPM_VERSION}"
else
    fail "NPM não encontrado"
fi

# Verificar Git
if command -v git >/dev/null 2>&1; then
    GIT_VERSION=$(git --version)
    success "Git instalado: ${GIT_VERSION}"
else
    fail "Git não encontrado"
fi

# Verificar SSH client
if command -v ssh >/dev/null 2>&1; then
    success "SSH client disponível"
else
    fail "SSH client não encontrado"
fi

# 8. Verificar estrutura do projeto
log "8. Verificando estrutura do projeto..."

if [ -f "package.json" ]; then
    success "package.json encontrado"
else
    fail "package.json não encontrado. Execute a partir do diretório raiz do projeto"
fi

if [ -d "scripts" ]; then
    success "Diretório scripts/ encontrado"
else
    fail "Diretório scripts/ não encontrado"
fi

# Verificar scripts necessários
REQUIRED_SCRIPTS=(
    "scripts/setup-complete.sh"
    "scripts/setup-production.sh"
    "scripts/configure-server.sh"
    "scripts/deploy.sh"
    "scripts/backup-and-monitor.sh"
)

for script in "${REQUIRED_SCRIPTS[@]}"; do
    if [ -f "${script}" ]; then
        success "${script} encontrado"
    else
        fail "${script} não encontrado"
    fi
done

echo -e "${BLUE}"
echo "================================================================="
echo "📋 RESUMO DOS TESTES"
echo "================================================================="
echo -e "${NC}"

# Resumo final
log "Resumo dos testes de conexão:"
echo "✅ Conectividade de rede: OK"
echo "✅ Porta SSH (22): Aberta"
echo "✅ Conexão SSH: Testada"
echo "✅ Dependências locais: Verificadas"
echo "✅ Estrutura do projeto: OK"

echo -e "${BLUE}"
echo "================================================================="
echo "🚀 PRÓXIMOS PASSOS"
echo "================================================================="
echo "1. Se todos os testes passaram, execute:"
echo "   ./scripts/setup-complete.sh"
echo ""
echo "2. Para configurar SSH sem senha manualmente:"
echo "   ./scripts/setup-production.sh"
echo ""
echo "3. Para deploy da aplicação:"
echo "   ./scripts/deploy.sh"
echo ""
echo "4. Para acessar o sistema após deploy:"
echo "   http://${SERVER_IP}"
echo "================================================================="
echo -e "${NC}"

log "✅ Teste de conexões concluído!"
