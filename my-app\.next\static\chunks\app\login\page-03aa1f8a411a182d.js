(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},6271:(e,s,r)=>{Promise.resolve().then(r.bind(r,9690))},9690:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(5155),o=r(2115),a=r(5695);function n(){let[e,s]=(0,o.useState)(""),[r,n]=(0,o.useState)(""),[i,l]=(0,o.useState)(!1),u=(0,a.useRouter)();console.log("LoginPage renderizando - vers\xe3o simplificada");let c=async s=>{s.preventDefault(),console.log("Tentativa de login:",{usuario:e,senha:r});try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({usuario:e,senha:r})}),t=await s.json();console.log("Resposta do login:",t),t.success?(document.cookie="rlponto_user=".concat(JSON.stringify(t.user),"; path=/; max-age=").concat(28800,"; SameSite=Lax"),alert("Login realizado com sucesso!"),u.push("/dashboard")):alert("Erro no login: "+(t.error||t.message))}catch(e){console.error("Erro na requisi\xe7\xe3o:",e),alert("Erro na conex\xe3o")}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4",children:(0,t.jsx)("div",{className:"max-w-md w-full",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)("svg",{className:"h-8 w-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"RLPONTO"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Sistema de Controle de Ponto"})]}),(0,t.jsxs)("form",{onSubmit:c,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"usuario",className:"block text-sm font-medium text-gray-700 mb-1",children:"Usu\xe1rio"}),(0,t.jsx)("input",{id:"usuario",type:"text",value:e,onChange:e=>s(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Digite seu usu\xe1rio",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"senha",className:"block text-sm font-medium text-gray-700 mb-1",children:"Senha"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"senha",type:i?"text":"password",value:r,onChange:e=>n(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10",placeholder:"Digite sua senha",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>l(!i),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:i?(0,t.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):(0,t.jsxs)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),(0,t.jsx)("button",{type:"submit",className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium",children:"Entrar"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsx)("a",{href:"#",className:"text-sm text-blue-600 hover:text-blue-800 hover:underline",children:"Esqueceu sua senha?"})})]})})})}}},e=>{e.O(0,[441,964,358],()=>e(e.s=6271)),_N_E=e.O()}]);