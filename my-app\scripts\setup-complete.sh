#!/bin/bash

# 🚀 Script Completo de Setup - Sistema RLPONTO
# Executa toda a configuração do ambiente de produção

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configurações
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASS="@Ric6109"

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

step() {
    echo -e "${BLUE}$1${NC}"
}

success() {
    echo -e "${PURPLE}$1${NC}"
}

echo -e "${BLUE}"
echo "================================================================="
echo "🚀 SETUP COMPLETO DO SISTEMA RLPONTO"
echo "================================================================="
echo "Servidor: ${SERVER_IP}"
echo "Usuário: ${SERVER_USER}"
echo ""
echo "Este script irá:"
echo "1. Configurar SSH sem senha"
echo "2. Configurar o servidor de produção"
echo "3. Preparar ambiente para deploy"
echo "4. Configurar monitoramento e backup"
echo "================================================================="
echo -e "${NC}"

read -p "Deseja continuar? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Setup cancelado."
    exit 1
fi

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    error "Execute este script a partir do diretório raiz do projeto"
fi

# Verificar se os scripts existem
SCRIPTS_DIR="./scripts"
if [ ! -d "$SCRIPTS_DIR" ]; then
    error "Diretório de scripts não encontrado: $SCRIPTS_DIR"
fi

# 1. Configurar SSH
step "PASSO 1: Configurando SSH sem senha..."
log "Executando setup-production.sh..."

chmod +x ${SCRIPTS_DIR}/setup-production.sh
${SCRIPTS_DIR}/setup-production.sh

if [ $? -eq 0 ]; then
    success "✅ SSH configurado com sucesso!"
else
    error "❌ Falha na configuração SSH"
fi

# 2. Configurar servidor
step "PASSO 2: Configurando servidor de produção..."
log "Enviando e executando configure-server.sh no servidor..."

# Enviar script para o servidor
scp ${SCRIPTS_DIR}/configure-server.sh rlponto-prod:/tmp/

# Executar script no servidor
ssh rlponto-prod "chmod +x /tmp/configure-server.sh && /tmp/configure-server.sh"

if [ $? -eq 0 ]; then
    success "✅ Servidor configurado com sucesso!"
else
    error "❌ Falha na configuração do servidor"
fi

# 3. Configurar scripts de monitoramento
step "PASSO 3: Configurando monitoramento e backup..."
log "Enviando scripts de monitoramento..."

# Enviar script de backup e monitoramento
scp ${SCRIPTS_DIR}/backup-and-monitor.sh rlponto-prod:/opt/rlponto/

# Configurar permissões e cron jobs
ssh rlponto-prod << 'EOF'
# Dar permissões ao script
chmod +x /opt/rlponto/backup-and-monitor.sh
chown rlponto:rlponto /opt/rlponto/backup-and-monitor.sh

# Configurar cron jobs para backup automático
(crontab -u rlponto -l 2>/dev/null; echo "# Backup diário às 2h da manhã") | crontab -u rlponto -
(crontab -u rlponto -l 2>/dev/null; echo "0 2 * * * /opt/rlponto/backup-and-monitor.sh backup") | crontab -u rlponto -

# Configurar cron jobs para monitoramento
(crontab -u rlponto -l 2>/dev/null; echo "# Monitoramento a cada 15 minutos") | crontab -u rlponto -
(crontab -u rlponto -l 2>/dev/null; echo "*/15 * * * * /opt/rlponto/backup-and-monitor.sh monitor") | crontab -u rlponto -

# Configurar relatório semanal
(crontab -u rlponto -l 2>/dev/null; echo "# Relatório semanal aos domingos às 6h") | crontab -u rlponto -
(crontab -u rlponto -l 2>/dev/null; echo "0 6 * * 0 /opt/rlponto/backup-and-monitor.sh report") | crontab -u rlponto -

echo "Cron jobs configurados:"
crontab -u rlponto -l
EOF

if [ $? -eq 0 ]; then
    success "✅ Monitoramento configurado com sucesso!"
else
    warning "⚠️ Possível problema na configuração do monitoramento"
fi

# 4. Configurar deploy
step "PASSO 4: Preparando scripts de deploy..."
log "Configurando permissões dos scripts de deploy..."

chmod +x ${SCRIPTS_DIR}/deploy.sh

# 5. Teste final
step "PASSO 5: Executando testes finais..."
log "Testando conectividade e serviços..."

# Testar SSH
if ssh -o ConnectTimeout=10 rlponto-prod "echo 'SSH OK'" >/dev/null 2>&1; then
    log "✅ SSH funcionando"
else
    error "❌ SSH não está funcionando"
fi

# Testar serviços no servidor
ssh rlponto-prod << 'EOF'
echo "Verificando serviços..."

# MySQL
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL rodando"
else
    echo "❌ MySQL não está rodando"
    exit 1
fi

# Nginx
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx rodando"
else
    echo "❌ Nginx não está rodando"
    exit 1
fi

# Verificar portas
if netstat -tlnp | grep -q ":80 "; then
    echo "✅ Porta 80 (HTTP) aberta"
else
    echo "❌ Porta 80 não está aberta"
fi

if netstat -tlnp | grep -q ":3306 "; then
    echo "✅ Porta 3306 (MySQL) aberta"
else
    echo "❌ Porta 3306 não está aberta"
fi

echo "Verificação de serviços concluída!"
EOF

if [ $? -eq 0 ]; then
    success "✅ Todos os serviços estão funcionando!"
else
    error "❌ Alguns serviços não estão funcionando corretamente"
fi

# 6. Informações finais
step "SETUP CONCLUÍDO COM SUCESSO! 🎉"

echo -e "${BLUE}"
echo "================================================================="
echo "🎉 AMBIENTE DE PRODUÇÃO CONFIGURADO COM SUCESSO!"
echo "================================================================="
echo ""
echo "📋 INFORMAÇÕES DO SERVIDOR:"
echo "  IP: ${SERVER_IP}"
echo "  Usuário: rlponto"
echo "  Diretório: /opt/rlponto"
echo "  URL: http://${SERVER_IP}"
echo ""
echo "🔑 ACESSO SSH:"
echo "  ssh rlponto-prod"
echo ""
echo "🚀 DEPLOY:"
echo "  ./scripts/deploy.sh"
echo ""
echo "📊 MONITORAMENTO:"
echo "  ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh monitor'"
echo "  ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh backup'"
echo "  ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh report'"
echo ""
echo "📝 LOGS:"
echo "  ssh rlponto-prod 'tail -f /var/log/rlponto/combined.log'"
echo "  ssh rlponto-prod 'pm2 logs rlponto'"
echo ""
echo "🔧 COMANDOS ÚTEIS:"
echo "  ssh rlponto-prod 'pm2 status'           # Status da aplicação"
echo "  ssh rlponto-prod 'pm2 restart rlponto'  # Reiniciar aplicação"
echo "  ssh rlponto-prod 'systemctl status nginx' # Status do Nginx"
echo "  ssh rlponto-prod 'systemctl status mysql' # Status do MySQL"
echo ""
echo "📅 BACKUP AUTOMÁTICO:"
echo "  ✅ Backup diário às 2h da manhã"
echo "  ✅ Monitoramento a cada 15 minutos"
echo "  ✅ Relatório semanal aos domingos"
echo ""
echo "🎯 PRÓXIMOS PASSOS:"
echo "  1. Executar primeiro deploy: ./scripts/deploy.sh"
echo "  2. Acessar sistema: http://${SERVER_IP}"
echo "  3. Configurar dados da empresa"
echo "  4. Cadastrar primeiro usuário administrador"
echo ""
echo "================================================================="
echo -e "${NC}"

log "Setup completo finalizado com sucesso! 🚀"
