#!/bin/bash

# 🔍 TESTE RIGOROSO DE SSH - Sistema RLPONTO
# Identifica exatamente qual é o problema com SSH

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
SERVER_IP="************"
SERVER_USER="root"
SSH_KEY_NAME="rl-ponto-next"

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}❌ [ERROR] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ [SUCCESS] $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️ [WARNING] $1${NC}"
}

fail() {
    echo -e "${RED}❌ [FAIL] $1${NC}"
}

echo -e "${RED}"
echo "================================================================="
echo "🔍 TESTE RIGOROSO DE SSH - IDENTIFICANDO PROBLEMAS"
echo "================================================================="
echo "Servidor: ${SERVER_IP}"
echo "Usuário: ${SERVER_USER}"
echo "Chave esperada: ~/.ssh/${SSH_KEY_NAME}"
echo "================================================================="
echo -e "${NC}"

# 1. Verificar se chave SSH existe
log "1. Verificando existência da chave SSH..."
SSH_KEY_PATH="$HOME/.ssh/${SSH_KEY_NAME}"

if [ -f "${SSH_KEY_PATH}" ]; then
    success "Chave privada existe: ${SSH_KEY_PATH}"
    
    # Verificar permissões
    PERMS=$(stat -c "%a" "${SSH_KEY_PATH}" 2>/dev/null || stat -f "%A" "${SSH_KEY_PATH}" 2>/dev/null || echo "unknown")
    if [ "$PERMS" = "600" ]; then
        success "Permissões da chave privada corretas: 600"
    else
        fail "Permissões da chave privada incorretas: $PERMS (deveria ser 600)"
    fi
else
    fail "Chave privada NÃO EXISTE: ${SSH_KEY_PATH}"
    echo "   Isso explica por que SSH pede senha!"
fi

if [ -f "${SSH_KEY_PATH}.pub" ]; then
    success "Chave pública existe: ${SSH_KEY_PATH}.pub"
    echo "   Conteúdo da chave pública:"
    cat "${SSH_KEY_PATH}.pub"
else
    fail "Chave pública NÃO EXISTE: ${SSH_KEY_PATH}.pub"
fi

# 2. Verificar configuração SSH
log "2. Verificando configuração SSH..."
SSH_CONFIG="$HOME/.ssh/config"

if [ -f "${SSH_CONFIG}" ]; then
    success "Arquivo ~/.ssh/config existe"
    
    if grep -q "Host rlponto-prod" "${SSH_CONFIG}"; then
        success "Configuração para rlponto-prod encontrada"
        echo "   Configuração atual:"
        grep -A 10 "Host rlponto-prod" "${SSH_CONFIG}" | sed 's/^/   /'
    else
        fail "Configuração para rlponto-prod NÃO ENCONTRADA"
    fi
else
    fail "Arquivo ~/.ssh/config NÃO EXISTE"
fi

# 3. Teste de SSH sem senha (deve falhar se não configurado)
log "3. Testando SSH sem senha..."
echo "   Tentando conectar sem senha (deve falhar se não configurado)..."

if ssh -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "echo 'SSH sem senha OK'" 2>/dev/null; then
    success "SSH sem senha FUNCIONANDO!"
else
    fail "SSH sem senha NÃO FUNCIONA (por isso pede senha)"
    echo "   Erro esperado: chave não configurada no servidor"
fi

# 4. Teste de SSH com alias (se configurado)
log "4. Testando alias SSH..."
if grep -q "Host rlponto-prod" "$HOME/.ssh/config" 2>/dev/null; then
    echo "   Tentando usar alias rlponto-prod..."
    if ssh -o ConnectTimeout=5 -o BatchMode=yes rlponto-prod "echo 'Alias funcionando'" 2>/dev/null; then
        success "Alias rlponto-prod FUNCIONANDO!"
    else
        fail "Alias rlponto-prod NÃO FUNCIONA"
    fi
else
    warning "Alias rlponto-prod não configurado ainda"
fi

# 5. Verificar diretório .ssh
log "5. Verificando estrutura do diretório .ssh..."
if [ -d "$HOME/.ssh" ]; then
    success "Diretório ~/.ssh existe"
    
    # Verificar permissões do diretório
    DIR_PERMS=$(stat -c "%a" "$HOME/.ssh" 2>/dev/null || stat -f "%A" "$HOME/.ssh" 2>/dev/null || echo "unknown")
    if [ "$DIR_PERMS" = "700" ]; then
        success "Permissões do diretório ~/.ssh corretas: 700"
    else
        warning "Permissões do diretório ~/.ssh: $DIR_PERMS (recomendado: 700)"
    fi
    
    echo "   Conteúdo do diretório ~/.ssh:"
    ls -la "$HOME/.ssh" | sed 's/^/   /'
else
    fail "Diretório ~/.ssh NÃO EXISTE"
fi

# 6. Verificar se authorized_keys existe no servidor
log "6. Verificando authorized_keys no servidor..."
echo "   Tentando verificar ~/.ssh/authorized_keys no servidor..."
echo "   (Isso vai pedir senha porque SSH sem senha não está configurado)"

# 7. Diagnóstico detalhado
log "7. Diagnóstico detalhado..."

echo -e "${BLUE}"
echo "================================================================="
echo "🔍 DIAGNÓSTICO COMPLETO"
echo "================================================================="

# Resumo dos problemas encontrados
PROBLEMS_FOUND=0

echo "PROBLEMAS IDENTIFICADOS:"
echo ""

if [ ! -f "${SSH_KEY_PATH}" ]; then
    echo "❌ PROBLEMA 1: Chave SSH não existe"
    echo "   Arquivo: ${SSH_KEY_PATH}"
    echo "   Solução: Executar scripts/setup-production.sh"
    echo ""
    PROBLEMS_FOUND=$((PROBLEMS_FOUND + 1))
fi

if [ ! -f "${SSH_CONFIG}" ] || ! grep -q "Host rlponto-prod" "${SSH_CONFIG}" 2>/dev/null; then
    echo "❌ PROBLEMA 2: Configuração SSH não existe"
    echo "   Arquivo: ${SSH_CONFIG}"
    echo "   Solução: Executar scripts/setup-production.sh"
    echo ""
    PROBLEMS_FOUND=$((PROBLEMS_FOUND + 1))
fi

if ! ssh -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} "echo test" >/dev/null 2>&1; then
    echo "❌ PROBLEMA 3: Chave pública não está no servidor"
    echo "   Local: ~/.ssh/authorized_keys no servidor"
    echo "   Solução: Executar scripts/setup-production.sh"
    echo ""
    PROBLEMS_FOUND=$((PROBLEMS_FOUND + 1))
fi

if [ $PROBLEMS_FOUND -eq 0 ]; then
    echo "🤔 ESTRANHO: Nenhum problema óbvio encontrado"
    echo "   Mas SSH ainda pede senha..."
    echo "   Pode ser problema de permissões ou configuração"
else
    echo "📊 TOTAL DE PROBLEMAS ENCONTRADOS: $PROBLEMS_FOUND"
fi

echo ""
echo "================================================================="
echo "🚀 SOLUÇÃO RECOMENDADA"
echo "================================================================="
echo ""
echo "Para corrigir TODOS os problemas de SSH:"
echo ""
echo "1. Execute o script de configuração SSH:"
echo "   bash scripts/setup-production.sh"
echo ""
echo "2. Ou execute o setup completo:"
echo "   bash scripts/setup-complete.sh"
echo ""
echo "3. Após a configuração, teste:"
echo "   ssh rlponto-prod"
echo "   (NÃO deve pedir senha)"
echo ""
echo "================================================================="
echo -e "${NC}"

if [ $PROBLEMS_FOUND -gt 0 ]; then
    error "SSH NÃO ESTÁ CONFIGURADO CORRETAMENTE!"
    echo "Execute scripts/setup-production.sh para corrigir."
    exit 1
else
    warning "SSH pode ter problemas sutis. Verifique configuração."
    exit 0
fi
