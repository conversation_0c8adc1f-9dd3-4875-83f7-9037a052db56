import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { Funcionario } from '@/types';

// Schema de validação para funcionário
const funcionarioSchema = z.object({
  nomeCompleto: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  cpf: z.string().regex(/^\d{11}$/, 'CPF deve ter 11 dígitos'),
  rg: z.string().optional(),
  email: z.string().email('Email inválido').optional(),
  telefone: z.string().optional(),
  celular: z.string().optional(),
  endereco: z.object({
    cep: z.string().optional(),
    logradouro: z.string().optional(),
    numero: z.string().optional(),
    complemento: z.string().optional(),
    bairro: z.string().optional(),
    cidade: z.string().optional(),
    uf: z.string().optional(),
  }).optional(),
  dadosProfissionais: z.object({
    matricula: z.string().min(1, 'Matrícula é obrigatória'),
    cargo: z.string().min(1, 'Cargo é obrigatório'),
    setor: z.string().min(1, 'Setor é obrigatório'),
    dataAdmissao: z.string().transform((str) => new Date(str)),
    salario: z.number().optional(),
    cargaHoraria: z.number().min(1, 'Carga horária é obrigatória'),
    horarioTrabalho: z.object({
      entrada: z.string(),
      saida: z.string(),
      intervaloInicio: z.string().optional(),
      intervaloFim: z.string().optional(),
    }),
  }),
  observacoes: z.string().optional(),
});

// Dados mock para demonstração
const mockFuncionarios: Funcionario[] = [
  {
    id: '1',
    nomeCompleto: 'João Silva Santos',
    cpf: '12345678901',
    rg: '123456789',
    email: '<EMAIL>',
    telefone: '1133334444',
    celular: '11999887766',
    endereco: {
      cep: '01234567',
      logradouro: 'Rua das Flores',
      numero: '123',
      bairro: 'Centro',
      cidade: 'São Paulo',
      uf: 'SP',
    },
    dadosProfissionais: {
      matricula: 'EMP001',
      cargo: 'Analista de Sistemas',
      setor: 'Tecnologia',
      dataAdmissao: new Date('2023-01-15'),
      salario: 5500,
      cargaHoraria: 40,
      horarioTrabalho: {
        entrada: '08:00',
        saida: '17:00',
        intervaloInicio: '12:00',
        intervaloFim: '13:00',
      },
    },
    foto: '/avatars/joao.jpg',
    biometria: {
      cadastrada: true,
      dataUltimoCadastro: new Date('2023-01-16'),
      templates: 10,
    },
    status: 'ativo',
    observacoes: 'Funcionário exemplar',
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-01-15'),
  },
  {
    id: '2',
    nomeCompleto: 'Maria Oliveira Costa',
    cpf: '98765432109',
    rg: '987654321',
    email: '<EMAIL>',
    telefone: '1144445555',
    celular: '11888776655',
    endereco: {
      cep: '09876543',
      logradouro: 'Av. Principal',
      numero: '456',
      bairro: 'Jardim',
      cidade: 'São Paulo',
      uf: 'SP',
    },
    dadosProfissionais: {
      matricula: 'EMP002',
      cargo: 'Gerente de Vendas',
      setor: 'Vendas',
      dataAdmissao: new Date('2022-06-10'),
      salario: 7500,
      cargaHoraria: 44,
      horarioTrabalho: {
        entrada: '07:30',
        saida: '16:30',
        intervaloInicio: '11:30',
        intervaloFim: '12:30',
      },
    },
    foto: '/avatars/maria.jpg',
    biometria: {
      cadastrada: true,
      dataUltimoCadastro: new Date('2022-06-11'),
      templates: 8,
    },
    status: 'ativo',
    observacoes: 'Líder de equipe',
    createdAt: new Date('2022-06-10'),
    updatedAt: new Date('2022-06-10'),
  },
  {
    id: '3',
    nomeCompleto: 'Carlos Roberto Lima',
    cpf: '11122233344',
    rg: '111222333',
    email: '<EMAIL>',
    telefone: '1155556666',
    celular: '11777665544',
    endereco: {
      cep: '12345678',
      logradouro: 'Rua do Comércio',
      numero: '789',
      bairro: 'Vila Nova',
      cidade: 'São Paulo',
      uf: 'SP',
    },
    dadosProfissionais: {
      matricula: 'EMP003',
      cargo: 'Operador de Produção',
      setor: 'Produção',
      dataAdmissao: new Date('2021-03-20'),
      salario: 3200,
      cargaHoraria: 40,
      horarioTrabalho: {
        entrada: '06:00',
        saida: '15:00',
        intervaloInicio: '10:00',
        intervaloFim: '10:15',
      },
    },
    foto: '/avatars/carlos.jpg',
    biometria: {
      cadastrada: false,
      templates: 0,
    },
    status: 'ativo',
    observacoes: 'Funcionário dedicado',
    createdAt: new Date('2021-03-20'),
    updatedAt: new Date('2021-03-20'),
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const setor = searchParams.get('setor');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let filteredFuncionarios = [...mockFuncionarios];

    // Filtro por busca (nome, CPF, matrícula)
    if (search) {
      const searchLower = search.toLowerCase();
      filteredFuncionarios = filteredFuncionarios.filter(
        (funcionario) =>
          funcionario.nomeCompleto.toLowerCase().includes(searchLower) ||
          funcionario.cpf.includes(search) ||
          funcionario.dadosProfissionais.matricula.toLowerCase().includes(searchLower)
      );
    }

    // Filtro por setor
    if (setor) {
      filteredFuncionarios = filteredFuncionarios.filter(
        (funcionario) => funcionario.dadosProfissionais.setor.toLowerCase() === setor.toLowerCase()
      );
    }

    // Filtro por status
    if (status) {
      filteredFuncionarios = filteredFuncionarios.filter(
        (funcionario) => funcionario.status === status
      );
    }

    // Paginação
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFuncionarios = filteredFuncionarios.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      funcionarios: paginatedFuncionarios,
      pagination: {
        page,
        limit,
        total: filteredFuncionarios.length,
        totalPages: Math.ceil(filteredFuncionarios.length / limit),
      },
    });

  } catch (error) {
    console.error('Erro ao buscar funcionários:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validar dados de entrada
    const validationResult = funcionarioSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Dados inválidos',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const novoFuncionario: Funcionario = {
      id: Date.now().toString(),
      ...validationResult.data,
      foto: undefined,
      biometria: {
        cadastrada: false,
        templates: 0,
      },
      status: 'ativo',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Em produção, salvar no banco de dados
    mockFuncionarios.push(novoFuncionario);

    return NextResponse.json({
      success: true,
      funcionario: novoFuncionario,
      message: 'Funcionário cadastrado com sucesso',
    });

  } catch (error) {
    console.error('Erro ao criar funcionário:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
