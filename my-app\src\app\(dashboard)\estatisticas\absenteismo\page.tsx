import { Metadata } from 'next';
import { 
  AlertTriangle, 
  ArrowLeft, 
  Users, 
  Calendar, 
  TrendingDown,
  Clock,
  Download,
  UserX
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Análise de Absenteísmo - RLPONTO',
  description: 'Análise detalhada de absenteísmo e faltas',
};

// Dados mockados para demonstração
const absenteismoData = {
  geral: {
    taxaAbsenteismo: 3.8,
    totalFaltas: 47,
    diasPerdidos: 94,
    custoEstimado: 15420
  },
  tiposFalta: [
    { tipo: 'Doença', quantidade: 18, percentual: 38.3 },
    { tipo: 'Particular', quantidade: 12, percentual: 25.5 },
    { tipo: 'Médico', quantidade: 8, percentual: 17.0 },
    { tipo: 'Família', quantidade: 6, percentual: 12.8 },
    { tipo: 'Outros', quantidade: 3, percentual: 6.4 }
  ],
  funcionariosProblematicos: [
    { nome: '<PERSON>', departamento: 'Operações', faltas: 8, ultimaFalta: '2024-01-20' },
    { nome: 'Ferna<PERSON>', departamento: 'Vendas', faltas: 6, ultimaFalta: '2024-01-18' },
    { nome: 'Carlos Mendes', departamento: 'TI', faltas: 5, ultimaFalta: '2024-01-15' },
    { nome: 'Ana Rodrigues', departamento: 'Financeiro', faltas: 4, ultimaFalta: '2024-01-12' }
  ],
  departamentos: [
    { nome: 'Operações', absenteismo: 5.2, funcionarios: 35, faltas: 18 },
    { nome: 'TI', absenteismo: 4.2, funcionarios: 18, faltas: 8 },
    { nome: 'Financeiro', absenteismo: 3.8, funcionarios: 12, faltas: 5 },
    { nome: 'Vendas', absenteismo: 2.1, funcionarios: 25, faltas: 5 },
    { nome: 'RH', absenteismo: 1.5, funcionarios: 8, faltas: 1 }
  ]
};

export default function AbsenteismoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Link href="/estatisticas">
                <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </button>
              </Link>
              <div className="p-2 bg-red-600 rounded-lg">
                <AlertTriangle className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Análise de Absenteísmo</h1>
                <p className="text-gray-600">Monitoramento de faltas e ausências</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option>Últimos 30 dias</option>
                <option>Últimos 90 dias</option>
                <option>Último ano</option>
              </select>
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>

          {/* Métricas Principais */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Taxa de Absenteísmo</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {absenteismoData.geral.taxaAbsenteismo}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <UserX className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total de Faltas</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {absenteismoData.geral.totalFaltas}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Dias Perdidos</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {absenteismoData.geral.diasPerdidos}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingDown className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Custo Estimado</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    R$ {absenteismoData.geral.custoEstimado.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Gráfico de Evolução (Placeholder) */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Evolução do Absenteísmo</h2>
            </div>
            <div className="p-6">
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <TrendingDown className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Gráfico de evolução do absenteísmo</p>
                  <p className="text-sm text-gray-400">Implementação com biblioteca de gráficos</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Tipos de Falta */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Tipos de Falta</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {absenteismoData.tiposFalta.map((tipo, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-red-500 rounded-full mr-3" />
                        <span className="text-sm font-medium text-gray-900">{tipo.tipo}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">{tipo.quantidade}</span>
                        <span className="text-sm font-medium text-gray-900">{tipo.percentual}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Funcionários com Mais Faltas */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Funcionários com Mais Faltas</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {absenteismoData.funcionariosProblematicos.map((funcionario, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <UserX className="w-4 h-4 text-red-600" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{funcionario.nome}</p>
                          <p className="text-xs text-gray-500">{funcionario.departamento}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-red-600">{funcionario.faltas} faltas</p>
                        <p className="text-xs text-gray-500">Última: {funcionario.ultimaFalta}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Absenteísmo por Departamento */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Absenteísmo por Departamento</h2>
            </div>
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Departamento
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Funcionários
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Taxa de Absenteísmo
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total de Faltas
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {absenteismoData.departamentos.map((dept, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {dept.nome}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.funcionarios}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            dept.absenteismo <= 2 ? 'bg-green-100 text-green-800' :
                            dept.absenteismo <= 4 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.absenteismo}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.faltas}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Alertas e Recomendações */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Alertas e Recomendações</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Atenção Necessária</h3>
                      <p className="text-sm text-red-700 mt-1">
                        O departamento de Operações apresenta taxa de absenteísmo acima da média. 
                        Recomenda-se investigação das causas.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <Clock className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Acompanhamento</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        4 funcionários apresentam padrão de faltas recorrentes. 
                        Considere reuniões individuais para entender as causas.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

