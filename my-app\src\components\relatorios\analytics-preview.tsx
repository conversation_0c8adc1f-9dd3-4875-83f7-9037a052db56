'use client';

import { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>Up, 
  Users, 
  Clock,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Settings,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui';

export function AnalyticsPreview() {
  const [selectedMetric, setSelectedMetric] = useState('frequencia');

  const metrics = {
    frequencia: {
      title: 'Taxa de Frequência',
      value: '94.2%',
      change: '+2.1%',
      trend: 'up',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      icon: CheckCircle
    },
    horasExtras: {
      title: 'Horas Extras',
      value: '127h',
      change: '-8.3%',
      trend: 'down',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      icon: Clock
    },
    absenteismo: {
      title: 'Taxa de Absenteísmo',
      value: '5.8%',
      change: '-1.2%',
      trend: 'down',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      icon: AlertCircle
    },
    custos: {
      title: 'Custo Total',
      value: 'R$ 45.2k',
      change: '+3.7%',
      trend: 'up',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      icon: DollarSign
    }
  };

  const chartData = [
    { name: 'TI', value: 95.2, color: '#3B82F6' },
    { name: 'RH', value: 92.8, color: '#10B981' },
    { name: 'Vendas', value: 89.5, color: '#F59E0B' },
    { name: 'Produção', value: 96.1, color: '#8B5CF6' },
    { name: 'Admin', value: 93.7, color: '#EF4444' }
  ];

  return (
    <div className="space-y-6">
      {/* Métricas Principais */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-900">Métricas Principais</h3>
        <div className="grid grid-cols-2 gap-3">
          {Object.entries(metrics).map(([key, metric]) => (
            <div
              key={key}
              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                selectedMetric === key
                  ? `border-purple-300 ${metric.bgColor}`
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedMetric(key)}
            >
              <div className="flex items-center justify-between mb-2">
                <metric.icon className={`h-4 w-4 ${metric.color}`} />
                <span className={`text-xs px-2 py-1 rounded-full ${
                  metric.trend === 'up' 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-red-100 text-red-700'
                }`}>
                  {metric.change}
                </span>
              </div>
              <div className="text-lg font-bold text-gray-900">{metric.value}</div>
              <div className="text-xs text-gray-600">{metric.title}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Gráfico Preview */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-900">Preview do Gráfico</h3>
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-gray-700">
              Frequência por Departamento
            </span>
            <BarChart3 className="h-4 w-4 text-gray-400" />
          </div>
          
          {/* Gráfico Simulado */}
          <div className="space-y-2">
            {chartData.map((item, index) => (
              <div key={index} className="flex items-center space-x-3">
                <span className="text-xs text-gray-600 w-12">{item.name}</span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${item.value}%`,
                      backgroundColor: item.color
                    }}
                  />
                </div>
                <span className="text-xs font-medium text-gray-700 w-10">
                  {item.value}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Insights */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-900">Insights Automáticos</h3>
        <div className="space-y-2">
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <TrendingUp className="h-4 w-4 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-900">Melhoria Detectada</p>
                <p className="text-xs text-green-700">
                  Frequência aumentou 2.1% comparado ao mês anterior
                </p>
              </div>
            </div>
          </div>
          
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-900">Atenção Necessária</p>
                <p className="text-xs text-yellow-700">
                  Departamento de Vendas com frequência abaixo da média
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Configurações Rápidas */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-900">Configurações</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span className="text-xs text-gray-600">Período</span>
            <span className="text-xs font-medium text-gray-900">Último mês</span>
          </div>
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span className="text-xs text-gray-600">Departamentos</span>
            <span className="text-xs font-medium text-gray-900">Todos (5)</span>
          </div>
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span className="text-xs text-gray-600">Formato</span>
            <span className="text-xs font-medium text-gray-900">PDF + Excel</span>
          </div>
        </div>
      </div>

      {/* Ações */}
      <div className="space-y-2">
        <Button variant="primary" size="sm" className="w-full">
          <Download className="h-3 w-3 mr-2" />
          Gerar Relatório Completo
        </Button>
        <Button variant="outline" size="sm" className="w-full">
          <Settings className="h-3 w-3 mr-2" />
          Configurar Template
        </Button>
      </div>

      {/* Estatísticas do Preview */}
      <div className="pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-gray-900">156</div>
            <div className="text-xs text-gray-600">Funcionários</div>
          </div>
          <div>
            <div className="text-lg font-bold text-gray-900">5</div>
            <div className="text-xs text-gray-600">Departamentos</div>
          </div>
        </div>
      </div>
    </div>
  );
}
