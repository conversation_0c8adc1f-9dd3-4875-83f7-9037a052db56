import { Suspense } from 'react';
import { Metadata } from 'next';
import { FuncionariosList } from '@/components/funcionarios/funcionarios-list';
import { FuncionariosFilters } from '@/components/funcionarios/funcionarios-filters';
import { Button } from '@/components/ui';
import { Plus, Users } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Funcionários - RLPONTO',
  description: 'Gestão de funcionários do sistema',
};

interface FuncionariosPageProps {
  searchParams: Promise<{
    search?: string;
    setor?: string;
    status?: string;
    page?: string;
  }>;
}

export default async function FuncionariosPage({ searchParams }: FuncionariosPageProps) {
  const params = await searchParams;
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Users className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Funcionários</h1>
                <p className="text-gray-600">Gerencie os funcionários da empresa</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Link href="/funcionarios/desligados">
                <Button variant="outline">
                  Desligados
                </Button>
              </Link>
              <Link href="/funcionarios/novo">
                <Button variant="primary">
                  <Plus className="mr-2 h-4 w-4" />
                  Novo Funcionário
                </Button>
              </Link>
            </div>
          </div>

          {/* Filtros */}
          <FuncionariosFilters />

          {/* Lista */}
          <Suspense fallback={<FuncionariosListSkeleton />}>
            <FuncionariosList searchParams={params} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}

function FuncionariosListSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
              <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3" />
              </div>
              <div className="w-20 h-8 bg-gray-200 rounded animate-pulse" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
