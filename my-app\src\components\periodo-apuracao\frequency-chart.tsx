'use client';

import { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, TrendingDown } from 'lucide-react';

interface FrequencyData {
  dia: number;
  presentes: number;
  ausentes: number;
  atrasados: number;
  horasExtras: number;
  diaSemana: string;
}

interface FrequencyChartProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function FrequencyChart({ period }: FrequencyChartProps) {
  const [data, setData] = useState<FrequencyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'presenca' | 'horas'>('presenca');

  useEffect(() => {
    fetchFrequencyData();
  }, [period]);

  const fetchFrequencyData = async () => {
    try {
      setLoading(true);
      
      // Simulação de dados para o mês
      const diasNoMes = new Date(period.ano, period.mes, 0).getDate();
      const mockData: FrequencyData[] = [];
      
      const diasSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
      
      for (let dia = 1; dia <= diasNoMes; dia++) {
        const data = new Date(period.ano, period.mes - 1, dia);
        const diaSemana = diasSemana[data.getDay()];
        
        // Simular dados baseados no dia da semana
        const isWeekend = data.getDay() === 0 || data.getDay() === 6;
        const totalFuncionarios = 8;
        
        if (!isWeekend) {
          // Usar padrões determinísticos baseados no dia para evitar problemas de hidratação
          const seed = dia % 7;
          mockData.push({
            dia,
            presentes: totalFuncionarios - (seed % 2), // 7-8 presentes
            ausentes: seed % 2, // 0-1 ausentes
            atrasados: seed % 3, // 0-2 atrasados
            horasExtras: (seed % 10) + 2, // 2-12 horas extras
            diaSemana
          });
        } else {
          mockData.push({
            dia,
            presentes: 0,
            ausentes: 0,
            atrasados: 0,
            horasExtras: 0,
            diaSemana
          });
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData);
    } catch (error) {
      console.error('Erro ao buscar dados de frequência:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="h-80 bg-gray-200 rounded animate-pulse flex items-center justify-center">
        <BarChart3 className="h-8 w-8 text-gray-400" />
      </div>
    );
  }

  const maxValue = viewMode === 'presenca' 
    ? Math.max(...data.map(d => Math.max(d.presentes, d.ausentes, d.atrasados)))
    : Math.max(...data.map(d => d.horasExtras));

  const getBarHeight = (value: number) => {
    return maxValue > 0 ? (value / maxValue) * 100 : 0;
  };

  const totalPresentes = data.reduce((sum, d) => sum + d.presentes, 0);
  const totalAusentes = data.reduce((sum, d) => sum + d.ausentes, 0);
  const totalAtrasados = data.reduce((sum, d) => sum + d.atrasados, 0);
  const totalHorasExtras = data.reduce((sum, d) => sum + d.horasExtras, 0);

  const frequenciaGeral = totalPresentes + totalAusentes > 0 
    ? (totalPresentes / (totalPresentes + totalAusentes)) * 100 
    : 0;

  return (
    <div className="space-y-6">
      {/* Controles */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('presenca')}
            className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              viewMode === 'presenca'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Presença
          </button>
          <button
            onClick={() => setViewMode('horas')}
            className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              viewMode === 'horas'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Horas Extras
          </button>
        </div>

        <div className="flex items-center space-x-4 text-sm">
          {viewMode === 'presenca' ? (
            <>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded" />
                <span>Presentes</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded" />
                <span>Ausentes</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded" />
                <span>Atrasados</span>
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded" />
              <span>Horas Extras</span>
            </div>
          )}
        </div>
      </div>

      {/* Gráfico */}
      <div className="relative h-64 border-b border-l border-gray-200">
        <div className="absolute inset-0 flex items-end justify-between px-2">
          {data.map((item, index) => (
            <div key={index} className="flex flex-col items-center space-y-1 flex-1 max-w-[30px]">
              {/* Barras */}
              <div className="relative w-full flex flex-col items-center justify-end h-56">
                {viewMode === 'presenca' ? (
                  <div className="w-full flex flex-col items-center justify-end space-y-1">
                    {/* Presentes */}
                    {item.presentes > 0 && (
                      <div
                        className="w-full bg-green-500 rounded-t"
                        style={{ height: `${getBarHeight(item.presentes)}%` }}
                        title={`${item.presentes} presentes`}
                      />
                    )}
                    {/* Atrasados */}
                    {item.atrasados > 0 && (
                      <div
                        className="w-full bg-yellow-500"
                        style={{ height: `${getBarHeight(item.atrasados)}%` }}
                        title={`${item.atrasados} atrasados`}
                      />
                    )}
                    {/* Ausentes */}
                    {item.ausentes > 0 && (
                      <div
                        className="w-full bg-red-500 rounded-b"
                        style={{ height: `${getBarHeight(item.ausentes)}%` }}
                        title={`${item.ausentes} ausentes`}
                      />
                    )}
                  </div>
                ) : (
                  <div
                    className="w-full bg-blue-500 rounded-t"
                    style={{ height: `${getBarHeight(item.horasExtras)}%` }}
                    title={`${item.horasExtras}h extras`}
                  />
                )}
              </div>
              
              {/* Labels */}
              <div className="text-xs text-center">
                <div className="font-medium">{item.dia}</div>
                <div className="text-gray-500">{item.diaSemana}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Resumo */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-700">{totalPresentes}</div>
          <div className="text-sm text-green-600">Total Presentes</div>
        </div>
        
        <div className="text-center p-3 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-700">{totalAusentes}</div>
          <div className="text-sm text-red-600">Total Ausentes</div>
        </div>
        
        <div className="text-center p-3 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-700">{totalAtrasados}</div>
          <div className="text-sm text-yellow-600">Total Atrasados</div>
        </div>
        
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-700">{totalHorasExtras}h</div>
          <div className="text-sm text-blue-600">Horas Extras</div>
        </div>
      </div>

      {/* Indicador de Frequência Geral */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Frequência Geral do Período</span>
          <div className="flex items-center space-x-1">
            {frequenciaGeral >= 95 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm font-medium ${
              frequenciaGeral >= 95 ? 'text-green-600' : 'text-red-600'
            }`}>
              {frequenciaGeral.toFixed(1)}%
            </span>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              frequenciaGeral >= 95 ? 'bg-green-600' : 'bg-red-600'
            }`}
            style={{ width: `${frequenciaGeral}%` }}
          />
        </div>
      </div>
    </div>
  );
}

