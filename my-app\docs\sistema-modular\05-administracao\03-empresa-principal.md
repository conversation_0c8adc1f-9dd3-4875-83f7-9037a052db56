# 🏢 MÓDULO EMPRESA PRINCIPAL - Sistema RLPONTO

## 📋 Visão Geral
Módu<PERSON> responsável pela gestão da empresa principal, incluindo dados corporativos, configurações organizacionais, estrutura hierárquica e informações institucionais.

## 🎯 Funcionalidades
- Dados da empresa principal
- Configurações organizacionais
- Estrutura hierárquica (departamentos/setores)
- Gestão de cargos e funções
- Configurações de jornada por setor
- Feriados e calendário corporativo
- Políticas de ponto
- Integração com sistemas externos
- Backup e restore de configurações

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── empresa/
│           ├── page.tsx                    # Dashboard da empresa
│           ├── dados-gerais/
│           │   └── page.tsx                # Dados gerais da empresa
│           ├── estrutura/
│           │   └── page.tsx                # Estrutura organizacional
│           ├── cargos/
│           │   └── page.tsx                # Gestão de cargos
│           ├── jornadas/
│           │   └── page.tsx                # Jornadas de trabalho
│           ├── feriados/
│           │   └── page.tsx                # Calendário de feriados
│           └── components/
│               ├── company-form.tsx        # Formulário da empresa
│               ├── org-chart.tsx           # Organograma
│               ├── department-tree.tsx     # Árvore de departamentos
│               ├── holiday-calendar.tsx    # Calendário de feriados
│               └── workday-config.tsx      # Configuração de jornadas
├── components/
│   └── empresa/
│       ├── company-card.tsx               # Card da empresa
│       ├── department-card.tsx            # Card de departamento
│       ├── position-badge.tsx             # Badge de cargo
│       └── hierarchy-tree.tsx             # Árvore hierárquica
└── api/
    └── empresa/
        ├── route.ts                       # API principal
        ├── departamentos/
        │   └── route.ts                   # Gestão de departamentos
        ├── cargos/
        │   └── route.ts                   # Gestão de cargos
        ├── jornadas/
        │   └── route.ts                   # Jornadas de trabalho
        └── feriados/
            └── route.ts                   # Feriados
```

## 🔧 Implementação Técnica

### 🏢 Dashboard da Empresa (page.tsx)
```typescript
// app/(dashboard)/empresa/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Building2, Users, MapPin, Calendar, Settings, Edit, Eye, Plus } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Empresa Principal - RLPONTO',
  description: 'Gestão da empresa principal e estrutura organizacional',
};

interface EmpresaInfo {
  id: number;
  razaoSocial: string;
  nomeFantasia: string;
  cnpj: string;
  endereco: {
    logradouro: string;
    numero: string;
    complemento?: string;
    bairro: string;
    cidade: string;
    estado: string;
    cep: string;
  };
  contato: {
    telefone: string;
    email: string;
    site?: string;
  };
  totalFuncionarios: number;
  totalDepartamentos: number;
  totalCargos: number;
  ativo: boolean;
}

export default function EmpresaPage() {
  // Dados simulados da empresa
  const empresa: EmpresaInfo = {
    id: 1,
    razaoSocial: 'RLPONTO Tecnologia Ltda',
    nomeFantasia: 'RLPONTO',
    cnpj: '12.345.678/0001-90',
    endereco: {
      logradouro: 'Rua das Tecnologias',
      numero: '123',
      complemento: 'Sala 456',
      bairro: 'Centro Empresarial',
      cidade: 'São Paulo',
      estado: 'SP',
      cep: '01234-567',
    },
    contato: {
      telefone: '(11) 1234-5678',
      email: '<EMAIL>',
      site: 'https://www.rlponto.com.br',
    },
    totalFuncionarios: 155,
    totalDepartamentos: 8,
    totalCargos: 24,
    ativo: true,
  };

  const quickStats = [
    {
      title: 'Funcionários Ativos',
      value: empresa.totalFuncionarios,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Departamentos',
      value: empresa.totalDepartamentos,
      icon: Building2,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Cargos Cadastrados',
      value: empresa.totalCargos,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Feriados Configurados',
      value: 12,
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  const menuItems = [
    {
      title: 'Dados Gerais',
      description: 'Informações básicas da empresa',
      href: '/empresa/dados-gerais',
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Estrutura Organizacional',
      description: 'Departamentos e hierarquia',
      href: '/empresa/estrutura',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Cargos e Funções',
      description: 'Gestão de cargos e responsabilidades',
      href: '/empresa/cargos',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Jornadas de Trabalho',
      description: 'Configuração de horários por setor',
      href: '/empresa/jornadas',
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Feriados e Calendário',
      description: 'Calendário corporativo e feriados',
      href: '/empresa/feriados',
      icon: Calendar,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Configurações Avançadas',
      description: 'Políticas e integrações',
      href: '/empresa/configuracoes',
      icon: Settings,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Building2 className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Empresa Principal</h1>
            <p className="text-gray-600">Gestão da empresa e estrutura organizacional</p>
          </div>
        </div>
        <Button asChild>
          <Link href="/empresa/dados-gerais">
            <Edit className="h-4 w-4 mr-2" />
            Editar Empresa
          </Link>
        </Button>
      </div>

      {/* Informações da Empresa */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">{empresa.nomeFantasia}</CardTitle>
              <CardDescription>{empresa.razaoSocial}</CardDescription>
            </div>
            <Badge variant={empresa.ativo ? 'default' : 'secondary'} className="bg-green-100 text-green-800">
              {empresa.ativo ? 'Ativa' : 'Inativa'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Dados Básicos */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Dados Básicos</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p><span className="font-medium">CNPJ:</span> {empresa.cnpj}</p>
                <p><span className="font-medium">Telefone:</span> {empresa.contato.telefone}</p>
                <p><span className="font-medium">Email:</span> {empresa.contato.email}</p>
                {empresa.contato.site && (
                  <p><span className="font-medium">Site:</span> {empresa.contato.site}</p>
                )}
              </div>
            </div>

            {/* Endereço */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Endereço</h4>
              <div className="text-sm text-gray-600">
                <p>{empresa.endereco.logradouro}, {empresa.endereco.numero}</p>
                {empresa.endereco.complemento && <p>{empresa.endereco.complemento}</p>}
                <p>{empresa.endereco.bairro}</p>
                <p>{empresa.endereco.cidade} - {empresa.endereco.estado}</p>
                <p>CEP: {empresa.endereco.cep}</p>
              </div>
            </div>

            {/* Estatísticas */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Estatísticas</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p><span className="font-medium">Funcionários:</span> {empresa.totalFuncionarios}</p>
                <p><span className="font-medium">Departamentos:</span> {empresa.totalDepartamentos}</p>
                <p><span className="font-medium">Cargos:</span> {empresa.totalCargos}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <div className={`w-8 h-8 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Menu de Gestão */}
      <Card>
        <CardHeader>
          <CardTitle>Gestão da Empresa</CardTitle>
          <CardDescription>
            Acesse as diferentes seções de configuração da empresa
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <Card key={item.title} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg ${item.bgColor} flex items-center justify-center mb-4`}>
                      <Icon className={`h-6 w-6 ${item.color}`} />
                    </div>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href={item.href}>
                        Acessar
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Ações Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Novo Departamento</CardTitle>
            <CardDescription>Criar um novo departamento</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" asChild>
              <Link href="/empresa/estrutura/novo">
                <Plus className="h-4 w-4 mr-2" />
                Criar Departamento
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Novo Cargo</CardTitle>
            <CardDescription>Cadastrar um novo cargo</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/empresa/cargos/novo">
                <Plus className="h-4 w-4 mr-2" />
                Criar Cargo
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Organograma</CardTitle>
            <CardDescription>Visualizar estrutura organizacional</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/empresa/organograma">
                <Eye className="h-4 w-4 mr-2" />
                Ver Organograma
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

### 🏗️ Formulário da Empresa (company-form.tsx)
```typescript
// app/(dashboard)/empresa/components/company-form.tsx
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Save, Building2, MapPin, Phone, Mail } from 'lucide-react';

const empresaSchema = z.object({
  razaoSocial: z.string().min(1, 'Razão social é obrigatória'),
  nomeFantasia: z.string().min(1, 'Nome fantasia é obrigatório'),
  cnpj: z.string().min(14, 'CNPJ deve ter 14 dígitos'),
  inscricaoEstadual: z.string().optional(),
  inscricaoMunicipal: z.string().optional(),
  endereco: z.object({
    cep: z.string().min(8, 'CEP deve ter 8 dígitos'),
    logradouro: z.string().min(1, 'Logradouro é obrigatório'),
    numero: z.string().min(1, 'Número é obrigatório'),
    complemento: z.string().optional(),
    bairro: z.string().min(1, 'Bairro é obrigatório'),
    cidade: z.string().min(1, 'Cidade é obrigatória'),
    estado: z.string().min(2, 'Estado deve ter 2 caracteres'),
  }),
  contato: z.object({
    telefone: z.string().min(10, 'Telefone deve ter pelo menos 10 dígitos'),
    email: z.string().email('Email inválido'),
    site: z.string().url('URL inválida').optional().or(z.literal('')),
  }),
  configuracoes: z.object({
    ativo: z.boolean(),
    permitirHorasExtras: z.boolean(),
    toleranciaAtraso: z.number().min(0).max(60),
    toleranciaSaidaAntecipada: z.number().min(0).max(60),
  }),
});

type EmpresaFormData = z.infer<typeof empresaSchema>;

interface CompanyFormProps {
  initialData?: Partial<EmpresaFormData>;
  isEditing?: boolean;
}

export function CompanyForm({ initialData, isEditing = false }: CompanyFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty },
  } = useForm<EmpresaFormData>({
    resolver: zodResolver(empresaSchema),
    defaultValues: {
      razaoSocial: '',
      nomeFantasia: '',
      cnpj: '',
      inscricaoEstadual: '',
      inscricaoMunicipal: '',
      endereco: {
        cep: '',
        logradouro: '',
        numero: '',
        complemento: '',
        bairro: '',
        cidade: '',
        estado: '',
      },
      contato: {
        telefone: '',
        email: '',
        site: '',
      },
      configuracoes: {
        ativo: true,
        permitirHorasExtras: true,
        toleranciaAtraso: 10,
        toleranciaSaidaAntecipada: 10,
      },
      ...initialData,
    },
  });

  const ativo = watch('configuracoes.ativo');
  const permitirHorasExtras = watch('configuracoes.permitirHorasExtras');

  useEffect(() => {
    if (isEditing && !initialData) {
      loadEmpresaData();
    }
  }, [isEditing, initialData]);

  const loadEmpresaData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/empresa');
      if (response.ok) {
        const empresa = await response.json();
        Object.keys(empresa).forEach((key) => {
          setValue(key as keyof EmpresaFormData, empresa[key]);
        });
      }
    } catch (error) {
      console.error('Erro ao carregar dados da empresa:', error);
      setMessage({ type: 'error', text: 'Erro ao carregar dados da empresa' });
    } finally {
      setIsLoading(false);
    }
  };

  const buscarCep = async (cep: string) => {
    if (cep.length === 8) {
      try {
        const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
        const data = await response.json();
        
        if (!data.erro) {
          setValue('endereco.logradouro', data.logradouro);
          setValue('endereco.bairro', data.bairro);
          setValue('endereco.cidade', data.localidade);
          setValue('endereco.estado', data.uf);
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
      }
    }
  };

  const formatCnpj = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/^(\d{2})(\d)/, '$1.$2')
      .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
      .replace(/\.(\d{3})(\d)/, '.$1/$2')
      .replace(/(\d{4})(\d)/, '$1-$2')
      .substring(0, 18);
  };

  const formatCep = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/^(\d{5})(\d)/, '$1-$2')
      .substring(0, 9);
  };

  const formatTelefone = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/^(\d{2})(\d)/, '($1) $2')
      .replace(/(\d{4})(\d)/, '$1-$2')
      .substring(0, 15);
  };

  const onSubmit = async (data: EmpresaFormData) => {
    setIsSaving(true);
    setMessage(null);

    try {
      const url = isEditing ? '/api/empresa' : '/api/empresa';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao salvar empresa');
      }

      setMessage({ 
        type: 'success', 
        text: isEditing ? 'Empresa atualizada com sucesso!' : 'Empresa criada com sucesso!' 
      });
    } catch (error) {
      console.error('Erro ao salvar empresa:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Erro ao salvar empresa' 
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Carregando dados da empresa...</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Message Alert */}
      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="dados-basicos" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dados-basicos">Dados Básicos</TabsTrigger>
          <TabsTrigger value="endereco">Endereço</TabsTrigger>
          <TabsTrigger value="contato">Contato</TabsTrigger>
          <TabsTrigger value="configuracoes">Configurações</TabsTrigger>
        </TabsList>

        {/* Dados Básicos */}
        <TabsContent value="dados-basicos" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>Informações da Empresa</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="razaoSocial">Razão Social</Label>
                  <Input
                    id="razaoSocial"
                    {...register('razaoSocial')}
                    placeholder="Razão social da empresa"
                  />
                  {errors.razaoSocial && (
                    <p className="text-sm text-red-600 mt-1">{errors.razaoSocial.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="nomeFantasia">Nome Fantasia</Label>
                  <Input
                    id="nomeFantasia"
                    {...register('nomeFantasia')}
                    placeholder="Nome fantasia da empresa"
                  />
                  {errors.nomeFantasia && (
                    <p className="text-sm text-red-600 mt-1">{errors.nomeFantasia.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="cnpj">CNPJ</Label>
                  <Input
                    id="cnpj"
                    {...register('cnpj')}
                    placeholder="00.000.000/0000-00"
                    onChange={(e) => {
                      const formatted = formatCnpj(e.target.value);
                      setValue('cnpj', formatted);
                    }}
                  />
                  {errors.cnpj && (
                    <p className="text-sm text-red-600 mt-1">{errors.cnpj.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="inscricaoEstadual">Inscrição Estadual</Label>
                  <Input
                    id="inscricaoEstadual"
                    {...register('inscricaoEstadual')}
                    placeholder="Inscrição estadual (opcional)"
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="inscricaoMunicipal">Inscrição Municipal</Label>
                  <Input
                    id="inscricaoMunicipal"
                    {...register('inscricaoMunicipal')}
                    placeholder="Inscrição municipal (opcional)"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Endereço */}
        <TabsContent value="endereco" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Endereço da Empresa</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="cep">CEP</Label>
                  <Input
                    id="cep"
                    {...register('endereco.cep')}
                    placeholder="00000-000"
                    onChange={(e) => {
                      const formatted = formatCep(e.target.value);
                      setValue('endereco.cep', formatted);
                      if (formatted.length === 9) {
                        buscarCep(formatted.replace('-', ''));
                      }
                    }}
                  />
                  {errors.endereco?.cep && (
                    <p className="text-sm text-red-600 mt-1">{errors.endereco.cep.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="logradouro">Logradouro</Label>
                  <Input
                    id="logradouro"
                    {...register('endereco.logradouro')}
                    placeholder="Rua, Avenida, etc."
                  />
                  {errors.endereco?.logradouro && (
                    <p className="text-sm text-red-600 mt-1">{errors.endereco.logradouro.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="numero">Número</Label>
                  <Input
                    id="numero"
                    {...register('endereco.numero')}
                    placeholder="123"
                  />
                  {errors.endereco?.numero && (
                    <p className="text-sm text-red-600 mt-1">{errors.endereco.numero.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="complemento">Complemento</Label>
                  <Input
                    id="complemento"
                    {...register('endereco.complemento')}
                    placeholder="Sala, Andar, etc. (opcional)"
                  />
                </div>

                <div>
                  <Label htmlFor="bairro">Bairro</Label>
                  <Input
                    id="bairro"
                    {...register('endereco.bairro')}
                    placeholder="Nome do bairro"
                  />
                  {errors.endereco?.bairro && (
                    <p className="text-sm text-red-600 mt-1">{errors.endereco.bairro.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="cidade">Cidade</Label>
                  <Input
                    id="cidade"
                    {...register('endereco.cidade')}
                    placeholder="Nome da cidade"
                  />
                  {errors.endereco?.cidade && (
                    <p className="text-sm text-red-600 mt-1">{errors.endereco.cidade.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="estado">Estado</Label>
                  <Input
                    id="estado"
                    {...register('endereco.estado')}
                    placeholder="SP"
                    maxLength={2}
                  />
                  {errors.endereco?.estado && (
                    <p className="text-sm text-red-600 mt-1">{errors.endereco.estado.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contato */}
        <TabsContent value="contato" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Phone className="h-5 w-5" />
                <span>Informações de Contato</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="telefone">Telefone</Label>
                  <Input
                    id="telefone"
                    {...register('contato.telefone')}
                    placeholder="(11) 1234-5678"
                    onChange={(e) => {
                      const formatted = formatTelefone(e.target.value);
                      setValue('contato.telefone', formatted);
                    }}
                  />
                  {errors.contato?.telefone && (
                    <p className="text-sm text-red-600 mt-1">{errors.contato.telefone.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('contato.email')}
                    placeholder="<EMAIL>"
                  />
                  {errors.contato?.email && (
                    <p className="text-sm text-red-600 mt-1">{errors.contato.email.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="site">Site</Label>
                  <Input
                    id="site"
                    {...register('contato.site')}
                    placeholder="https://www.empresa.com.br (opcional)"
                  />
                  {errors.contato?.site && (
                    <p className="text-sm text-red-600 mt-1">{errors.contato.site.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configurações */}
        <TabsContent value="configuracoes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configurações Gerais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="ativo">Empresa Ativa</Label>
                  <p className="text-sm text-gray-600">Empresa está ativa no sistema</p>
                </div>
                <Switch
                  id="ativo"
                  checked={ativo}
                  onCheckedChange={(checked) => setValue('configuracoes.ativo', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="permitirHorasExtras">Permitir Horas Extras</Label>
                  <p className="text-sm text-gray-600">Funcionários podem fazer horas extras</p>
                </div>
                <Switch
                  id="permitirHorasExtras"
                  checked={permitirHorasExtras}
                  onCheckedChange={(checked) => setValue('configuracoes.permitirHorasExtras', checked)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="toleranciaAtraso">Tolerância para Atraso (minutos)</Label>
                  <Input
                    id="toleranciaAtraso"
                    type="number"
                    {...register('configuracoes.toleranciaAtraso', { valueAsNumber: true })}
                    min={0}
                    max={60}
                  />
                  {errors.configuracoes?.toleranciaAtraso && (
                    <p className="text-sm text-red-600 mt-1">{errors.configuracoes.toleranciaAtraso.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="toleranciaSaidaAntecipada">Tolerância para Saída Antecipada (minutos)</Label>
                  <Input
                    id="toleranciaSaidaAntecipada"
                    type="number"
                    {...register('configuracoes.toleranciaSaidaAntecipada', { valueAsNumber: true })}
                    min={0}
                    max={60}
                  />
                  {errors.configuracoes?.toleranciaSaidaAntecipada && (
                    <p className="text-sm text-red-600 mt-1">{errors.configuracoes.toleranciaSaidaAntecipada.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Botão de Salvar */}
      <div className="flex justify-end">
        <Button type="submit" disabled={!isDirty || isSaving}>
          {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          {isEditing ? 'Atualizar' : 'Salvar'} Empresa
        </Button>
      </div>
    </form>
  );
}
```

## 🔌 API Routes

### 🏢 API Principal da Empresa (route.ts)
```typescript
// app/api/empresa/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { empresaSchema } from '@/lib/validations/empresa';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const empresa = await prisma.empresa.findFirst({
      include: {
        _count: {
          select: {
            funcionarios: { where: { ativo: true } },
            departamentos: true,
            cargos: true,
          },
        },
      },
    });

    if (!empresa) {
      return NextResponse.json({ error: 'Empresa não encontrada' }, { status: 404 });
    }

    return NextResponse.json({
      ...empresa,
      totalFuncionarios: empresa._count.funcionarios,
      totalDepartamentos: empresa._count.departamentos,
      totalCargos: empresa._count.cargos,
    });
  } catch (error) {
    console.error('Erro ao buscar empresa:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = empresaSchema.parse(body);

    const empresa = await prisma.empresa.upsert({
      where: { id: 1 }, // Assumindo empresa única
      update: {
        ...validatedData,
        endereco: JSON.stringify(validatedData.endereco),
        contato: JSON.stringify(validatedData.contato),
        configuracoes: JSON.stringify(validatedData.configuracoes),
        atualizadoEm: new Date(),
        atualizadoPor: parseInt(session.user.id),
      },
      create: {
        ...validatedData,
        endereco: JSON.stringify(validatedData.endereco),
        contato: JSON.stringify(validatedData.contato),
        configuracoes: JSON.stringify(validatedData.configuracoes),
        criadoPor: parseInt(session.user.id),
      },
    });

    // Log da alteração
    await prisma.logAuditoria.create({
      data: {
        acao: 'empresa_atualizada',
        usuarioId: parseInt(session.user.id),
        detalhes: JSON.stringify({
          empresaId: empresa.id,
          alteracoes: validatedData,
        }),
        timestamp: new Date(),
      },
    });

    return NextResponse.json(empresa);
  } catch (error) {
    console.error('Erro ao salvar empresa:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Schema do Banco de Dados

### 🏢 Modelo Prisma
```prisma
model Empresa {
  id                  Int       @id @default(autoincrement())
  razaoSocial         String    @map("razao_social")
  nomeFantasia        String    @map("nome_fantasia")
  cnpj                String    @unique
  inscricaoEstadual   String?   @map("inscricao_estadual")
  inscricaoMunicipal  String?   @map("inscricao_municipal")
  endereco            String    // JSON
  contato             String    // JSON
  configuracoes       String    // JSON
  ativo               Boolean   @default(true)
  criadoEm            DateTime  @default(now()) @map("criado_em")
  atualizadoEm        DateTime  @updatedAt @map("atualizado_em")
  criadoPor           Int       @map("criado_por")
  atualizadoPor       Int?      @map("atualizado_por")

  // Relacionamentos
  criador             Usuario   @relation("EmpresaCriador", fields: [criadoPor], references: [id])
  atualizador         Usuario?  @relation("EmpresaAtualizador", fields: [atualizadoPor], references: [id])
  funcionarios        Funcionario[]
  departamentos       Departamento[]
  cargos              Cargo[]
  feriados            Feriado[]

  @@map("empresas")
}

model Departamento {
  id          Int       @id @default(autoincrement())
  nome        String
  descricao   String?
  codigo      String?   @unique
  empresaId   Int       @map("empresa_id")
  parentId    Int?      @map("parent_id") // Para hierarquia
  ativo       Boolean   @default(true)
  criadoEm    DateTime  @default(now()) @map("criado_em")

  // Relacionamentos
  empresa     Empresa   @relation(fields: [empresaId], references: [id])
  parent      Departamento? @relation("DepartamentoHierarquia", fields: [parentId], references: [id])
  filhos      Departamento[] @relation("DepartamentoHierarquia")
  funcionarios Funcionario[]

  @@map("departamentos")
}

model Cargo {
  id          Int       @id @default(autoincrement())
  nome        String
  descricao   String?
  nivel       String?   // 'junior', 'pleno', 'senior', 'coordenador', 'gerente', 'diretor'
  empresaId   Int       @map("empresa_id")
  ativo       Boolean   @default(true)
  criadoEm    DateTime  @default(now()) @map("criado_em")

  // Relacionamentos
  empresa     Empresa   @relation(fields: [empresaId], references: [id])
  funcionarios Funcionario[]

  @@map("cargos")
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Dashboard da empresa
- [ ] Formulário de dados gerais
- [ ] Gestão de departamentos
- [ ] Gestão de cargos
- [ ] Configuração de jornadas
- [ ] Calendário de feriados
- [ ] Organograma
- [ ] Estrutura hierárquica

### 🔧 Validações
- [ ] Validação de CNPJ
- [ ] Busca automática de CEP
- [ ] Validação de emails
- [ ] Controle de permissões
- [ ] Logs de auditoria

## 🚀 Próximos Passos
1. **Configurações de Ponto** - Parâmetros específicos
2. **Funcionários Desligados** - Gestão de ex-funcionários
3. **Integração com sistemas externos**
