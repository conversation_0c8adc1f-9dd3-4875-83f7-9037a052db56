import { Metadata } from 'next';
import { Settings, ArrowLeft, Play, Save, Download } from 'lucide-react';
import Link from 'next/link';
import { ReportBuilder } from '@/components/relatorios/report-builder';

export const metadata: Metadata = {
  title: 'Construtor de Relatórios - RLPONTO',
  description: 'Construa relatórios personalizados com interface visual intuitiva',
};

export default function ConstrutorRelatoriosPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/relatorios">
                <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <ArrowLeft className="h-4 w-4 mr-2 inline" />
                  Voltar
                </button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-600 rounded-lg">
                  <Settings className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Construtor de Relatórios</h1>
                  <p className="text-gray-600">Crie relatórios personalizados com interface visual</p>
                </div>
              </div>
            </div>
            
            {/* Actions */}
            <div className="flex items-center space-x-3">
              <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                <Save className="h-4 w-4 mr-2 inline" />
                Salvar
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                <Play className="h-4 w-4 mr-2 inline" />
                Executar
              </button>
              <button className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
                <Download className="h-4 w-4 mr-2 inline" />
                Exportar
              </button>
            </div>
          </div>

          {/* Descrição */}
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-purple-900 mb-3">Construtor Visual de Relatórios</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-purple-700">
              <div>
                <strong>🎨 Interface Visual</strong>
                <p className="mt-1">Arraste e solte campos para criar relatórios personalizados</p>
              </div>
              <div>
                <strong>📊 Múltiplas Visualizações</strong>
                <p className="mt-1">Tabelas, gráficos, cards e dashboards interativos</p>
              </div>
              <div>
                <strong>⚡ Tempo Real</strong>
                <p className="mt-1">Preview instantâneo das alterações conforme você constrói</p>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Settings className="w-4 h-4 text-blue-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Templates</p>
                  <p className="text-2xl font-semibold text-gray-900">12</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Campos Disponíveis</p>
                  <p className="text-2xl font-semibold text-gray-900">45</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-yellow-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Relatórios Salvos</p>
                  <p className="text-2xl font-semibold text-gray-900">8</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-purple-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Execuções Hoje</p>
                  <p className="text-2xl font-semibold text-gray-900">23</p>
                </div>
              </div>
            </div>
          </div>

          {/* Report Builder Component */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Construtor Visual</h2>
              <p className="text-sm text-gray-500">
                Arraste campos da biblioteca para a área de design e configure as visualizações
              </p>
            </div>
            <div className="p-6">
              <ReportBuilder />
            </div>
          </div>

          {/* Templates Rápidos */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Templates Rápidos</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer">
                <h4 className="font-medium text-gray-900 mb-2">Relatório de Presença</h4>
                <p className="text-sm text-gray-600 mb-3">Análise de frequência e pontualidade por funcionário</p>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">Frequência</span>
                  <span className="ml-2">5 campos</span>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer">
                <h4 className="font-medium text-gray-900 mb-2">Horas Trabalhadas</h4>
                <p className="text-sm text-gray-600 mb-3">Consolidado de horas normais e extras por período</p>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Horas</span>
                  <span className="ml-2">7 campos</span>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer">
                <h4 className="font-medium text-gray-900 mb-2">Dashboard Executivo</h4>
                <p className="text-sm text-gray-600 mb-3">KPIs e métricas principais para gestores</p>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">KPIs</span>
                  <span className="ml-2">12 campos</span>
                </div>
              </div>
            </div>
          </div>

          {/* Informações Adicionais */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Como Usar o Construtor</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Passos Básicos</h4>
                <ol className="space-y-1 list-decimal list-inside">
                  <li>Selecione um template ou comece do zero</li>
                  <li>Arraste campos da biblioteca para o design</li>
                  <li>Configure filtros e agrupamentos</li>
                  <li>Escolha o tipo de visualização</li>
                  <li>Execute o preview e ajuste conforme necessário</li>
                  <li>Salve e exporte o relatório</li>
                </ol>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Recursos Avançados</h4>
                <ul className="space-y-1">
                  <li>• Fórmulas personalizadas e cálculos</li>
                  <li>• Filtros dinâmicos e condicionais</li>
                  <li>• Agrupamentos e subtotais automáticos</li>
                  <li>• Gráficos interativos e dashboards</li>
                  <li>• Agendamento de execução automática</li>
                  <li>• Compartilhamento e colaboração</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
