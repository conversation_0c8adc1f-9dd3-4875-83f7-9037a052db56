# Application Configuration
NEXT_PUBLIC_APP_URL=http://************
NEXT_PUBLIC_APP_NAME="RLPONTO"

# API Configuration
NEXT_PUBLIC_API_URL=/api

# Ambiente
NODE_ENV=production
PORT=3000

# Database
DATABASE_URL="mysql://rlponto_user:RLPont<PERSON>@DB2024#@localhost:3306/rlponto"

# NextAuth.js
NEXTAUTH_SECRET="super-secret-key-change-in-production-2024"
NEXTAUTH_URL="http://************"

# Upload
UPLOAD_DIR="/opt/rlponto/shared/uploads"
MAX_FILE_SIZE=10485760

# Logs
LOG_LEVEL="info"
LOG_FILE="/var/log/rlponto/app.log"

# Email (configurar conforme necessário)
EMAIL_SERVER_HOST=""
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=""
EMAIL_SERVER_PASSWORD=""
EMAIL_FROM="<EMAIL>"
