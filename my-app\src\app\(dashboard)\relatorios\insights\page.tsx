import { Metadata } from 'next';
import { <PERSON><PERSON><PERSON><PERSON>p, ArrowLeft, Brain, <PERSON>ert<PERSON><PERSON>gle, CheckCircle, Info } from 'lucide-react';
import Link from 'next/link';
import { InsightsEngine } from '@/components/relatorios/insights-engine';

export const metadata: Metadata = {
  title: 'Insights Automáticos - RLPONTO',
  description: 'Análises inteligentes e insights automáticos baseados em IA',
};

export default function InsightsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/relatorios">
                <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <ArrowLeft className="h-4 w-4 mr-2 inline" />
                  Voltar
                </button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-yellow-600 rounded-lg">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Insights Automáticos</h1>
                  <p className="text-gray-600">Análises inteligentes e recomendações baseadas em IA</p>
                </div>
              </div>
            </div>
          </div>

          {/* Descrição */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">Inteligência Artificial para RH</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-yellow-700">
              <div>
                <strong>🧠 Análise Preditiva</strong>
                <p className="mt-1">Identifica padrões e tendências antes que se tornem problemas</p>
              </div>
              <div>
                <strong>🎯 Recomendações Personalizadas</strong>
                <p className="mt-1">Sugestões específicas para cada situação e funcionário</p>
              </div>
              <div>
                <strong>⚡ Alertas Inteligentes</strong>
                <p className="mt-1">Notificações proativas sobre situações que requerem atenção</p>
              </div>
            </div>
          </div>

          {/* Estatísticas de Insights */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Brain className="w-4 h-4 text-yellow-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Insights Gerados</p>
                  <p className="text-2xl font-semibold text-gray-900">47</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="w-4 h-4 text-red-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Alertas Críticos</p>
                  <p className="text-2xl font-semibold text-gray-900">3</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Ações Implementadas</p>
                  <p className="text-2xl font-semibold text-gray-900">12</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-blue-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Precisão do Modelo</p>
                  <p className="text-2xl font-semibold text-gray-900">94.2%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Insights Recentes */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Insights Recentes</h2>
              <p className="text-sm text-gray-500">
                Análises automáticas baseadas nos dados mais recentes
              </p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* Insight Crítico */}
                <div className="border-l-4 border-red-400 bg-red-50 p-4 rounded-r-lg">
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-red-800">
                        Aumento significativo de atrasos no Departamento de Produção
                      </h3>
                      <p className="text-sm text-red-700 mt-1">
                        Detectado aumento de 35% nos atrasos nos últimos 7 dias. Principais causas identificadas: 
                        transporte público e horário de entrada. Recomenda-se revisar política de flexibilidade.
                      </p>
                      <div className="mt-3 flex items-center space-x-4 text-xs text-red-600">
                        <span>🎯 Confiança: 92%</span>
                        <span>📅 Detectado: Hoje</span>
                        <span>👥 Afeta: 8 funcionários</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Insight de Oportunidade */}
                <div className="border-l-4 border-green-400 bg-green-50 p-4 rounded-r-lg">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-green-800">
                        Oportunidade de otimização de horas extras
                      </h3>
                      <p className="text-sm text-green-700 mt-1">
                        Análise mostra que 60% das horas extras no TI ocorrem às sextas-feiras. 
                        Redistribuindo tarefas, é possível reduzir custos em até R$ 3.200/mês.
                      </p>
                      <div className="mt-3 flex items-center space-x-4 text-xs text-green-600">
                        <span>💰 Economia potencial: R$ 3.200/mês</span>
                        <span>📊 Baseado em: 3 meses de dados</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Insight Informativo */}
                <div className="border-l-4 border-blue-400 bg-blue-50 p-4 rounded-r-lg">
                  <div className="flex items-start">
                    <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-blue-800">
                        Padrão sazonal identificado na frequência
                      </h3>
                      <p className="text-sm text-blue-700 mt-1">
                        Histórico mostra redução de 12% na pontualidade durante meses de inverno. 
                        Sugestão: implementar horário flexível de maio a agosto.
                      </p>
                      <div className="mt-3 flex items-center space-x-4 text-xs text-blue-600">
                        <span>📈 Padrão: Sazonal</span>
                        <span>🔄 Recorrência: Anual</span>
                        <span>📋 Ação: Planejamento</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Insight de Tendência */}
                <div className="border-l-4 border-yellow-400 bg-yellow-50 p-4 rounded-r-lg">
                  <div className="flex items-start">
                    <TrendingUp className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Melhoria contínua na pontualidade geral
                      </h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Tendência positiva: pontualidade aumentou 8% nos últimos 3 meses. 
                        Principais fatores: campanhas de conscientização e sistema de reconhecimento.
                      </p>
                      <div className="mt-3 flex items-center space-x-4 text-xs text-yellow-600">
                        <span>📈 Tendência: +8% em 3 meses</span>
                        <span>🏆 Fator principal: Campanhas</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Engine de Insights */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Engine de Análise</h2>
              <p className="text-sm text-gray-500">
                Configure e monitore o sistema de insights automáticos
              </p>
            </div>
            <div className="p-6">
              <InsightsEngine />
            </div>
          </div>

          {/* Configurações de IA */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Configurações de Inteligência Artificial</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Modelos Ativos</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Detecção de Anomalias</p>
                      <p className="text-sm text-gray-600">Identifica padrões anômalos em tempo real</p>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-sm text-green-600">Ativo</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Análise Preditiva</p>
                      <p className="text-sm text-gray-600">Prevê tendências e comportamentos futuros</p>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-sm text-green-600">Ativo</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Otimização de Recursos</p>
                      <p className="text-sm text-gray-600">Sugere melhorias na alocação de recursos</p>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                      <span className="text-sm text-yellow-600">Treinando</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Métricas de Performance</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Precisão Geral</span>
                    <span className="font-medium">94.2%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Insights Processados</span>
                    <span className="font-medium">1,247</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Tempo Médio de Análise</span>
                    <span className="font-medium">2.3s</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Última Atualização</span>
                    <span className="font-medium">Há 5 min</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
