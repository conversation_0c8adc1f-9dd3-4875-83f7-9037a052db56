'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button, Input, SearchIcon } from '@/components/ui';
import { Filter, X } from 'lucide-react';

export function FuncionariosFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [search, setSearch] = useState(searchParams.get('search') || '');
  const [setor, setSetor] = useState(searchParams.get('setor') || '');
  const [status, setStatus] = useState(searchParams.get('status') || '');

  const handleSearch = () => {
    const params = new URLSearchParams();
    
    if (search) params.set('search', search);
    if (setor) params.set('setor', setor);
    if (status) params.set('status', status);
    
    router.push(`/funcionarios?${params.toString()}`);
  };

  const handleClearFilters = () => {
    setSearch('');
    setSetor('');
    setStatus('');
    router.push('/funcionarios');
  };

  const hasActiveFilters = search || setor || status;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center space-x-4">
        {/* Busca por Nome/CPF */}
        <div className="flex-1">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2" />
            <Input
              placeholder="Buscar por nome, CPF ou matrícula..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Filtro por Setor */}
        <div className="w-48">
          <select
            value={setor}
            onChange={(e) => setSetor(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
            style={{ color: setor ? '#111827' : '#6B7280' }}
          >
            <option value="" style={{ color: '#6B7280' }}>Todos os setores</option>
            <option value="administracao">Administração</option>
            <option value="producao">Produção</option>
            <option value="vendas">Vendas</option>
            <option value="rh">Recursos Humanos</option>
            <option value="ti">Tecnologia</option>
            <option value="financeiro">Financeiro</option>
          </select>
        </div>

        {/* Filtro por Status */}
        <div className="w-40">
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
            style={{ color: status ? '#111827' : '#6B7280' }}
          >
            <option value="" style={{ color: '#6B7280' }}>Todos os status</option>
            <option value="ativo">Ativo</option>
            <option value="inativo">Inativo</option>
            <option value="ferias">Férias</option>
            <option value="licenca">Licença</option>
          </select>
        </div>

        {/* Botões de Ação */}
        <div className="flex space-x-2">
          <Button onClick={handleSearch} variant="primary">
            <Filter className="mr-2 h-4 w-4" />
            Filtrar
          </Button>
          
          {hasActiveFilters && (
            <Button onClick={handleClearFilters} variant="outline">
              <X className="mr-2 h-4 w-4" />
              Limpar
            </Button>
          )}
        </div>
      </div>

      {/* Filtros Ativos */}
      {hasActiveFilters && (
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-600">Filtros ativos:</span>
          {search && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Busca: {search}
            </span>
          )}
          {setor && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Setor: {setor}
            </span>
          )}
          {status && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Status: {status}
            </span>
          )}
        </div>
      )}
    </div>
  );
}

