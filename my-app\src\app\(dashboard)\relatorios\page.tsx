import { Metadata } from 'next';
import { Suspense } from 'react';
import { ReportTemplates } from '@/components/relatorios/report-templates';
import { RecentReports } from '@/components/relatorios/recent-reports';
import { QuickStats } from '@/components/relatorios/quick-stats';
import { FileText, ArrowLeft, Settings, Clock, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Relatórios - RLPONTO',
  description: 'Relatórios detalhados do sistema de ponto',
};

export default function RelatoriosPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-600 rounded-lg">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Relatórios</h1>
                  <p className="text-gray-600">Gere relatórios detalhados do sistema de ponto</p>
                </div>
              </div>
            </div>
          </div>

          {/* Estatísticas Rápidas */}
          <Suspense fallback={<QuickStatsSkeleton />}>
            <QuickStats />
          </Suspense>

          {/* Grid Principal */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Templates de Relatórios */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Templates de Relatórios
                  </h2>
                  <p className="text-sm text-gray-600">
                    Escolha um template para gerar seu relatório
                  </p>
                </div>
                <div className="p-6">
                  <Suspense fallback={<TemplatesSkeleton />}>
                    <ReportTemplates />
                  </Suspense>
                </div>
              </div>
            </div>

            {/* Relatórios Recentes */}
            <div>
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Relatórios Recentes
                  </h2>
                </div>
                <div className="p-6">
                  <Suspense fallback={<RecentSkeleton />}>
                    <RecentReports />
                  </Suspense>
                </div>
              </div>
            </div>
          </div>

          {/* Ações Rápidas */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Link href="/relatorios/funcionario">
                <div className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Relatório Individual</h4>
                      <p className="text-sm text-gray-600">Por funcionário específico</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/relatorios/periodo">
                <div className="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <FileText className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Relatório por Período</h4>
                      <p className="text-sm text-gray-600">Consolidado mensal</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/relatorios/analiticos">
                <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <FileText className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Relatórios Analíticos</h4>
                      <p className="text-sm text-gray-600">Análises e gráficos</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/relatorios/construtor">
                <div className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-indigo-100 rounded-lg">
                      <Settings className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Construtor Visual</h4>
                      <p className="text-sm text-gray-600">Relatórios personalizados</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/relatorios/agendamentos">
                <div className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Clock className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Agendamentos</h4>
                      <p className="text-sm text-gray-600">Relatórios automáticos</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/relatorios/insights">
                <div className="p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <TrendingUp className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Insights</h4>
                      <p className="text-sm text-gray-600">Análises inteligentes</p>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Skeletons para loading states
function QuickStatsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-white rounded-lg shadow p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded" />
              <div className="w-16 h-4 bg-gray-200 rounded" />
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded mb-2" />
            <div className="w-24 h-4 bg-gray-200 rounded" />
          </div>
        </div>
      ))}
    </div>
  );
}

function TemplatesSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="p-4 border rounded-lg animate-pulse">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-10 h-10 bg-gray-200 rounded-lg" />
            <div className="flex-1">
              <div className="w-3/4 h-4 bg-gray-200 rounded mb-2" />
              <div className="w-1/2 h-3 bg-gray-200 rounded" />
            </div>
          </div>
          <div className="w-20 h-6 bg-gray-200 rounded" />
        </div>
      ))}
    </div>
  );
}

function RecentSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3 border rounded animate-pulse">
          <div className="w-8 h-8 bg-gray-200 rounded" />
          <div className="flex-1">
            <div className="w-3/4 h-4 bg-gray-200 rounded mb-2" />
            <div className="w-1/2 h-3 bg-gray-200 rounded" />
          </div>
          <div className="w-16 h-6 bg-gray-200 rounded" />
        </div>
      ))}
    </div>
  );
}

