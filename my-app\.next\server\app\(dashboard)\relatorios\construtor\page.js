(()=>{var a={};a.id=567,a.ids=[567],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3567:(a,b,c)=>{"use strict";var d=c(43210);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},4082:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["relatorios",{children:["construtor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,92038)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\construtor\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\construtor\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/relatorios/construtor/page",pathname:"/relatorios/construtor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/relatorios/construtor/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6895:(a,b,c)=>{"use strict";c(3567)},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29052:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,30389))},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30389:(a,b,c)=>{"use strict";c.d(b,{ReportBuilder:()=>d$});var d=c(60687),e=c(43210),f=c.n(e),g=c(51215),h=c.n(g);function i(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}var j="function"==typeof Symbol&&Symbol.observable||"@@observable",k=()=>Math.random().toString(36).substring(7).split("").join("."),l={INIT:`@@redux/INIT${k()}`,REPLACE:`@@redux/REPLACE${k()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${k()}`};function m(a,b){return function(...c){return b(a.apply(this,c))}}function n(a,b){if("function"==typeof a)return m(a,b);if("object"!=typeof a||null===a)throw Error(i(16));let c={};for(let d in a){let e=a[d];"function"==typeof e&&(c[d]=m(e,b))}return c}function o(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}c(6895);var p=Symbol.for(e.version.startsWith("19")?"react.transitional.element":"react.element"),q=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),t=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),v=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),y=Symbol.for("react.suspense_list"),z=Symbol.for("react.memo"),A=Symbol.for("react.lazy");function B(a){return function(b){let c=a(b);function d(){return c}return d.dependsOnOwnProps=!1,d}}function C(a){return a.dependsOnOwnProps?!!a.dependsOnOwnProps:1!==a.length}function D(a,b){return function(b,{displayName:c}){let d=function(a,b){return d.dependsOnOwnProps?d.mapToProps(a,b):d.mapToProps(a,void 0)};return d.dependsOnOwnProps=!0,d.mapToProps=function(b,c){d.mapToProps=a,d.dependsOnOwnProps=C(a);let e=d(b,c);return"function"==typeof e&&(d.mapToProps=e,d.dependsOnOwnProps=C(e),e=d(b,c)),e},d}}function E(a,b){return(c,d)=>{throw Error(`Invalid value of type ${typeof a} for ${b} argument when connecting component ${d.wrappedComponentName}.`)}}function F(a,b,c){return{...c,...a,...b}}var G={notify(){},get:()=>[]};function H(a,b){let c,d=G,e=0,f=!1;function g(){j.onStateChange&&j.onStateChange()}function h(){if(e++,!c){let e,f;c=b?b.addNestedSub(g):a.subscribe(g),e=null,f=null,d={clear(){e=null,f=null},notify(){let a=e;for(;a;)a.callback(),a=a.next},get(){let a=[],b=e;for(;b;)a.push(b),b=b.next;return a},subscribe(a){let b=!0,c=f={callback:a,next:null,prev:f};return c.prev?c.prev.next=c:e=c,function(){b&&null!==e&&(b=!1,c.next?c.next.prev=c.prev:f=c.prev,c.prev?c.prev.next=c.next:e=c.next)}}}}}function i(){e--,c&&0===e&&(c(),c=void 0,d.clear(),d=G)}let j={addNestedSub:function(a){h();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),i())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:g,isSubscribed:function(){return f},trySubscribe:function(){f||(f=!0,h())},tryUnsubscribe:function(){f&&(f=!1,i())},getListeners:()=>d};return j}var I="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,J="undefined"!=typeof navigator&&"ReactNative"===navigator.product,K=I||J?e.useLayoutEffect:e.useEffect;function L(a,b){return a===b?0!==a||0!==b||1/a==1/b:a!=a&&b!=b}function M(a,b){if(L(a,b))return!0;if("object"!=typeof a||null===a||"object"!=typeof b||null===b)return!1;let c=Object.keys(a),d=Object.keys(b);if(c.length!==d.length)return!1;for(let d=0;d<c.length;d++)if(!Object.prototype.hasOwnProperty.call(b,c[d])||!L(a[c[d]],b[c[d]]))return!1;return!0}var N={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},O={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},P={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Q={[w]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[z]:P};function R(a){return function(a){if("object"==typeof a&&null!==a){let{$$typeof:b}=a;switch(b){case p:switch(a=a.type){case r:case t:case s:case x:case y:return a;default:switch(a=a&&a.$$typeof){case v:case w:case A:case z:case u:return a;default:return b}}case q:return b}}}(a)===z?P:Q[a.$$typeof]||N}var S=Object.defineProperty,T=Object.getOwnPropertyNames,U=Object.getOwnPropertySymbols,V=Object.getOwnPropertyDescriptor,W=Object.getPrototypeOf,X=Object.prototype;function Y(a,b){if("string"!=typeof b){if(X){let c=W(b);c&&c!==X&&Y(a,c)}let c=T(b);U&&(c=c.concat(U(b)));let d=R(a),e=R(b);for(let f=0;f<c.length;++f){let g=c[f];if(!O[g]&&!(e&&e[g])&&!(d&&d[g])){let c=V(b,g);try{S(a,g,c)}catch(a){}}}}return a}var Z=Symbol.for("react-redux-context"),$="undefined"!=typeof globalThis?globalThis:{},_=function(){if(!e.createContext)return{};let a=$[Z]??=new Map,b=a.get(e.createContext);return b||(b=e.createContext(null),a.set(e.createContext,b)),b}(),aa=[null,null];function ab(a,b,c,d,e,f){a.current=d,c.current=!1,e.current&&(e.current=null,f())}function ac(a,b){return a===b}var ad=function(a,b,c,{pure:d,areStatesEqual:f=ac,areOwnPropsEqual:g=M,areStatePropsEqual:h=M,areMergedPropsEqual:i=M,forwardRef:j=!1,context:k=_}={}){let l=a?"function"==typeof a?D(a,"mapStateToProps"):E(a,"mapStateToProps"):B(()=>({})),m=b&&"object"==typeof b?B(a=>(function(a,b){let c={};for(let d in a){let e=a[d];"function"==typeof e&&(c[d]=(...a)=>b(e(...a)))}return c})(b,a)):b?"function"==typeof b?D(b,"mapDispatchToProps"):E(b,"mapDispatchToProps"):B(a=>({dispatch:a})),n=c?"function"==typeof c?function(a,{displayName:b,areMergedPropsEqual:d}){let e,f=!1;return function(a,b,g){let h=c(a,b,g);return f?d(h,e)||(e=h):(f=!0,e=h),e}}:E(c,"mergeProps"):()=>F,o=!!a;return a=>{let b=a.displayName||a.name||"Component",c=`Connect(${b})`,d={shouldHandleStateChanges:o,displayName:c,wrappedComponentName:b,WrappedComponent:a,initMapStateToProps:l,initMapDispatchToProps:m,initMergeProps:n,areStatesEqual:f,areStatePropsEqual:h,areOwnPropsEqual:g,areMergedPropsEqual:i};function p(b){var c;let f,[g,h,i]=e.useMemo(()=>{let{reactReduxForwardedRef:a,...c}=b;return[b.context,a,c]},[b]),j=e.useMemo(()=>(g?.Consumer,k),[g,k]),l=e.useContext(j),m=!!b.store&&!!b.store.getState&&!!b.store.dispatch,n=!!l&&!!l.store,p=m?b.store:l.store,q=n?l.getServerState:p.getState,r=e.useMemo(()=>(function(a,{initMapStateToProps:b,initMapDispatchToProps:c,initMergeProps:d,...e}){let f=b(a,e),g=c(a,e);return function(a,b,c,d,{areStatesEqual:e,areOwnPropsEqual:f,areStatePropsEqual:g}){let h,i,j,k,l,m=!1;return function(n,o){return m?function(m,n){let o=!f(n,i),p=!e(m,h,n,i);if(h=m,i=n,o&&p)return j=a(h,i),b.dependsOnOwnProps&&(k=b(d,i)),l=c(j,k,i);if(o)return a.dependsOnOwnProps&&(j=a(h,i)),b.dependsOnOwnProps&&(k=b(d,i)),l=c(j,k,i);if(p){let b=a(h,i),d=!g(b,j);return j=b,d&&(l=c(j,k,i)),l}return l}(n,o):(j=a(h=n,i=o),k=b(d,i),l=c(j,k,i),m=!0,l)}}(f,g,d(a,e),a,e)})(p.dispatch,d),[p]),[s,t]=e.useMemo(()=>{if(!o)return aa;let a=H(p,m?void 0:l.subscription),b=a.notifyNestedSubs.bind(a);return[a,b]},[p,m,l]),u=e.useMemo(()=>m?l:{...l,subscription:s},[m,l,s]),v=e.useRef(void 0),w=e.useRef(i),x=e.useRef(void 0),y=e.useRef(!1),z=e.useRef(!1),A=e.useRef(void 0);K(()=>(z.current=!0,()=>{z.current=!1}),[]);let B=e.useMemo(()=>()=>x.current&&i===w.current?x.current:r(p.getState(),i),[p,i]),C=e.useMemo(()=>a=>{if(!s)return()=>{};if(!o)return()=>{};let b=!1,c=null,d=()=>{let d,e;if(b||!z.current)return;let f=p.getState();try{d=r(f,w.current)}catch(a){e=a,c=a}e||(c=null),d===v.current?y.current||t():(v.current=d,x.current=d,y.current=!0,a())};return s.onStateChange=d,s.trySubscribe(),d(),()=>{if(b=!0,s.tryUnsubscribe(),s.onStateChange=null,c)throw c}},[s]);c=[w,v,y,i,x,t],K(()=>ab(...c),void 0);try{f=e.useSyncExternalStore(C,B,q?()=>r(q(),i):B)}catch(a){throw A.current&&(a.message+=`
The error may be correlated with this previous error:
${A.current.stack}

`),a}K(()=>{A.current=void 0,x.current=void 0,v.current=f});let D=e.useMemo(()=>e.createElement(a,{...f,ref:h}),[h,a,f]);return e.useMemo(()=>o?e.createElement(j.Provider,{value:u},D):D,[j,D,u])}let q=e.memo(p);if(q.WrappedComponent=a,q.displayName=p.displayName=c,j){let b=e.forwardRef(function(a,b){return e.createElement(q,{...a,reactReduxForwardedRef:b})});return b.displayName=c,b.WrappedComponent=a,Y(b,a)}return Y(q,a)}},ae=function(a){let{children:b,context:c,serverState:d,store:f}=a,g=e.useMemo(()=>{let a=H(f);return{store:f,subscription:a,getServerState:d?()=>d:void 0}},[f,d]),h=e.useMemo(()=>f.getState(),[f]);return K(()=>{let{subscription:a}=g;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),h!==f.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[g,h]),e.createElement((c||_).Provider,{value:g},b)},af=function(a){var b=a.top,c=a.right,d=a.bottom,e=a.left;return{top:b,right:c,bottom:d,left:e,width:c-e,height:d-b,x:e,y:b,center:{x:(c+e)/2,y:(d+b)/2}}},ag=function(a,b){return{top:a.top-b.top,left:a.left-b.left,bottom:a.bottom+b.bottom,right:a.right+b.right}},ah=function(a,b){return{top:a.top+b.top,left:a.left+b.left,bottom:a.bottom-b.bottom,right:a.right-b.right}},ai={top:0,right:0,bottom:0,left:0},aj=function(a){var b=a.borderBox,c=a.margin,d=void 0===c?ai:c,e=a.border,f=void 0===e?ai:e,g=a.padding,h=void 0===g?ai:g,i=af(ag(b,d)),j=af(ah(b,f)),k=af(ah(j,h));return{marginBox:i,borderBox:af(b),paddingBox:j,contentBox:k,margin:d,border:f,padding:h}},ak=function(a){var b=a.slice(0,-2);if("px"!==a.slice(-2))return 0;var c=Number(b);return isNaN(c)&&function(a,b){if(!a)throw Error("Invariant failed")}(!1),c},al=function(a,b){var c=a.borderBox,d=a.border,e=a.margin,f=a.padding;return aj({borderBox:{top:c.top+b.y,left:c.left+b.x,bottom:c.bottom+b.y,right:c.right+b.x},border:d,margin:e,padding:f})},am=function(a,b){return void 0===b&&(b={x:window.pageXOffset,y:window.pageYOffset}),al(a,b)},an=function(a,b){return aj({borderBox:a,margin:{top:ak(b.marginTop),right:ak(b.marginRight),bottom:ak(b.marginBottom),left:ak(b.marginLeft)},padding:{top:ak(b.paddingTop),right:ak(b.paddingRight),bottom:ak(b.paddingBottom),left:ak(b.paddingLeft)},border:{top:ak(b.borderTopWidth),right:ak(b.borderRightWidth),bottom:ak(b.borderBottomWidth),left:ak(b.borderLeftWidth)}})},ao=function(a){return an(a.getBoundingClientRect(),window.getComputedStyle(a))};let ap=function(a){var b=[],c=null,d=function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];b=e,c||(c=requestAnimationFrame(function(){c=null,a.apply(void 0,b)}))};return d.cancel=function(){c&&(cancelAnimationFrame(c),c=null)},d};function aq(){return(aq=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ar(a,b){}ar.bind(null,"warn");ar.bind(null,"error");function as(){}function at(a,b,c){let d=b.map(b=>{var d;let e=(d=b.options,{...c,...d});return a.addEventListener(b.eventName,b.fn,e),function(){a.removeEventListener(b.eventName,b.fn,e)}});return function(){d.forEach(a=>{a()})}}class au extends Error{}function av(a,b){throw new au("Invariant failed")}au.prototype.toString=function(){return this.message};class aw extends f().Component{constructor(...a){super(...a),this.callbacks=null,this.unbind=as,this.onWindowError=a=>{let b=this.getCallbacks();b.isDragging()&&b.tryAbort(),a.error instanceof au&&a.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=a=>{this.callbacks=a}}componentDidMount(){this.unbind=at(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(a){if(a instanceof au)return void this.setState({});throw a}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let ax=(a,b)=>{let c=a.droppableId===b.droppableId,d=a.index+1,e=b.index+1;return c?`
      You have moved the item from position ${d}
      to position ${e}
    `:`
    You have moved the item from position ${d}
    in list ${a.droppableId}
    to list ${b.droppableId}
    in position ${e}
  `},ay=(a,b,c)=>b.droppableId===c.droppableId?`
      The item ${a}
      has been combined with ${c.draggableId}`:`
      The item ${a}
      in list ${b.droppableId}
      has been combined with ${c.draggableId}
      in list ${c.droppableId}
    `,az=a=>`
  The item has returned to its starting position
  of ${a.index+1}
`,aA={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:a=>`
  You have lifted an item in position ${a.source.index+1}
`,onDragUpdate:a=>{let b=a.destination;if(b)return ax(a.source,b);let c=a.combine;return c?ay(a.draggableId,a.source,c):"You are over an area that cannot be dropped on"},onDragEnd:a=>{if("CANCEL"===a.reason)return`
      Movement cancelled.
      ${az(a.source)}
    `;let b=a.destination,c=a.combine;return b?`
      You have dropped the item.
      ${ax(a.source,b)}
    `:c?`
      You have dropped the item.
      ${ay(a.draggableId,a.source,c)}
    `:`
    The item has been dropped while not over a drop area.
    ${az(a.source)}
  `}};function aB(a,b){if(a.length!==b.length)return!1;for(let e=0;e<a.length;e++){var c,d;if(!((c=a[e])===(d=b[e])||Number.isNaN(c)&&Number.isNaN(d))&&1)return!1}return!0}function aC(a,b){let c=(0,e.useState)(()=>({inputs:b,result:a()}))[0],d=(0,e.useRef)(!0),f=(0,e.useRef)(c),g=d.current||b&&f.current.inputs&&aB(b,f.current.inputs)?f.current:{inputs:b,result:a()};return(0,e.useEffect)(()=>{d.current=!1,f.current=g},[g]),g.result}function aD(a,b){return aC(()=>a,b)}let aE={x:0,y:0},aF=(a,b)=>({x:a.x+b.x,y:a.y+b.y}),aG=(a,b)=>({x:a.x-b.x,y:a.y-b.y}),aH=(a,b)=>a.x===b.x&&a.y===b.y,aI=a=>({x:0!==a.x?-a.x:0,y:0!==a.y?-a.y:0}),aJ=(a,b,c=0)=>"x"===a?{x:b,y:c}:{x:c,y:b},aK=(a,b)=>Math.sqrt((b.x-a.x)**2+(b.y-a.y)**2),aL=(a,b)=>Math.min(...b.map(b=>aK(a,b))),aM=a=>b=>({x:a(b.x),y:a(b.y)}),aN=(a,b)=>({top:a.top+b.y,left:a.left+b.x,bottom:a.bottom+b.y,right:a.right+b.x}),aO=a=>[{x:a.left,y:a.top},{x:a.right,y:a.top},{x:a.left,y:a.bottom},{x:a.right,y:a.bottom}];var aP=({page:a,withPlaceholder:b,axis:c,frame:d})=>{let e=((a,b)=>b&&b.shouldClipSubject?((a,b)=>{let c=af({top:Math.max(b.top,a.top),right:Math.min(b.right,a.right),bottom:Math.min(b.bottom,a.bottom),left:Math.max(b.left,a.left)});return c.width<=0||c.height<=0?null:c})(b.pageMarginBox,a):af(a))(((a,b,c)=>c&&c.increasedBy?{...a,[b.end]:a[b.end]+c.increasedBy[b.line]}:a)(((a,b)=>b?aN(a,b.scroll.diff.displacement):a)(a.marginBox,d),c,b),d);return{page:a,withPlaceholder:b,active:e}},aQ=(a,b)=>{a.frame||av();let c=a.frame,d=aG(b,c.scroll.initial),e=aI(d),f={...c,scroll:{initial:c.scroll.initial,current:b,diff:{value:d,displacement:e},max:c.scroll.max}},g=aP({page:a.subject.page,withPlaceholder:a.subject.withPlaceholder,axis:a.axis,frame:f});return{...a,frame:f,subject:g}};function aR(a,b=aB){let c=null;function d(...e){if(c&&c.lastThis===this&&b(e,c.lastArgs))return c.lastResult;let f=a.apply(this,e);return c={lastResult:f,lastArgs:e,lastThis:this},f}return d.clear=function(){c=null},d}let aS=aR(a=>a.reduce((a,b)=>(a[b.descriptor.id]=b,a),{})),aT=aR(a=>a.reduce((a,b)=>(a[b.descriptor.id]=b,a),{})),aU=aR(a=>Object.values(a)),aV=aR(a=>Object.values(a));var aW=aR((a,b)=>aV(b).filter(b=>a===b.descriptor.droppableId).sort((a,b)=>a.descriptor.index-b.descriptor.index));function aX(a){return a.at&&"REORDER"===a.at.type?a.at.destination:null}function aY(a){return a.at&&"COMBINE"===a.at.type?a.at.combine:null}var aZ=aR((a,b)=>b.filter(b=>b.descriptor.id!==a.descriptor.id)),a$=(a,b)=>a.descriptor.droppableId===b.descriptor.id;let a_={point:aE,value:0},a0={invisible:{},visible:{},all:[]},a1={displaced:a0,displacedBy:a_,at:null};var a2=(a,b)=>c=>a<=c&&c<=b,a3=a=>{let b=a2(a.top,a.bottom),c=a2(a.left,a.right);return d=>{if(b(d.top)&&b(d.bottom)&&c(d.left)&&c(d.right))return!0;let e=b(d.top)||b(d.bottom),f=c(d.left)||c(d.right);if(e&&f)return!0;let g=d.top<a.top&&d.bottom>a.bottom,h=d.left<a.left&&d.right>a.right;return!!g&&!!h||g&&f||h&&e}},a4=a=>{let b=a2(a.top,a.bottom),c=a2(a.left,a.right);return a=>b(a.top)&&b(a.bottom)&&c(a.left)&&c(a.right)};let a5={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},a6={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},a7=({target:a,destination:b,viewport:c,withDroppableDisplacement:d,isVisibleThroughFrameFn:e})=>{let f=d?((a,b)=>aN(a,b.frame?b.frame.scroll.diff.displacement:aE))(a,b):a;return((a,b,c)=>!!b.subject.active&&c(b.subject.active)(a))(f,b,e)&&e(c)(f)},a8=a=>a7({...a,isVisibleThroughFrameFn:a4});function a9({afterDragging:a,destination:b,displacedBy:c,viewport:d,forceShouldAnimate:e,last:f}){return a.reduce(function(a,g){var h,i;let j=(h=g,i=c,af(ag(h.page.marginBox,{top:i.point.y,right:0,bottom:0,left:i.point.x}))),k=g.descriptor.id;if(a.all.push(k),!a7({target:j,destination:b,viewport:d,withDroppableDisplacement:!0,isVisibleThroughFrameFn:a3}))return a.invisible[g.descriptor.id]=!0,a;let l=((a,b,c)=>{if("boolean"==typeof c)return c;if(!b)return!0;let{invisible:d,visible:e}=b;if(d[a])return!1;let f=e[a];return!f||f.shouldAnimate})(k,f,e);return a.visible[k]={draggableId:k,shouldAnimate:l},a},{all:[],visible:{},invisible:{}})}function ba({insideDestination:a,inHomeList:b,displacedBy:c,destination:d}){let e=function(a,b){if(!a.length)return 0;let c=a[a.length-1].descriptor.index;return b.inHomeList?c:c+1}(a,{inHomeList:b});return{displaced:a0,displacedBy:c,at:{type:"REORDER",destination:{droppableId:d.descriptor.id,index:e}}}}function bb({draggable:a,insideDestination:b,destination:c,viewport:d,displacedBy:e,last:f,index:g,forceShouldAnimate:h}){let i=a$(a,c);if(null==g)return ba({insideDestination:b,inHomeList:i,displacedBy:e,destination:c});let j=b.find(a=>a.descriptor.index===g);if(!j)return ba({insideDestination:b,inHomeList:i,displacedBy:e,destination:c});let k=aZ(a,b),l=b.indexOf(j);return{displaced:a9({afterDragging:k.slice(l),destination:c,displacedBy:e,last:f,viewport:d.frame,forceShouldAnimate:h}),displacedBy:e,at:{type:"REORDER",destination:{droppableId:c.descriptor.id,index:g}}}}function bc(a,b){return!!b.effected[a]}let bd=(a,b)=>b.margin[a.start]+b.borderBox[a.size]/2,be=(a,b,c)=>b[a.crossAxisStart]+c.margin[a.crossAxisStart]+c.borderBox[a.crossAxisSize]/2,bf=({axis:a,moveRelativeTo:b,isMoving:c})=>aJ(a.line,b.marginBox[a.end]+bd(a,c),be(a,b.marginBox,c)),bg=({axis:a,moveRelativeTo:b,isMoving:c})=>aJ(a.line,b.marginBox[a.start]-((a,b)=>b.margin[a.end]+b.borderBox[a.size]/2)(a,c),be(a,b.marginBox,c));var bh=(a,b)=>{let c=a.frame;return c?aF(b,c.scroll.diff.displacement):b},bi=a=>{let b=(({impact:a,draggable:b,droppable:c,draggables:d,afterCritical:e})=>{let f=b.page.borderBox.center,g=a.at;return c&&g?"REORDER"===g.type?(({impact:a,draggable:b,draggables:c,droppable:d,afterCritical:e})=>{let f=aW(d.descriptor.id,c),g=b.page,h=d.axis;if(!f.length)return(({axis:a,moveInto:b,isMoving:c})=>aJ(a.line,b.contentBox[a.start]+bd(a,c),be(a,b.contentBox,c)))({axis:h,moveInto:d.page,isMoving:g});let{displaced:i,displacedBy:j}=a,k=i.all[0];if(k){let a=c[k];return bc(k,e)?bg({axis:h,moveRelativeTo:a.page,isMoving:g}):bg({axis:h,moveRelativeTo:al(a.page,j.point),isMoving:g})}let l=f[f.length-1];return l.descriptor.id===b.descriptor.id?g.borderBox.center:bc(l.descriptor.id,e)?bf({axis:h,moveRelativeTo:al(l.page,aI(e.displacedBy.point)),isMoving:g}):bf({axis:h,moveRelativeTo:l.page,isMoving:g})})({impact:a,draggable:b,draggables:d,droppable:c,afterCritical:e}):(({afterCritical:a,impact:b,draggables:c})=>{let d=aY(b);d||av();let e=d.draggableId;return aF(c[e].page.borderBox.center,(({displaced:a,afterCritical:b,combineWith:c,displacedBy:d})=>{let e=!!(a.visible[c]||a.invisible[c]);return bc(c,b)?e?aE:aI(d.point):e?d.point:aE})({displaced:b.displaced,afterCritical:a,combineWith:e,displacedBy:b.displacedBy}))})({impact:a,draggables:d,afterCritical:e}):f})(a),c=a.droppable;return c?bh(c,b):b},bj=(a,b)=>{let c=aG(b,a.scroll.initial),d=aI(c);return{frame:af({top:b.y,bottom:b.y+a.frame.height,left:b.x,right:b.x+a.frame.width}),scroll:{initial:a.scroll.initial,max:a.scroll.max,current:b,diff:{value:c,displacement:d}}}};function bk(a,b){return a.map(a=>b[a])}var bl=({pageBorderBoxCenter:a,draggable:b,viewport:c})=>{let d=aG(aF(c.scroll.diff.displacement,a),b.page.borderBox.center);return aF(b.client.borderBox.center,d)},bm=({draggable:a,destination:b,newPageBorderBoxCenter:c,viewport:d,withDroppableDisplacement:e,onlyOnMainAxis:f=!1})=>{let g=aG(c,a.page.borderBox.center),h={target:aN(a.page.borderBox,g),destination:b,withDroppableDisplacement:e,viewport:d};return f?(a=>{let b;return a7({...a,isVisibleThroughFrameFn:(b=a.destination.axis,a=>{let c=a2(a.top,a.bottom),d=a2(a.left,a.right);return a=>b===a5?c(a.top)&&c(a.bottom):d(a.left)&&d(a.right)})})})(h):a8(h)};let bn=a=>{let b=a.subject.active;return b||av(),b},bo=(a,b)=>{let c=a.page.borderBox.center;return bc(a.descriptor.id,b)?aG(c,b.displacedBy.point):c};var bp=aR(function(a,b){let c=b[a.line];return{value:c,point:aJ(a.line,c)}});let bq=(a,b)=>({...a,scroll:{...a.scroll,max:b}}),br=(a,b,c)=>{let d=a.frame;a$(b,a)&&av(),a.subject.withPlaceholder&&av();let e=bp(a.axis,b.displaceBy).point,f=((a,b,c)=>{let d=a.axis;if("virtual"===a.descriptor.mode)return aJ(d.line,b[d.line]);let e=a.subject.page.contentBox[d.size],f=aW(a.descriptor.id,c).reduce((a,b)=>a+b.client.marginBox[d.size],0)+b[d.line]-e;return f<=0?null:aJ(d.line,f)})(a,e,c),g={placeholderSize:e,increasedBy:f,oldFrameMaxScroll:a.frame?a.frame.scroll.max:null};if(!d){let b=aP({page:a.subject.page,withPlaceholder:g,axis:a.axis,frame:a.frame});return{...a,subject:b}}let h=f?aF(d.scroll.max,f):d.scroll.max,i=bq(d,h),j=aP({page:a.subject.page,withPlaceholder:g,axis:a.axis,frame:i});return{...a,subject:j,frame:i}};var bs=a=>{let b=a.at;return b?"REORDER"===b.type?b.destination.droppableId:b.combine.droppableId:null};function bt(a){return"DRAGGING"===a.phase||"COLLECTING"===a.phase}function bu(a){let b=a2(a.top,a.bottom),c=a2(a.left,a.right);return function(a){return b(a.y)&&c(a.x)}}let bv=(a,b)=>af(aN(a,b));function bw({displaced:a,id:b}){return!!(a.visible[b]||a.invisible[b])}var bx=({pageOffset:a,draggable:b,draggables:c,droppables:d,previousImpact:e,viewport:f,afterCritical:g})=>{let h=bv(b.page.borderBox,a),i=function({pageBorderBox:a,draggable:b,droppables:c}){let d=aU(c).filter(b=>{if(!b.isEnabled)return!1;let c=b.subject.active;if(!c||!(a.left<c.right)||!(a.right>c.left)||!(a.top<c.bottom)||!(a.bottom>c.top))return!1;if(bu(c)(a.center))return!0;let d=b.axis,e=c.center[d.crossAxisLine],f=a[d.crossAxisStart],g=a[d.crossAxisEnd],h=a2(c[d.crossAxisStart],c[d.crossAxisEnd]),i=h(f),j=h(g);return!i&&!j||(i?f<e:g>e)});return d.length?1===d.length?d[0].descriptor.id:function({pageBorderBox:a,draggable:b,candidates:c}){let d=b.page.borderBox.center,e=c.map(b=>{let c=b.axis,e=aJ(b.axis.line,a.center[c.line],b.page.borderBox.center[c.crossAxisLine]);return{id:b.descriptor.id,distance:aK(d,e)}}).sort((a,b)=>b.distance-a.distance);return e[0]?e[0].id:null}({pageBorderBox:a,draggable:b,candidates:d}):null}({pageBorderBox:h,draggable:b,droppables:d});if(!i)return a1;let j=d[i],k=aW(j.descriptor.id,c),l=((a,b)=>{let c=a.frame;return c?bv(b,c.scroll.diff.value):b})(j,h);return(({draggable:a,pageBorderBoxWithDroppableScroll:b,previousImpact:c,destination:d,insideDestination:e,afterCritical:f})=>{if(!d.isCombineEnabled)return null;let g=d.axis,h=bp(d.axis,a.displaceBy),i=h.value,j=b[g.start],k=b[g.end],l=aZ(a,e).find(a=>{let b=a.descriptor.id,d=a.page.borderBox,e=d[g.size]/4,h=bc(b,f),l=bw({displaced:c.displaced,id:b});return h?l?k>d[g.start]+e&&k<d[g.end]-e:j>d[g.start]-i+e&&j<d[g.end]-i-e:l?k>d[g.start]+i+e&&k<d[g.end]+i-e:j>d[g.start]+e&&j<d[g.end]-e});return l?{displacedBy:h,displaced:c.displaced,at:{type:"COMBINE",combine:{draggableId:l.descriptor.id,droppableId:d.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:l,draggable:b,previousImpact:e,destination:j,insideDestination:k,afterCritical:g})||(({pageBorderBoxWithDroppableScroll:a,draggable:b,destination:c,insideDestination:d,last:e,viewport:f,afterCritical:g})=>{let h=c.axis,i=bp(c.axis,b.displaceBy),j=i.value,k=a[h.start],l=a[h.end],m=aZ(b,d).find(a=>{let b=a.descriptor.id,c=a.page.borderBox.center[h.line],d=bc(b,g),f=bw({displaced:e,id:b});return d?f?l<=c:k<c-j:f?l<=c+j:k<c})||null,n=function({draggable:a,closest:b,inHomeList:c}){return b?c&&b.descriptor.index>a.descriptor.index?b.descriptor.index-1:b.descriptor.index:null}({draggable:b,closest:m,inHomeList:a$(b,c)});return bb({draggable:b,insideDestination:d,destination:c,viewport:f,last:e,displacedBy:i,index:n})})({pageBorderBoxWithDroppableScroll:l,draggable:b,destination:j,insideDestination:k,last:e.displaced,viewport:f,afterCritical:g})},by=(a,b)=>({...a,[b.descriptor.id]:b}),bz=({state:a,clientSelection:b,dimensions:c,viewport:d,impact:e,scrollJumpRequest:f})=>{let g=d||a.viewport,h=c||a.dimensions,i=b||a.current.client.selection,j=aG(i,a.initial.client.selection),k={offset:j,selection:i,borderBoxCenter:aF(a.initial.client.borderBoxCenter,j)},l={selection:aF(k.selection,g.scroll.current),borderBoxCenter:aF(k.borderBoxCenter,g.scroll.current),offset:aF(k.offset,g.scroll.diff.value)},m={client:k,page:l};if("COLLECTING"===a.phase)return{...a,dimensions:h,viewport:g,current:m};let n=h.draggables[a.critical.draggable.id],o=e||bx({pageOffset:l.offset,draggable:n,draggables:h.draggables,droppables:h.droppables,previousImpact:a.impact,viewport:g,afterCritical:a.afterCritical}),p=(({draggable:a,draggables:b,droppables:c,previousImpact:d,impact:e})=>{let f=(({previousImpact:a,impact:b,droppables:c})=>{let d=bs(a),e=bs(b);if(!d||d===e)return c;let f=c[d];return f.subject.withPlaceholder?by(c,(a=>{let b=a.subject.withPlaceholder;b||av();let c=a.frame;if(!c){let b=aP({page:a.subject.page,axis:a.axis,frame:null,withPlaceholder:null});return{...a,subject:b}}let d=b.oldFrameMaxScroll;d||av();let e=bq(c,d),f=aP({page:a.subject.page,axis:a.axis,frame:e,withPlaceholder:null});return{...a,subject:f,frame:e}})(f)):c})({previousImpact:d,impact:e,droppables:c}),g=bs(e);if(!g)return f;let h=c[g];return a$(a,h)||h.subject.withPlaceholder?f:by(f,br(h,a,b))})({draggable:n,impact:o,previousImpact:a.impact,draggables:h.draggables,droppables:h.droppables});return{...a,current:m,dimensions:{draggables:h.draggables,droppables:p},impact:o,viewport:g,scrollJumpRequest:f||null,forceShouldAnimate:!f&&null}},bA=({impact:a,viewport:b,draggables:c,destination:d,forceShouldAnimate:e})=>{var f;let g=a.displaced,h=a9({afterDragging:(f=g.all,f.map(a=>c[a])),destination:d,displacedBy:a.displacedBy,viewport:b.frame,forceShouldAnimate:e,last:g});return{...a,displaced:h}},bB=({impact:a,draggable:b,droppable:c,draggables:d,viewport:e,afterCritical:f})=>bl({pageBorderBoxCenter:bi({impact:a,draggable:b,draggables:d,droppable:c,afterCritical:f}),draggable:b,viewport:e}),bC=({state:a,dimensions:b,viewport:c})=>{"SNAP"!==a.movementMode&&av();let d=a.impact,e=c||a.viewport,f=b||a.dimensions,{draggables:g,droppables:h}=f,i=g[a.critical.draggable.id],j=bs(d);j||av();let k=h[j],l=bA({impact:d,viewport:e,destination:k,draggables:g}),m=bB({impact:l,draggable:i,droppable:k,draggables:g,viewport:e,afterCritical:a.afterCritical});return bz({impact:l,clientSelection:m,state:a,dimensions:f,viewport:e})},bD=({draggable:a,home:b,draggables:c,viewport:d})=>{let e=bp(b.axis,a.displaceBy),f=aW(b.descriptor.id,c),g=f.indexOf(a);-1===g&&av();let h=f.slice(g+1),i=h.reduce((a,b)=>(a[b.descriptor.id]=!0,a),{}),j={inVirtualList:"virtual"===b.descriptor.mode,displacedBy:e,effected:i};return{impact:{displaced:a9({afterDragging:h,destination:b,displacedBy:e,last:null,viewport:d.frame,forceShouldAnimate:!1}),displacedBy:e,at:{type:"REORDER",destination:(a=>({index:a.index,droppableId:a.droppableId}))(a.descriptor)}},afterCritical:j}};let bE=a=>{},bF=a=>{},bG=(a,b,c)=>{let d=((a,b)=>({draggables:a.draggables,droppables:by(a.droppables,b)}))(a.dimensions,b);return"SNAP"!==a.movementMode||c?bz({state:a,dimensions:d}):bC({state:a,dimensions:d})};function bH(a){return a.isDragging&&"SNAP"===a.movementMode?{...a,scrollJumpRequest:null}:a}let bI={phase:"IDLE",completed:null,shouldFlush:!1};var bJ=(a=bI,b)=>{if("FLUSH"===b.type)return{...bI,shouldFlush:!0};if("INITIAL_PUBLISH"===b.type){"IDLE"!==a.phase&&av();let{critical:c,clientSelection:d,viewport:e,dimensions:f,movementMode:g}=b.payload,h=f.draggables[c.draggable.id],i=f.droppables[c.droppable.id],j={selection:d,borderBoxCenter:h.client.borderBox.center,offset:aE},k={client:j,page:{selection:aF(j.selection,e.scroll.initial),borderBoxCenter:aF(j.selection,e.scroll.initial),offset:aF(j.selection,e.scroll.diff.value)}},l=aU(f.droppables).every(a=>!a.isFixedOnPage),{impact:m,afterCritical:n}=bD({draggable:h,home:i,draggables:f.draggables,viewport:e});return{phase:"DRAGGING",isDragging:!0,critical:c,movementMode:g,dimensions:f,initial:k,current:k,isWindowScrollAllowed:l,impact:m,afterCritical:n,onLiftImpact:m,viewport:e,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===b.type)return"COLLECTING"===a.phase||"DROP_PENDING"===a.phase?a:("DRAGGING"!==a.phase&&av(),{...a,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===b.type)return"COLLECTING"!==a.phase&&"DROP_PENDING"!==a.phase&&av(),(({state:a,published:b})=>{bE();let c=b.modified.map(b=>aQ(a.dimensions.droppables[b.droppableId],b.scroll)),d={...a.dimensions.droppables,...aS(c)},e=aT((({additions:a,updatedDroppables:b,viewport:c})=>{let d=c.scroll.diff.value;return a.map(a=>{let e=aF(d,(a=>{let b=a.frame;return b||av(),b})(b[a.descriptor.droppableId]).scroll.diff.value);return(({draggable:a,offset:b,initialWindowScroll:c})=>{let d=al(a.client,b),e=am(d,c);return{...a,placeholder:{...a.placeholder,client:d},client:d,page:e}})({draggable:a,offset:e,initialWindowScroll:c.scroll.initial})})})({additions:b.additions,updatedDroppables:d,viewport:a.viewport})),f={...a.dimensions.draggables,...e};b.removals.forEach(a=>{delete f[a]});let g={droppables:d,draggables:f},h=bs(a.impact),i=h?g.droppables[h]:null,{impact:j,afterCritical:k}=bD({draggable:g.draggables[a.critical.draggable.id],home:g.droppables[a.critical.droppable.id],draggables:f,viewport:a.viewport}),l=i&&i.isCombineEnabled?a.impact:j,m=bx({pageOffset:a.current.page.offset,draggable:g.draggables[a.critical.draggable.id],draggables:g.draggables,droppables:g.droppables,previousImpact:l,viewport:a.viewport,afterCritical:k});bF();let n={...a,phase:"DRAGGING",impact:m,onLiftImpact:j,dimensions:g,afterCritical:k,forceShouldAnimate:!1};return"COLLECTING"===a.phase?n:{...n,phase:"DROP_PENDING",reason:a.reason,isWaiting:!1}})({state:a,published:b.payload});if("MOVE"===b.type){if("DROP_PENDING"===a.phase)return a;bt(a)||av();let{client:c}=b.payload;return aH(c,a.current.client.selection)?a:bz({state:a,clientSelection:c,impact:"SNAP"===a.movementMode?a.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===b.type){if("DROP_PENDING"===a.phase||"COLLECTING"===a.phase)return bH(a);bt(a)||av();let{id:c,newScroll:d}=b.payload,e=a.dimensions.droppables[c];return e?bG(a,aQ(e,d),!1):a}if("UPDATE_DROPPABLE_IS_ENABLED"===b.type){if("DROP_PENDING"===a.phase)return a;bt(a)||av();let{id:c,isEnabled:d}=b.payload,e=a.dimensions.droppables[c];return e||av(),e.isEnabled===d&&av(),bG(a,{...e,isEnabled:d},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===b.type){if("DROP_PENDING"===a.phase)return a;bt(a)||av();let{id:c,isCombineEnabled:d}=b.payload,e=a.dimensions.droppables[c];return e||av(),e.isCombineEnabled===d&&av(),bG(a,{...e,isCombineEnabled:d},!0)}if("MOVE_BY_WINDOW_SCROLL"===b.type){if("DROP_PENDING"===a.phase||"DROP_ANIMATING"===a.phase)return a;bt(a)||av(),a.isWindowScrollAllowed||av();let c=b.payload.newScroll;if(aH(a.viewport.scroll.current,c))return bH(a);let d=bj(a.viewport,c);return"SNAP"===a.movementMode?bC({state:a,viewport:d}):bz({state:a,viewport:d})}if("UPDATE_VIEWPORT_MAX_SCROLL"===b.type){if(!bt(a))return a;let c=b.payload.maxScroll;if(aH(c,a.viewport.scroll.max))return a;let d={...a.viewport,scroll:{...a.viewport.scroll,max:c}};return{...a,viewport:d}}if("MOVE_UP"===b.type||"MOVE_DOWN"===b.type||"MOVE_LEFT"===b.type||"MOVE_RIGHT"===b.type){if("COLLECTING"===a.phase||"DROP_PENDING"===a.phase)return a;"DRAGGING"!==a.phase&&av();let c=(({state:a,type:b})=>{let c=((a,b)=>{let c=bs(a);return c?b[c]:null})(a.impact,a.dimensions.droppables),d=!!c,e=a.dimensions.droppables[a.critical.droppable.id],f=c||e,g=f.axis.direction,h="vertical"===g&&("MOVE_UP"===b||"MOVE_DOWN"===b)||"horizontal"===g&&("MOVE_LEFT"===b||"MOVE_RIGHT"===b);if(h&&!d)return null;let i="MOVE_DOWN"===b||"MOVE_RIGHT"===b,j=a.dimensions.draggables[a.critical.draggable.id],k=a.current.page.borderBoxCenter,{draggables:l,droppables:m}=a.dimensions;return h?(({isMovingForward:a,draggable:b,destination:c,draggables:d,previousImpact:e,viewport:f,previousPageBorderBoxCenter:g,previousClientSelection:h,afterCritical:i})=>{if(!c.isEnabled)return null;let j=aW(c.descriptor.id,d),k=a$(b,c),l=(({isMovingForward:a,draggable:b,destination:c,insideDestination:d,previousImpact:e})=>{if(!c.isCombineEnabled||!aX(e))return null;function f(a){let b={type:"COMBINE",combine:{draggableId:a,droppableId:c.descriptor.id}};return{...e,at:b}}let g=e.displaced.all,h=g.length?g[0]:null;if(a)return h?f(h):null;let i=aZ(b,d);if(!h)return i.length?f(i[i.length-1].descriptor.id):null;let j=i.findIndex(a=>a.descriptor.id===h);-1===j&&av();let k=j-1;return k<0?null:f(i[k].descriptor.id)})({isMovingForward:a,draggable:b,destination:c,insideDestination:j,previousImpact:e})||(({isMovingForward:a,isInHomeList:b,draggable:c,draggables:d,destination:e,insideDestination:f,previousImpact:g,viewport:h,afterCritical:i})=>{let j=g.at;if(j||av(),"REORDER"===j.type){let d=(({isMovingForward:a,isInHomeList:b,insideDestination:c,location:d})=>{if(!c.length)return null;let e=d.index,f=a?e+1:e-1,g=c[0].descriptor.index,h=c[c.length-1].descriptor.index;return f<g||f>(b?h:h+1)?null:f})({isMovingForward:a,isInHomeList:b,location:j.destination,insideDestination:f});return null==d?null:bb({draggable:c,insideDestination:f,destination:e,viewport:h,last:g.displaced,displacedBy:g.displacedBy,index:d})}let k=(({isMovingForward:a,destination:b,draggables:c,combine:d,afterCritical:e})=>{if(!b.isCombineEnabled)return null;let f=d.draggableId,g=c[f].descriptor.index;return bc(f,e)?a?g:g-1:a?g+1:g})({isMovingForward:a,destination:e,displaced:g.displaced,draggables:d,combine:j.combine,afterCritical:i});return null==k?null:bb({draggable:c,insideDestination:f,destination:e,viewport:h,last:g.displaced,displacedBy:g.displacedBy,index:k})})({isMovingForward:a,isInHomeList:k,draggable:b,draggables:d,destination:c,insideDestination:j,previousImpact:e,viewport:f,afterCritical:i});if(!l)return null;let m=bi({impact:l,draggable:b,droppable:c,draggables:d,afterCritical:i});if(bm({draggable:b,destination:c,newPageBorderBoxCenter:m,viewport:f.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:bl({pageBorderBoxCenter:m,draggable:b,viewport:f}),impact:l,scrollJumpRequest:null};let n=aG(m,g),o=(({impact:a,viewport:b,destination:c,draggables:d,maxScrollChange:e})=>{let f=bj(b,aF(b.scroll.current,e)),g=c.frame?aQ(c,aF(c.frame.scroll.current,e)):c,h=a.displaced,i=a9({afterDragging:bk(h.all,d),destination:c,displacedBy:a.displacedBy,viewport:f.frame,last:h,forceShouldAnimate:!1}),j=a9({afterDragging:bk(h.all,d),destination:g,displacedBy:a.displacedBy,viewport:b.frame,last:h,forceShouldAnimate:!1}),k={},l={},m=[h,i,j];return h.all.forEach(a=>{let b=function(a,b){for(let c=0;c<b.length;c++){let d=b[c].visible[a];if(d)return d}return null}(a,m);if(b){l[a]=b;return}k[a]=!0}),{...a,displaced:{all:h.all,invisible:k,visible:l}}})({impact:l,viewport:f,destination:c,draggables:d,maxScrollChange:n});return{clientSelection:h,impact:o,scrollJumpRequest:n}})({isMovingForward:i,previousPageBorderBoxCenter:k,draggable:j,destination:f,draggables:l,viewport:a.viewport,previousClientSelection:a.current.client.selection,previousImpact:a.impact,afterCritical:a.afterCritical}):(({isMovingForward:a,previousPageBorderBoxCenter:b,draggable:c,isOver:d,draggables:e,droppables:f,viewport:g,afterCritical:h})=>{let i=(({isMovingForward:a,pageBorderBoxCenter:b,source:c,droppables:d,viewport:e})=>{let f=c.subject.active;if(!f)return null;let g=c.axis,h=a2(f[g.start],f[g.end]),i=aU(d).filter(a=>a!==c).filter(a=>a.isEnabled).filter(a=>!!a.subject.active).filter(a=>a3(e.frame)(bn(a))).filter(b=>{let c=bn(b);return a?f[g.crossAxisEnd]<c[g.crossAxisEnd]:c[g.crossAxisStart]<f[g.crossAxisStart]}).filter(a=>{let b=bn(a),c=a2(b[g.start],b[g.end]);return h(b[g.start])||h(b[g.end])||c(f[g.start])||c(f[g.end])}).sort((b,c)=>{let d=bn(b)[g.crossAxisStart],e=bn(c)[g.crossAxisStart];return a?d-e:e-d}).filter((a,b,c)=>bn(a)[g.crossAxisStart]===bn(c[0])[g.crossAxisStart]);if(!i.length)return null;if(1===i.length)return i[0];let j=i.filter(a=>a2(bn(a)[g.start],bn(a)[g.end])(b[g.line]));return 1===j.length?j[0]:j.length>1?j.sort((a,b)=>bn(a)[g.start]-bn(b)[g.start])[0]:i.sort((a,c)=>{let d=aL(b,aO(bn(a))),e=aL(b,aO(bn(c)));return d!==e?d-e:bn(a)[g.start]-bn(c)[g.start]})[0]})({isMovingForward:a,pageBorderBoxCenter:b,source:d,droppables:f,viewport:g});if(!i)return null;let j=aW(i.descriptor.id,e),k=(({pageBorderBoxCenter:a,viewport:b,destination:c,insideDestination:d,afterCritical:e})=>d.filter(a=>a8({target:((a,b)=>{let c=a.page.borderBox;return bc(a.descriptor.id,b)?aN(c,aI(b.displacedBy.point)):c})(a,e),destination:c,viewport:b.frame,withDroppableDisplacement:!0})).sort((b,d)=>{let f=aK(a,bh(c,bo(b,e))),g=aK(a,bh(c,bo(d,e)));return f<g?-1:g<f?1:b.descriptor.index-d.descriptor.index})[0]||null)({pageBorderBoxCenter:b,viewport:g,destination:i,insideDestination:j,afterCritical:h}),l=(({previousPageBorderBoxCenter:a,moveRelativeTo:b,insideDestination:c,draggable:d,draggables:e,destination:f,viewport:g,afterCritical:h})=>{if(!b){if(c.length)return null;let a={displaced:a0,displacedBy:a_,at:{type:"REORDER",destination:{droppableId:f.descriptor.id,index:0}}},b=bi({impact:a,draggable:d,droppable:f,draggables:e,afterCritical:h}),i=a$(d,f)?f:br(f,d,e);return bm({draggable:d,destination:i,newPageBorderBoxCenter:b,viewport:g.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?a:null}let i=a[f.axis.line]<=b.page.borderBox.center[f.axis.line],j=(()=>{let a=b.descriptor.index;return b.descriptor.id===d.descriptor.id||i?a:a+1})(),k=bp(f.axis,d.displaceBy);return bb({draggable:d,insideDestination:c,destination:f,viewport:g,displacedBy:k,last:a0,index:j})})({previousPageBorderBoxCenter:b,destination:i,draggable:c,draggables:e,moveRelativeTo:k,insideDestination:j,viewport:g,afterCritical:h});return l?{clientSelection:bl({pageBorderBoxCenter:bi({impact:l,draggable:c,droppable:i,draggables:e,afterCritical:h}),draggable:c,viewport:g}),impact:l,scrollJumpRequest:null}:null})({isMovingForward:i,previousPageBorderBoxCenter:k,draggable:j,isOver:f,draggables:l,droppables:m,viewport:a.viewport,afterCritical:a.afterCritical})})({state:a,type:b.type});return c?bz({state:a,impact:c.impact,clientSelection:c.clientSelection,scrollJumpRequest:c.scrollJumpRequest}):a}if("DROP_PENDING"===b.type){let c=b.payload.reason;return"COLLECTING"!==a.phase&&av(),{...a,phase:"DROP_PENDING",isWaiting:!0,reason:c}}if("DROP_ANIMATE"===b.type){let{completed:c,dropDuration:d,newHomeClientOffset:e}=b.payload;return"DRAGGING"!==a.phase&&"DROP_PENDING"!==a.phase&&av(),{phase:"DROP_ANIMATING",completed:c,dropDuration:d,newHomeClientOffset:e,dimensions:a.dimensions}}if("DROP_COMPLETE"===b.type){let{completed:a}=b.payload;return{phase:"IDLE",completed:a,shouldFlush:!1}}return a};function bK(a,b){return a instanceof Object&&"type"in a&&a.type===b}let bL=a=>({type:"PUBLISH_WHILE_DRAGGING",payload:a}),bM=()=>({type:"COLLECTION_STARTING",payload:null}),bN=a=>({type:"UPDATE_DROPPABLE_SCROLL",payload:a}),bO=a=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:a}),bP=a=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:a}),bQ=a=>({type:"MOVE",payload:a}),bR=()=>({type:"MOVE_UP",payload:null}),bS=()=>({type:"MOVE_DOWN",payload:null}),bT=()=>({type:"MOVE_RIGHT",payload:null}),bU=()=>({type:"MOVE_LEFT",payload:null}),bV=()=>({type:"FLUSH",payload:null}),bW=a=>({type:"DROP_COMPLETE",payload:a}),bX=a=>({type:"DROP",payload:a}),bY=()=>({type:"DROP_ANIMATION_FINISHED",payload:null}),bZ={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},b$={opacity:{drop:0,combining:.7},scale:{drop:.75}},b_={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},b0=`${b_.outOfTheWay}s ${bZ.outOfTheWay}`,b1={fluid:`opacity ${b0}`,snap:`transform ${b0}, opacity ${b0}`,drop:a=>{let b=`${a}s ${bZ.drop}`;return`transform ${b}, opacity ${b}`},outOfTheWay:`transform ${b0}`,placeholder:`height ${b0}, width ${b0}, margin ${b0}`},b2=a=>aH(a,aE)?void 0:`translate(${a.x}px, ${a.y}px)`,b3={moveTo:b2,drop:(a,b)=>{let c=b2(a);if(c)return b?`${c} scale(${b$.scale.drop})`:c}},{minDropTime:b4,maxDropTime:b5}=b_,b6=b5-b4,b7=({getState:a,dispatch:b})=>c=>d=>{if(!bK(d,"DROP"))return void c(d);let e=a(),f=d.payload.reason;if("COLLECTING"===e.phase)return void b({type:"DROP_PENDING",payload:{reason:f}});if("IDLE"===e.phase)return;"DROP_PENDING"===e.phase&&e.isWaiting&&av(),"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&av();let g=e.critical,h=e.dimensions,i=h.draggables[e.critical.draggable.id],{impact:j,didDropInsideDroppable:k}=(({draggables:a,reason:b,lastImpact:c,home:d,viewport:e,onLiftImpact:f})=>c.at&&"DROP"===b?"REORDER"===c.at.type?{impact:c,didDropInsideDroppable:!0}:{impact:{...c,displaced:a0},didDropInsideDroppable:!0}:{impact:bA({draggables:a,impact:f,destination:d,viewport:e,forceShouldAnimate:!0}),didDropInsideDroppable:!1})({reason:f,lastImpact:e.impact,afterCritical:e.afterCritical,onLiftImpact:e.onLiftImpact,home:e.dimensions.droppables[e.critical.droppable.id],viewport:e.viewport,draggables:e.dimensions.draggables}),l=k?aX(j):null,m=k?aY(j):null,n={index:g.draggable.index,droppableId:g.droppable.id},o={draggableId:i.descriptor.id,type:i.descriptor.type,source:n,reason:f,mode:e.movementMode,destination:l,combine:m},p=(({impact:a,draggable:b,dimensions:c,viewport:d,afterCritical:e})=>{let{draggables:f,droppables:g}=c,h=bs(a),i=h?g[h]:null,j=g[b.descriptor.droppableId];return aG(bB({impact:a,draggable:b,draggables:f,afterCritical:e,droppable:i||j,viewport:d}),b.client.borderBox.center)})({impact:j,draggable:i,dimensions:h,viewport:e.viewport,afterCritical:e.afterCritical}),q={critical:e.critical,afterCritical:e.afterCritical,result:o,impact:j};if(!(!aH(e.current.client.offset,p)||o.combine))return void b(bW({completed:q}));let r=(({current:a,destination:b,reason:c})=>{let d=aK(a,b);if(d<=0)return b4;if(d>=1500)return b5;let e=b4+d/1500*b6;return Number(("CANCEL"===c?.6*e:e).toFixed(2))})({current:e.current.client.offset,destination:p,reason:f});b({type:"DROP_ANIMATE",payload:{newHomeClientOffset:p,dropDuration:r,completed:q}})};var b8=()=>({x:window.pageXOffset,y:window.pageYOffset});let b9=a=>{let b=function({onWindowScroll:a}){let b=ap(function(){a(b8())}),c={eventName:"scroll",options:{passive:!0,capture:!1},fn:a=>{(a.target===window||a.target===window.document)&&b()}},d=as;function e(){return d!==as}return{start:function(){e()&&av(),d=at(window,[c])},stop:function(){e()||av(),b.cancel(),d(),d=as},isActive:e}}({onWindowScroll:b=>{a.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:b}})}});return a=>c=>{!b.isActive()&&bK(c,"INITIAL_PUBLISH")&&b.start(),b.isActive()&&(a=>bK(a,"DROP_COMPLETE")||bK(a,"DROP_ANIMATE")||bK(a,"FLUSH"))(c)&&b.stop(),a(c)}},ca=(a,b)=>{bE(),b(),bF()},cb=(a,b)=>({draggableId:a.draggable.id,type:a.droppable.type,source:{droppableId:a.droppable.id,index:a.draggable.index},mode:b});function cc(a,b,c,d){if(!a)return void c(d(b));let e=(a=>{let b=!1,c=!1,d=setTimeout(()=>{c=!0}),e=e=>{!b&&(c||(b=!0,a(e),clearTimeout(d)))};return e.wasCalled=()=>b,e})(c);a(b,{announce:e}),e.wasCalled()||c(d(b))}let cd=a=>b=>c=>{if(!bK(c,"DROP_ANIMATION_FINISHED"))return void b(c);let d=a.getState();"DROP_ANIMATING"!==d.phase&&av(),a.dispatch(bW({completed:d.completed}))},ce=a=>{let b=null,c=null;return d=>e=>{if((bK(e,"FLUSH")||bK(e,"DROP_COMPLETE")||bK(e,"DROP_ANIMATION_FINISHED"))&&(c&&(cancelAnimationFrame(c),c=null),b&&(b(),b=null)),d(e),!bK(e,"DROP_ANIMATE"))return;let f={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===a.getState().phase&&a.dispatch(bY())}};c=requestAnimationFrame(()=>{c=null,b=at(window,[f])})}},cf=a=>b=>c=>{if(b(c),!bK(c,"PUBLISH_WHILE_DRAGGING"))return;let d=a.getState();"DROP_PENDING"===d.phase&&(d.isWaiting||a.dispatch(bX({reason:d.reason})))},cg=()=>({additions:{},removals:{},modified:{}});var ch=({scrollHeight:a,scrollWidth:b,height:c,width:d})=>{let e=aG({x:b,y:a},{x:d,y:c});return{x:Math.max(0,e.x),y:Math.max(0,e.y)}},ci=()=>{let a=document.documentElement;return a||av(),a},cj=()=>{let a=ci();return ch({scrollHeight:a.scrollHeight,scrollWidth:a.scrollWidth,width:a.clientWidth,height:a.clientHeight})};function ck(a,b,c){return c.descriptor.id!==b.id&&c.descriptor.type===b.type&&"virtual"===a.droppable.getById(c.descriptor.droppableId).descriptor.mode}var cl=(a,b)=>"IDLE"===a.phase||"DROP_ANIMATING"===a.phase&&a.completed.result.draggableId!==b&&"DROP"===a.completed.result.reason,cm=a=>{window.scrollBy(a.x,a.y)};let cn=aR(a=>aU(a).filter(a=>!!a.isEnabled&&!!a.frame)),co={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:a=>a**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var cp=({startOfRange:a,endOfRange:b,current:c})=>{let d=b-a;return 0===d?0:(c-a)/d},cq=({distanceToEdge:a,thresholds:b,dragStartTime:c,shouldUseTimeDampening:d,getAutoScrollerOptions:e})=>{let f=((a,b,c=()=>co)=>{let d=c();if(a>b.startScrollingFrom)return 0;if(a<=b.maxScrollValueAt)return d.maxPixelScroll;if(a===b.startScrollingFrom)return 1;let e=cp({startOfRange:b.maxScrollValueAt,endOfRange:b.startScrollingFrom,current:a});return Math.ceil(d.maxPixelScroll*d.ease(1-e))})(a,b,e);return 0===f?0:d?Math.max(((a,b,c)=>{let d=c(),e=d.durationDampening.accelerateAt,f=d.durationDampening.stopDampeningAt,g=Date.now()-b;if(g>=f)return a;if(g<e)return 1;let h=cp({startOfRange:e,endOfRange:f,current:g});return Math.ceil(a*d.ease(h))})(f,c,e),1):f},cr=({container:a,distanceToEdges:b,dragStartTime:c,axis:d,shouldUseTimeDampening:e,getAutoScrollerOptions:f})=>{let g=((a,b,c=()=>co)=>{let d=c(),e=a[b.size]*d.startFromPercentage;return{startScrollingFrom:e,maxScrollValueAt:a[b.size]*d.maxScrollAtPercentage}})(a,d,f);return b[d.end]<b[d.start]?cq({distanceToEdge:b[d.end],thresholds:g,dragStartTime:c,shouldUseTimeDampening:e,getAutoScrollerOptions:f}):-1*cq({distanceToEdge:b[d.start],thresholds:g,dragStartTime:c,shouldUseTimeDampening:e,getAutoScrollerOptions:f})};let cs=aM(a=>0===a?0:a);var ct=({dragStartTime:a,container:b,subject:c,center:d,shouldUseTimeDampening:e,getAutoScrollerOptions:f})=>{let g={top:d.y-b.top,right:b.right-d.x,bottom:b.bottom-d.y,left:d.x-b.left},h=cr({container:b,distanceToEdges:g,dragStartTime:a,axis:a5,shouldUseTimeDampening:e,getAutoScrollerOptions:f}),i=cs({x:cr({container:b,distanceToEdges:g,dragStartTime:a,axis:a6,shouldUseTimeDampening:e,getAutoScrollerOptions:f}),y:h});if(aH(i,aE))return null;let j=(({container:a,subject:b,proposedScroll:c})=>{let d=b.height>a.height,e=b.width>a.width;return e||d?e&&d?null:{x:e?0:c.x,y:d?0:c.y}:c})({container:b,subject:c,proposedScroll:i});return j?aH(j,aE)?null:j:null};let cu=aM(a=>0===a?0:a>0?1:-1),cv=(()=>{let a=(a,b)=>a<0?a:a>b?a-b:0;return({current:b,max:c,change:d})=>{let e=aF(b,d),f={x:a(e.x,c.x),y:a(e.y,c.y)};return aH(f,aE)?null:f}})(),cw=({max:a,current:b,change:c})=>{let d={x:Math.max(b.x,a.x),y:Math.max(b.y,a.y)},e=cu(c),f=cv({max:d,current:b,change:e});return!f||0!==e.x&&0===f.x||0!==e.y&&0===f.y},cx=(a,b)=>cw({current:a.scroll.current,max:a.scroll.max,change:b}),cy=(a,b)=>{let c=a.frame;return!!c&&cw({current:c.scroll.current,max:c.scroll.max,change:b})};var cz=({state:a,dragStartTime:b,shouldUseTimeDampening:c,scrollWindow:d,scrollDroppable:e,getAutoScrollerOptions:f})=>{let g=a.current.page.borderBoxCenter,h=a.dimensions.draggables[a.critical.draggable.id].page.marginBox;if(a.isWindowScrollAllowed){let e=(({viewport:a,subject:b,center:c,dragStartTime:d,shouldUseTimeDampening:e,getAutoScrollerOptions:f})=>{let g=ct({dragStartTime:d,container:a.frame,subject:b,center:c,shouldUseTimeDampening:e,getAutoScrollerOptions:f});return g&&cx(a,g)?g:null})({dragStartTime:b,viewport:a.viewport,subject:h,center:g,shouldUseTimeDampening:c,getAutoScrollerOptions:f});if(e)return void d(e)}let i=(({center:a,destination:b,droppables:c})=>{if(b){let a=c[b];return a.frame?a:null}return cn(c).find(b=>(b.frame||av(),bu(b.frame.pageMarginBox)(a)))||null})({center:g,destination:bs(a.impact),droppables:a.dimensions.droppables});if(!i)return;let j=(({droppable:a,subject:b,center:c,dragStartTime:d,shouldUseTimeDampening:e,getAutoScrollerOptions:f})=>{let g=a.frame;if(!g)return null;let h=ct({dragStartTime:d,container:g.pageMarginBox,subject:b,center:c,shouldUseTimeDampening:e,getAutoScrollerOptions:f});return h&&cy(a,h)?h:null})({dragStartTime:b,droppable:i,subject:h,center:g,shouldUseTimeDampening:c,getAutoScrollerOptions:f});j&&e(i.descriptor.id,j)};let cA="data-rfd",cB=(()=>{let a=`${cA}-drag-handle`;return{base:a,draggableId:`${a}-draggable-id`,contextId:`${a}-context-id`}})(),cC=(()=>{let a=`${cA}-draggable`;return{base:a,contextId:`${a}-context-id`,id:`${a}-id`}})(),cD=(()=>{let a=`${cA}-droppable`;return{base:a,contextId:`${a}-context-id`,id:`${a}-id`}})(),cE={contextId:`${cA}-scroll-container-context-id`},cF=(a,b)=>a.map(a=>{let c=a.styles[b];return c?`${a.selector} { ${c} }`:""}).join(" "),cG="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?e.useLayoutEffect:e.useEffect,cH=()=>{let a=document.querySelector("head");return a||av(),a},cI=a=>{let b=document.createElement("style");return a&&b.setAttribute("nonce",a),b.type="text/css",b};function cJ(a,b){return Array.from(a.querySelectorAll(b))}var cK=a=>a&&a.ownerDocument&&a.ownerDocument.defaultView?a.ownerDocument.defaultView:window;function cL(a){return a instanceof cK(a).HTMLElement}function cM(a,b){let c=cJ(document,`[${cB.contextId}="${a}"]`);if(!c.length)return null;let d=c.find(a=>a.getAttribute(cB.draggableId)===b);return d&&cL(d)?d:null}function cN(){let a={draggables:{},droppables:{}},b=[];function c(a){b.length&&b.forEach(b=>b(a))}function d(b){return a.draggables[b]||null}function e(b){return a.droppables[b]||null}return{draggable:{register:b=>{a.draggables[b.descriptor.id]=b,c({type:"ADDITION",value:b})},update:(b,c)=>{let d=a.draggables[c.descriptor.id];d&&d.uniqueId===b.uniqueId&&(delete a.draggables[c.descriptor.id],a.draggables[b.descriptor.id]=b)},unregister:b=>{let e=b.descriptor.id,f=d(e);f&&b.uniqueId===f.uniqueId&&(delete a.draggables[e],a.droppables[b.descriptor.droppableId]&&c({type:"REMOVAL",value:b}))},getById:function(a){let b=d(a);return b||av(),b},findById:d,exists:a=>!!d(a),getAllByType:b=>Object.values(a.draggables).filter(a=>a.descriptor.type===b)},droppable:{register:b=>{a.droppables[b.descriptor.id]=b},unregister:b=>{let c=e(b.descriptor.id);c&&b.uniqueId===c.uniqueId&&delete a.droppables[b.descriptor.id]},getById:function(a){let b=e(a);return b||av(),b},findById:e,exists:a=>!!e(a),getAllByType:b=>Object.values(a.droppables).filter(a=>a.descriptor.type===b)},subscribe:function(a){return b.push(a),function(){let c=b.indexOf(a);-1!==c&&b.splice(c,1)}},clean:function(){a.draggables={},a.droppables={},b.length=0}}}var cO=f().createContext(null),cP=()=>{let a=document.body;return a||av(),a};let cQ={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},cR={separator:"::"};function cS(a,b=cR){let c=f().useId();return aC(()=>`${a}${b.separator}${c}`,[b.separator,a,c])}var cT=f().createContext(null),cU={react:"^18.0.0 || ^19.0.0"};let cV=/(\d+)\.(\d+)\.(\d+)/,cW=a=>{let b=cV.exec(a);null==b&&av();let c=Number(b[1]);return{major:c,minor:Number(b[2]),patch:Number(b[3]),raw:a}};function cX(a,b){}function cY(a){let b=(0,e.useRef)(a);return(0,e.useEffect)(()=>{b.current=a}),b}function cZ(a){return"IDLE"!==a.phase&&"DROP_ANIMATING"!==a.phase&&a.isDragging}let c$={13:!0,9:!0};var c_=a=>{c$[a.keyCode]&&a.preventDefault()};let c0=(()=>{let a="visibilitychange";return"undefined"==typeof document?a:[a,`ms${a}`,`webkit${a}`,`moz${a}`,`o${a}`].find(a=>`on${a}`in document)||a})(),c1={type:"IDLE"};function c2(){}let c3={34:!0,33:!0,36:!0,35:!0},c4={type:"IDLE"},c5=["input","button","textarea","select","option","optgroup","video","audio"],c6=(()=>{let a="matches";return"undefined"==typeof document?a:[a,"msMatchesSelector","webkitMatchesSelector"].find(a=>a in Element.prototype)||a})();function c7(a){a.preventDefault()}function c8({expected:a,phase:b,isLockActive:c,shouldWarn:d}){return!!c()&&a===b}function c9({lockAPI:a,store:b,registry:c,draggableId:d}){if(a.isClaimed())return!1;let e=c.draggable.findById(d);return!!e&&!!e.options.isEnabled&&!!cl(b.getState(),d)}let da=[function(a){let b=(0,e.useRef)(c1),c=(0,e.useRef)(as),d=aC(()=>({eventName:"mousedown",fn:function(b){if(b.defaultPrevented||0!==b.button||b.ctrlKey||b.metaKey||b.shiftKey||b.altKey)return;let d=a.findClosestDraggableId(b);if(!d)return;let e=a.tryGetLock(d,h,{sourceEvent:b});if(!e)return;b.preventDefault();let f={x:b.clientX,y:b.clientY};c.current(),k(e,f)}}),[a]),f=aC(()=>({eventName:"webkitmouseforcewillbegin",fn:b=>{if(b.defaultPrevented)return;let c=a.findClosestDraggableId(b);if(!c)return;let d=a.findOptionsForDraggable(c);d&&!d.shouldRespectForcePress&&a.canGetLock(c)&&b.preventDefault()}}),[a]),g=aD(function(){c.current=at(window,[f,d],{passive:!1,capture:!0})},[f,d]),h=aD(()=>{"IDLE"!==b.current.type&&(b.current=c1,c.current(),g())},[g]),i=aD(()=>{let a=b.current;h(),"DRAGGING"===a.type&&a.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===a.type&&a.actions.abort()},[h]),j=aD(function(){c.current=at(window,function({cancel:a,completed:b,getPhase:c,setPhase:d}){return[{eventName:"mousemove",fn:a=>{var b;let{button:e,clientX:f,clientY:g}=a;if(0!==e)return;let h={x:f,y:g},i=c();if("DRAGGING"===i.type){a.preventDefault(),i.actions.move(h);return}"PENDING"!==i.type&&av(),b=i.point,(Math.abs(h.x-b.x)>=5||Math.abs(h.y-b.y)>=5)&&(a.preventDefault(),d({type:"DRAGGING",actions:i.actions.fluidLift(h)}))}},{eventName:"mouseup",fn:d=>{let e=c();if("DRAGGING"!==e.type)return void a();d.preventDefault(),e.actions.drop({shouldBlockNextClick:!0}),b()}},{eventName:"mousedown",fn:b=>{"DRAGGING"===c().type&&b.preventDefault(),a()}},{eventName:"keydown",fn:b=>{if("PENDING"===c().type)return void a();if(27===b.keyCode){b.preventDefault(),a();return}c_(b)}},{eventName:"resize",fn:a},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===c().type&&a()}},{eventName:"webkitmouseforcedown",fn:b=>{let d=c();if("IDLE"===d.type&&av(),d.actions.shouldRespectForcePress())return void a();b.preventDefault()}},{eventName:c0,fn:a}]}({cancel:i,completed:h,getPhase:()=>b.current,setPhase:a=>{b.current=a}}),{capture:!0,passive:!1})},[i,h]),k=aD(function(a,c){"IDLE"!==b.current.type&&av(),b.current={type:"PENDING",point:c,actions:a},j()},[j]);cG(function(){return g(),function(){c.current()}},[g])},function(a){let b=(0,e.useRef)(c2),c=aC(()=>({eventName:"keydown",fn:function(c){if(c.defaultPrevented||32!==c.keyCode)return;let e=a.findClosestDraggableId(c);if(!e)return;let f=a.tryGetLock(e,i,{sourceEvent:c});if(!f)return;c.preventDefault();let g=!0,h=f.snapLift();function i(){g||av(),g=!1,b.current(),d()}b.current(),b.current=at(window,function(a,b){function c(){b(),a.cancel()}return[{eventName:"keydown",fn:d=>{if(27===d.keyCode){d.preventDefault(),c();return}if(32===d.keyCode){d.preventDefault(),b(),a.drop();return}if(40===d.keyCode){d.preventDefault(),a.moveDown();return}if(38===d.keyCode){d.preventDefault(),a.moveUp();return}if(39===d.keyCode){d.preventDefault(),a.moveRight();return}if(37===d.keyCode){d.preventDefault(),a.moveLeft();return}if(c3[d.keyCode])return void d.preventDefault();c_(d)}},{eventName:"mousedown",fn:c},{eventName:"mouseup",fn:c},{eventName:"click",fn:c},{eventName:"touchstart",fn:c},{eventName:"resize",fn:c},{eventName:"wheel",fn:c,options:{passive:!0}},{eventName:c0,fn:c}]}(h,i),{capture:!0,passive:!1})}}),[a]),d=aD(function(){b.current=at(window,[c],{passive:!1,capture:!0})},[c]);cG(function(){return d(),function(){b.current()}},[d])},function(a){let b=(0,e.useRef)(c4),c=(0,e.useRef)(as),d=aD(function(){return b.current},[]),f=aD(function(a){b.current=a},[]),g=aC(()=>({eventName:"touchstart",fn:function(b){if(b.defaultPrevented)return;let d=a.findClosestDraggableId(b);if(!d)return;let e=a.tryGetLock(d,i,{sourceEvent:b});if(!e)return;let{clientX:f,clientY:g}=b.touches[0];c.current(),m(e,{x:f,y:g})}}),[a]),h=aD(function(){c.current=at(window,[g],{capture:!0,passive:!1})},[g]),i=aD(()=>{let a=b.current;"IDLE"!==a.type&&("PENDING"===a.type&&clearTimeout(a.longPressTimerId),f(c4),c.current(),h())},[h,f]),j=aD(()=>{let a=b.current;i(),"DRAGGING"===a.type&&a.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===a.type&&a.actions.abort()},[i]),k=aD(function(){let a={capture:!0,passive:!1},b={cancel:j,completed:i,getPhase:d},e=at(window,function({cancel:a,completed:b,getPhase:c}){return[{eventName:"touchmove",options:{capture:!1},fn:b=>{let d=c();if("DRAGGING"!==d.type)return void a();d.hasMoved=!0;let{clientX:e,clientY:f}=b.touches[0];b.preventDefault(),d.actions.move({x:e,y:f})}},{eventName:"touchend",fn:d=>{let e=c();if("DRAGGING"!==e.type)return void a();d.preventDefault(),e.actions.drop({shouldBlockNextClick:!0}),b()}},{eventName:"touchcancel",fn:b=>{if("DRAGGING"!==c().type)return void a();b.preventDefault(),a()}},{eventName:"touchforcechange",fn:b=>{let d=c();"IDLE"===d.type&&av();let e=b.touches[0];if(!e||!(e.force>=.15))return;let f=d.actions.shouldRespectForcePress();if("PENDING"===d.type){f&&a();return}if(f)return d.hasMoved?void b.preventDefault():void a();b.preventDefault()}},{eventName:c0,fn:a}]}(b),a),f=at(window,function({cancel:a,getPhase:b}){return[{eventName:"orientationchange",fn:a},{eventName:"resize",fn:a},{eventName:"contextmenu",fn:a=>{a.preventDefault()}},{eventName:"keydown",fn:c=>{if("DRAGGING"!==b().type)return void a();27===c.keyCode&&c.preventDefault(),a()}},{eventName:c0,fn:a}]}(b),a);c.current=function(){e(),f()}},[j,d,i]),l=aD(function(){let a=d();"PENDING"!==a.type&&av(),f({type:"DRAGGING",actions:a.actions.fluidLift(a.point),hasMoved:!1})},[d,f]),m=aD(function(a,b){"IDLE"!==d().type&&av(),f({type:"PENDING",point:b,actions:a,longPressTimerId:setTimeout(l,120)}),k()},[k,d,f,l]);cG(function(){return h(),function(){c.current();let a=d();"PENDING"===a.type&&(clearTimeout(a.longPressTimerId),f(c4))}},[d,h,f]),cG(function(){return at(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}];function db(a){return a.current||av(),a.current}function dc(a){let{contextId:b,setCallbacks:c,sensors:d,nonce:h,dragHandleUsageInstructions:k}=a,m=(0,e.useRef)(null);cX(()=>{((a,b)=>{let c,d;if(c=cW(a),(d=cW(b)).major>c.major||!(d.major<c.major)&&(d.minor>c.minor||!(d.minor<c.minor)&&d.patch>=c.patch))return})(cU.react,f().version);let a=document.doctype;a&&(a.name.toLowerCase(),a.publicId)},[]);let p=cY(a),q=aD(()=>{let a;return{onBeforeCapture:b=>{(0,g.flushSync)(()=>{a.onBeforeCapture&&a.onBeforeCapture(b)})},onBeforeDragStart:(a=p.current).onBeforeDragStart,onDragStart:a.onDragStart,onDragEnd:a.onDragEnd,onDragUpdate:a.onDragUpdate}},[p]),r=aD(()=>{let a;return a=p.current,{...co,...a.autoScrollerOptions,durationDampening:{...co.durationDampening,...a.autoScrollerOptions}}},[p]),s=function(a){let b=aC(()=>`rfd-announcement-${a}`,[a]),c=(0,e.useRef)(null);return(0,e.useEffect)(function(){let a=document.createElement("div");return c.current=a,a.id=b,a.setAttribute("aria-live","assertive"),a.setAttribute("aria-atomic","true"),aq(a.style,cQ),cP().appendChild(a),function(){setTimeout(function(){let b=cP();b.contains(a)&&b.removeChild(a),a===c.current&&(c.current=null)})}},[b]),aD(a=>{let b=c.current;if(b){b.textContent=a;return}},[])}(b),t=function({contextId:a,text:b}){let c=cS("hidden-text",{separator:"-"}),d=aC(()=>(function({contextId:a,uniqueId:b}){return`rfd-hidden-text-${a}-${b}`})({contextId:a,uniqueId:c}),[c,a]);return(0,e.useEffect)(function(){let a=document.createElement("div");return a.id=d,a.textContent=b,a.style.display="none",cP().appendChild(a),function(){let b=cP();b.contains(a)&&b.removeChild(a)}},[d,b]),d}({contextId:b,text:k}),u=function(a,b){let c=aC(()=>(a=>{let b=b=>`[${b}="${a}"]`,c=(()=>{let a=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:b(cB.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:a,dragging:"pointer-events: none;",dropAnimating:a}}})(),d=(()=>{let a=`
      transition: ${b1.outOfTheWay};
    `;return{selector:b(cC.contextId),styles:{dragging:a,dropAnimating:a,userCancel:a}}})(),e=[d,c,{selector:b(cD.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:cF(e,"always"),resting:cF(e,"resting"),dragging:cF(e,"dragging"),dropAnimating:cF(e,"dropAnimating"),userCancel:cF(e,"userCancel")}})(a),[a]),d=(0,e.useRef)(null),f=(0,e.useRef)(null),g=aD(aR(a=>{let b=f.current;b||av(),b.textContent=a}),[]),h=aD(a=>{let b=d.current;b||av(),b.textContent=a},[]);cG(()=>{(d.current||f.current)&&av();let e=cI(b),i=cI(b);return d.current=e,f.current=i,e.setAttribute(`${cA}-always`,a),i.setAttribute(`${cA}-dynamic`,a),cH().appendChild(e),cH().appendChild(i),h(c.always),g(c.resting),()=>{let a=a=>{let b=a.current;b||av(),cH().removeChild(b),a.current=null};a(d),a(f)}},[b,h,g,c.always,c.resting,a]);let i=aD(()=>g(c.dragging),[g,c.dragging]),j=aD(a=>{if("DROP"===a)return void g(c.dropAnimating);g(c.userCancel)},[g,c.dropAnimating,c.userCancel]),k=aD(()=>{f.current&&g(c.resting)},[g,c.resting]);return aC(()=>({dragging:i,dropping:j,resting:k}),[i,j,k])}(b,h),v=aD(a=>{db(m).dispatch(a)},[]),w=aC(()=>n({publishWhileDragging:bL,updateDroppableScroll:bN,updateDroppableIsEnabled:bO,updateDroppableIsCombineEnabled:bP,collectionStarting:bM},v),[v]),x=function(){let a=aC(cN,[]);return(0,e.useEffect)(()=>function(){a.clean()},[a]),a}(),y=aC(()=>((a,b)=>{let c=null,d=function({registry:a,callbacks:b}){let c=cg(),d=null,e=()=>{d||(b.collectionStarting(),d=requestAnimationFrame(()=>{d=null,bE();let{additions:e,removals:f,modified:g}=c,h=Object.keys(e).map(b=>a.draggable.getById(b).getDimension(aE)).sort((a,b)=>a.descriptor.index-b.descriptor.index),i=Object.keys(g).map(b=>{let c=a.droppable.getById(b).callbacks.getScrollWhileDragging();return{droppableId:b,scroll:c}}),j={additions:h,removals:Object.keys(f),modified:i};c=cg(),bF(),b.publish(j)}))};return{add:a=>{let b=a.descriptor.id;c.additions[b]=a,c.modified[a.descriptor.droppableId]=!0,c.removals[b]&&delete c.removals[b],e()},remove:a=>{let b=a.descriptor;c.removals[b.id]=!0,c.modified[b.droppableId]=!0,c.additions[b.id]&&delete c.additions[b.id],e()},stop:()=>{d&&(cancelAnimationFrame(d),d=null,c=cg())}}}({callbacks:{publish:b.publishWhileDragging,collectionStarting:b.collectionStarting},registry:a}),e=b=>{c||av();let e=c.critical.draggable;"ADDITION"===b.type&&ck(a,e,b.value)&&d.add(b.value),"REMOVAL"===b.type&&ck(a,e,b.value)&&d.remove(b.value)};return{updateDroppableIsEnabled:(d,e)=>{a.droppable.exists(d)||av(),c&&b.updateDroppableIsEnabled({id:d,isEnabled:e})},updateDroppableIsCombineEnabled:(d,e)=>{c&&(a.droppable.exists(d)||av(),b.updateDroppableIsCombineEnabled({id:d,isCombineEnabled:e}))},scrollDroppable:(b,d)=>{c&&a.droppable.getById(b).callbacks.scroll(d)},updateDroppableScroll:(d,e)=>{c&&(a.droppable.exists(d)||av(),b.updateDroppableScroll({id:d,newScroll:e}))},startPublishing:b=>{c&&av();let d=a.draggable.getById(b.draggableId),f=a.droppable.getById(d.descriptor.droppableId),g={draggable:d.descriptor,droppable:f.descriptor};return c={critical:g,unsubscribe:a.subscribe(e)},(({critical:a,scrollOptions:b,registry:c})=>{bE();let d=(()=>{let a=b8(),b=cj(),c=a.y,d=a.x,e=ci(),f=e.clientWidth;return{frame:af({top:c,left:d,right:d+f,bottom:c+e.clientHeight}),scroll:{initial:a,current:a,max:b,diff:{value:aE,displacement:aE}}}})(),e=d.scroll.current,f=a.droppable,g=c.droppable.getAllByType(f.type).map(a=>a.callbacks.getDimensionAndWatchScroll(e,b)),h={draggables:aT(c.draggable.getAllByType(a.draggable.type).map(a=>a.getDimension(e))),droppables:aS(g)};return bF(),{dimensions:h,critical:a,viewport:d}})({critical:g,registry:a,scrollOptions:b.scrollOptions})},stopPublishing:()=>{if(!c)return;d.stop();let b=c.critical.droppable;a.droppable.getAllByType(b.type).forEach(a=>a.callbacks.dragStopped()),c.unsubscribe(),c=null}}})(x,w),[x,w]),z=aC(()=>(({scrollDroppable:a,scrollWindow:b,move:c,getAutoScrollerOptions:d})=>{let e=(({scrollWindow:a,scrollDroppable:b,getAutoScrollerOptions:c=()=>co})=>{let d=ap(a),e=ap(b),f=null,g=a=>{f||av();let{shouldUseTimeDampening:b,dragStartTime:g}=f;cz({state:a,scrollWindow:d,scrollDroppable:e,dragStartTime:g,shouldUseTimeDampening:b,getAutoScrollerOptions:c})};return{start:a=>{bE(),f&&av();let b=Date.now(),d=!1,e=()=>{d=!0};cz({state:a,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:e,scrollDroppable:e,getAutoScrollerOptions:c}),f={dragStartTime:b,shouldUseTimeDampening:d},bF(),d&&g(a)},stop:()=>{f&&(d.cancel(),e.cancel(),f=null)},scroll:g}})({scrollWindow:b,scrollDroppable:a,getAutoScrollerOptions:d}),f=(({move:a,scrollDroppable:b,scrollWindow:c})=>d=>{let e=d.scrollJumpRequest;if(!e)return;let f=bs(d.impact);f||av();let g=((a,c)=>{if(!cy(a,c))return c;let d=((a,b)=>{let c=a.frame;return c&&cy(a,b)?cv({current:c.scroll.current,max:c.scroll.max,change:b}):null})(a,c);if(!d)return b(a.descriptor.id,c),null;let e=aG(c,d);return b(a.descriptor.id,e),aG(c,e)})(d.dimensions.droppables[f],e);if(!g)return;let h=d.viewport,i=((a,b,d)=>{if(!a||!cx(b,d))return d;let e=((a,b)=>{if(!cx(a,b))return null;let c=a.scroll.max;return cv({current:a.scroll.current,max:c,change:b})})(b,d);if(!e)return c(d),null;let f=aG(d,e);return c(f),aG(d,f)})(d.isWindowScrollAllowed,h,g);i&&a({client:aF(d.current.client.selection,i)})})({move:c,scrollWindow:b,scrollDroppable:a});return{scroll:a=>{if(!d().disabled&&"DRAGGING"===a.phase){if("FLUID"===a.movementMode)return void e.scroll(a);a.scrollJumpRequest&&f(a)}},start:e.start,stop:e.stop}})({scrollWindow:cm,scrollDroppable:y.scrollDroppable,getAutoScrollerOptions:r,...n({move:bQ},v)}),[y.scrollDroppable,v,r]),A=function(a){let b=(0,e.useRef)({}),c=(0,e.useRef)(null),d=(0,e.useRef)(null),f=(0,e.useRef)(!1),g=aD(function(a,c){let d={id:a,focus:c};return b.current[a]=d,function(){let c=b.current;c[a]!==d&&delete c[a]}},[]),h=aD(function(b){let c=cM(a,b);c&&c!==document.activeElement&&c.focus()},[a]),i=aD(function(a,b){c.current===a&&(c.current=b)},[]),j=aD(function(){!d.current&&f.current&&(d.current=requestAnimationFrame(()=>{d.current=null;let a=c.current;a&&h(a)}))},[h]),k=aD(function(a){c.current=null;let b=document.activeElement;b&&b.getAttribute(cB.draggableId)===a&&(c.current=a)},[]);return cG(()=>(f.current=!0,function(){f.current=!1;let a=d.current;a&&cancelAnimationFrame(a)}),[]),aC(()=>({register:g,tryRecordFocus:k,tryRestoreFocusRecorded:j,tryShiftRecord:i}),[g,k,j,i])}(b),B=aC(()=>(({dimensionMarshal:a,focusMarshal:b,styleMarshal:c,getResponders:d,announce:e,autoScroller:f})=>{var g;let h,k,m;return function a(b,c,d){if("function"!=typeof b)throw Error(i(2));if("function"==typeof c&&"function"==typeof d||"function"==typeof d&&"function"==typeof arguments[3])throw Error(i(0));if("function"==typeof c&&void 0===d&&(d=c,c=void 0),void 0!==d){if("function"!=typeof d)throw Error(i(1));return d(a)(b,c)}let e=b,f=c,g=new Map,h=g,k=0,m=!1;function n(){h===g&&(h=new Map,g.forEach((a,b)=>{h.set(b,a)}))}function o(){if(m)throw Error(i(3));return f}function p(a){if("function"!=typeof a)throw Error(i(4));if(m)throw Error(i(5));let b=!0;n();let c=k++;return h.set(c,a),function(){if(b){if(m)throw Error(i(6));b=!1,n(),h.delete(c),g=null}}}function q(a){if(!function(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}(a))throw Error(i(7));if(void 0===a.type)throw Error(i(8));if("string"!=typeof a.type)throw Error(i(17));if(m)throw Error(i(9));try{m=!0,f=e(f,a)}finally{m=!1}return(g=h).forEach(a=>{a()}),a}return q({type:l.INIT}),{dispatch:q,subscribe:p,getState:o,replaceReducer:function(a){if("function"!=typeof a)throw Error(i(10));e=a,q({type:l.REPLACE})},[j]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(i(11));function b(){a.next&&a.next(o())}return b(),{unsubscribe:p(b)}},[j](){return this}}}}}(bJ,o(function(...a){return b=>(c,d)=>{let e=b(c,d),f=()=>{throw Error(i(15))},g={getState:e.getState,dispatch:(a,...b)=>f(a,...b)};return f=o(...a.map(a=>a(g)))(e.dispatch),{...e,dispatch:f}}}((h=c,()=>a=>b=>{bK(b,"INITIAL_PUBLISH")&&h.dragging(),bK(b,"DROP_ANIMATE")&&h.dropping(b.payload.completed.result.reason),(bK(b,"FLUSH")||bK(b,"DROP_COMPLETE"))&&h.resting(),a(b)}),()=>b=>c=>{(bK(c,"DROP_COMPLETE")||bK(c,"FLUSH")||bK(c,"DROP_ANIMATE"))&&a.stopPublishing(),b(c)},({getState:b,dispatch:c})=>d=>e=>{if(!bK(e,"LIFT"))return void d(e);let{id:f,clientSelection:g,movementMode:h}=e.payload,i=b();"DROP_ANIMATING"===i.phase&&c(bW({completed:i.completed})),"IDLE"!==b().phase&&av(),c(bV()),c({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:f,movementMode:h}});let{critical:j,dimensions:k,viewport:l}=a.startPublishing({draggableId:f,scrollOptions:{shouldPublishImmediately:"SNAP"===h}});c({type:"INITIAL_PUBLISH",payload:{critical:j,dimensions:k,clientSelection:g,movementMode:h,viewport:l}})},b7,cd,ce,cf,(k=f,a=>b=>c=>{let d;if(bK(d=c,"DROP_COMPLETE")||bK(d,"DROP_ANIMATE")||bK(d,"FLUSH")){k.stop(),b(c);return}if(bK(c,"INITIAL_PUBLISH")){b(c);let d=a.getState();"DRAGGING"!==d.phase&&av(),k.start(d);return}b(c),k.scroll(a.getState())}),b9,(g=b,m=!1,()=>a=>b=>{if(bK(b,"INITIAL_PUBLISH")){m=!0,g.tryRecordFocus(b.payload.critical.draggable.id),a(b),g.tryRestoreFocusRecorded();return}if(a(b),m){if(bK(b,"FLUSH")){m=!1,g.tryRestoreFocusRecorded();return}if(bK(b,"DROP_COMPLETE")){m=!1;let a=b.payload.completed.result;a.combine&&g.tryShiftRecord(a.draggableId,a.combine.draggableId),g.tryRestoreFocusRecorded()}}}),((a,b)=>{let c=((a,b)=>{let c=(()=>{let a=[];return{add:b=>{let c=setTimeout(()=>(b=>{let c=a.findIndex(a=>a.timerId===b);-1===c&&av();let[d]=a.splice(c,1);d.callback()})(c));a.push({timerId:c,callback:b})},flush:()=>{if(!a.length)return;let b=[...a];a.length=0,b.forEach(a=>{clearTimeout(a.timerId),a.callback()})}}})(),d=null,e=c=>{d||av(),d=null,ca("onDragEnd",()=>cc(a().onDragEnd,c,b,aA.onDragEnd))};return{beforeCapture:(b,c)=>{d&&av(),ca("onBeforeCapture",()=>{let d=a().onBeforeCapture;d&&d({draggableId:b,mode:c})})},beforeStart:(b,c)=>{d&&av(),ca("onBeforeDragStart",()=>{let d=a().onBeforeDragStart;d&&d(cb(b,c))})},start:(e,f)=>{d&&av();let g=cb(e,f);d={mode:f,lastCritical:e,lastLocation:g.source,lastCombine:null},c.add(()=>{ca("onDragStart",()=>cc(a().onDragStart,g,b,aA.onDragStart))})},update:(e,f)=>{let g,h,i,j,k=aX(f),l=aY(f);d||av();let m=!((a,b)=>{if(a===b)return!0;let c=a.draggable.id===b.draggable.id&&a.draggable.droppableId===b.draggable.droppableId&&a.draggable.type===b.draggable.type&&a.draggable.index===b.draggable.index,d=a.droppable.id===b.droppable.id&&a.droppable.type===b.droppable.type;return c&&d})(e,d.lastCritical);m&&(d.lastCritical=e);let n=(g=d.lastLocation,h=k,(null!=g||null!=h)&&(null==g||null==h||g.droppableId!==h.droppableId||g.index!==h.index));n&&(d.lastLocation=k);let o=(i=d.lastCombine,j=l,(null!=i||null!=j)&&(null==i||null==j||i.draggableId!==j.draggableId||i.droppableId!==j.droppableId));if(o&&(d.lastCombine=l),!m&&!n&&!o)return;let p={...cb(e,d.mode),combine:l,destination:k};c.add(()=>{ca("onDragUpdate",()=>cc(a().onDragUpdate,p,b,aA.onDragUpdate))})},flush:()=>{d||av(),c.flush()},drop:e,abort:()=>{d&&e({...cb(d.lastCritical,d.mode),combine:null,destination:null,reason:"CANCEL"})}}})(a,b);return a=>b=>d=>{if(bK(d,"BEFORE_INITIAL_CAPTURE"))return void c.beforeCapture(d.payload.draggableId,d.payload.movementMode);if(bK(d,"INITIAL_PUBLISH")){let a=d.payload.critical;c.beforeStart(a,d.payload.movementMode),b(d),c.start(a,d.payload.movementMode);return}if(bK(d,"DROP_COMPLETE")){let a=d.payload.completed.result;c.flush(),b(d),c.drop(a);return}if(b(d),bK(d,"FLUSH"))return void c.abort();let e=a.getState();"DRAGGING"===e.phase&&c.update(e.critical,e.impact)}})(d,e))))})({announce:s,autoScroller:z,dimensionMarshal:y,focusMarshal:A,getResponders:q,styleMarshal:u}),[s,z,y,A,q,u]);m.current=B;let C=aD(()=>{let a=db(m);"IDLE"!==a.getState().phase&&a.dispatch(bV())},[]),D=aD(()=>{let a=db(m).getState();return"DROP_ANIMATING"===a.phase||"IDLE"!==a.phase&&a.isDragging},[]);c(aC(()=>({isDragging:D,tryAbort:C}),[D,C]));let E=aD(a=>cl(db(m).getState(),a),[]),F=aD(()=>bt(db(m).getState()),[]),G=aC(()=>({marshal:y,focus:A,contextId:b,canLift:E,isMovementAllowed:F,dragHandleUsageInstructionsId:t,registry:x}),[b,y,t,A,E,F,x]);return!function({contextId:a,store:b,registry:c,customSensors:d,enableDefaultSensors:f}){let g=[...f?da:[],...d||[]],h=(0,e.useState)(()=>(function(){let a=null;function b(){a||av(),a=null}return{isClaimed:function(){return!!a},isActive:function(b){return b===a},claim:function(b){a&&av();let c={abandon:b};return a=c,c},release:b,tryAbandon:function(){a&&(a.abandon(),b())}}})())[0],i=aD(function(a,b){cZ(a)&&!cZ(b)&&h.tryAbandon()},[h]);cG(function(){let a=b.getState();return b.subscribe(()=>{let c=b.getState();i(a,c),a=c})},[h,b,i]),cG(()=>h.tryAbandon,[h.tryAbandon]);let j=aD(a=>c9({lockAPI:h,registry:c,store:b,draggableId:a}),[h,c,b]),k=aD((d,e,f)=>(function({lockAPI:a,contextId:b,store:c,registry:d,draggableId:e,forceSensorStop:f,sourceEvent:g}){if(!c9({lockAPI:a,store:c,registry:d,draggableId:e}))return null;let h=d.draggable.getById(e),i=function(a,b){let c=cJ(document,`[${cC.contextId}="${a}"]`).find(a=>a.getAttribute(cC.id)===b);return c&&cL(c)?c:null}(b,h.descriptor.id);if(!i||g&&!h.options.canDragInteractiveElements&&function(a,b){let c=b.target;return!!cL(c)&&function a(b,c){if(null==c)return!1;if(c5.includes(c.tagName.toLowerCase()))return!0;let d=c.getAttribute("contenteditable");return"true"===d||""===d||c!==b&&a(b,c.parentElement)}(a,c)}(i,g))return null;let j=a.claim(f||as),k="PRE_DRAG";function l(){return h.options.shouldRespectForcePress}function m(){return a.isActive(j)}let n=(function(a,b){c8({expected:a,phase:k,isLockActive:m,shouldWarn:!0})&&c.dispatch(b())}).bind(null,"DRAGGING");function o(b){function d(){a.release(),k="COMPLETED"}function e(a,f={shouldBlockNextClick:!1}){b.cleanup(),f.shouldBlockNextClick&&setTimeout(at(window,[{eventName:"click",fn:c7,options:{once:!0,passive:!1,capture:!0}}])),d(),c.dispatch(bX({reason:a}))}return"PRE_DRAG"!==k&&(d(),av()),c.dispatch({type:"LIFT",payload:b.liftActionArgs}),k="DRAGGING",{isActive:()=>c8({expected:"DRAGGING",phase:k,isLockActive:m,shouldWarn:!1}),shouldRespectForcePress:l,drop:a=>e("DROP",a),cancel:a=>e("CANCEL",a),...b.actions}}return{isActive:()=>c8({expected:"PRE_DRAG",phase:k,isLockActive:m,shouldWarn:!1}),shouldRespectForcePress:l,fluidLift:function(a){let b=ap(a=>{n(()=>bQ({client:a}))});return{...o({liftActionArgs:{id:e,clientSelection:a,movementMode:"FLUID"},cleanup:()=>b.cancel(),actions:{move:b}}),move:b}},snapLift:function(){return o({liftActionArgs:{id:e,clientSelection:af(i.getBoundingClientRect()).center,movementMode:"SNAP"},cleanup:as,actions:{moveUp:()=>n(bR),moveRight:()=>n(bT),moveDown:()=>n(bS),moveLeft:()=>n(bU)}})},abort:function(){c8({expected:"PRE_DRAG",phase:k,isLockActive:m,shouldWarn:!0})&&a.release()}}})({lockAPI:h,registry:c,contextId:a,store:b,draggableId:d,forceSensorStop:e||null,sourceEvent:f&&f.sourceEvent?f.sourceEvent:null}),[a,h,c,b]),l=aD(b=>(function(a,b){let c=function(a,b){let c=b.target;if(!(c instanceof cK(c).Element))return null;let d=`[${cB.contextId}="${a}"]`,e=c.closest?c.closest(d):function a(b,c){return null==b?null:b[c6](c)?b:a(b.parentElement,c)}(c,d);return e&&cL(e)?e:null}(a,b);return c?c.getAttribute(cB.draggableId):null})(a,b),[a]),m=aD(a=>{let b=c.draggable.findById(a);return b?b.options:null},[c.draggable]),n=aD(function(){h.isClaimed()&&(h.tryAbandon(),"IDLE"!==b.getState().phase&&b.dispatch(bV()))},[h,b]),o=aD(()=>h.isClaimed(),[h]),p=aC(()=>({canGetLock:j,tryGetLock:k,findClosestDraggableId:l,findOptionsForDraggable:m,tryReleaseLock:n,isLockClaimed:o}),[j,k,l,m,n,o]);for(let a=0;a<g.length;a++)g[a](p)}({contextId:b,store:B,registry:x,customSensors:d||null,enableDefaultSensors:!1!==a.enableDefaultSensors}),(0,e.useEffect)(()=>C,[C]),f().createElement(cT.Provider,{value:G},f().createElement(ae,{context:cO,store:B},a.children))}function dd(a){let b=f().useId(),c=a.dragHandleUsageInstructions||aA.dragHandleUsageInstructions;return f().createElement(aw,null,d=>f().createElement(dc,{nonce:a.nonce,contextId:b,setCallbacks:d,dragHandleUsageInstructions:c,enableDefaultSensors:a.enableDefaultSensors,sensors:a.sensors,onBeforeCapture:a.onBeforeCapture,onBeforeDragStart:a.onBeforeDragStart,onDragStart:a.onDragStart,onDragUpdate:a.onDragUpdate,onDragEnd:a.onDragEnd,autoScrollerOptions:a.autoScrollerOptions},a.children))}let de={dragging:5e3,dropAnimating:4500};var df=f().createContext(null);function dg(a){a&&cL(a)||av()}function dh(a){let b=(0,e.useContext)(a);return b||av(),b}function di(a){a.preventDefault()}var dj=(a,b)=>a===b,dk=a=>{let{combine:b,destination:c}=a;return c?c.droppableId:b?b.droppableId:null};function dl(a=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:a,combineWith:null}}let dm={mapped:{type:"SECONDARY",offset:aE,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:dl(null)}},dn=ad(()=>{let a=function(){let a=aR((a,b)=>({x:a,y:b})),b=aR((a,b,c=null,d=null,e=null)=>({isDragging:!0,isClone:b,isDropAnimating:!!e,dropAnimation:e,mode:a,draggingOver:c,combineWith:d,combineTargetFor:null})),c=aR((a,c,d,e,f=null,g=null,h=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:f,combineWith:g,mode:c,offset:a,dimension:d,forceShouldAnimate:h,snapshot:b(c,e,f,g,null)}}));return(d,e)=>{if(cZ(d)){let b;if(d.critical.draggable.id!==e.draggableId)return null;let f=d.current.client.offset,g=d.dimensions.draggables[e.draggableId],h=bs(d.impact),i=(b=d.impact).at&&"COMBINE"===b.at.type?b.at.combine.draggableId:null,j=d.forceShouldAnimate;return c(a(f.x,f.y),d.movementMode,g,e.isClone,h,i,j)}if("DROP_ANIMATING"===d.phase){let a=d.completed;if(a.result.draggableId!==e.draggableId)return null;let c=e.isClone,f=d.dimensions.draggables[e.draggableId],g=a.result,h=g.mode,i=dk(g),j=g.combine?g.combine.draggableId:null,k={duration:d.dropDuration,curve:bZ.drop,moveTo:d.newHomeClientOffset,opacity:j?b$.opacity.drop:null,scale:j?b$.scale.drop:null};return{mapped:{type:"DRAGGING",offset:d.newHomeClientOffset,dimension:f,dropping:k,draggingOver:i,combineWith:j,mode:h,forceShouldAnimate:null,snapshot:b(h,c,i,j,k)}}}return null}}(),b=function(){let a=aR((a,b)=>({x:a,y:b})),b=aR(dl),c=aR((a,c=null,d)=>({mapped:{type:"SECONDARY",offset:a,combineTargetFor:c,shouldAnimateDisplacement:d,snapshot:b(c)}})),d=a=>a?c(aE,a,!0):null,e=(b,e,f,g)=>{let h=f.displaced.visible[b],i=!!(g.inVirtualList&&g.effected[b]),j=aY(f),k=j&&j.draggableId===b?e:null;if(!h){if(!i)return d(k);if(f.displaced.invisible[b])return null;let e=aI(g.displacedBy.point);return c(a(e.x,e.y),k,!0)}if(i)return d(k);let l=f.displacedBy.point;return c(a(l.x,l.y),k,h.shouldAnimate)};return(a,b)=>{if(cZ(a))return a.critical.draggable.id===b.draggableId?null:e(b.draggableId,a.critical.draggable.id,a.impact,a.afterCritical);if("DROP_ANIMATING"===a.phase){let c=a.completed;return c.result.draggableId===b.draggableId?null:e(b.draggableId,c.result.draggableId,c.impact,c.afterCritical)}return null}}();return(c,d)=>a(c,d)||b(c,d)||dm},{dropAnimationFinished:bY},null,{context:cO,areStatePropsEqual:dj})(a=>{let b=(0,e.useRef)(null),c=aD((a=null)=>{b.current=a},[]),d=aD(()=>b.current,[]),{contextId:h,dragHandleUsageInstructionsId:i,registry:j}=dh(cT),{type:k,droppableId:l}=dh(df),m=aC(()=>({id:a.draggableId,index:a.index,type:k,droppableId:l}),[a.draggableId,a.index,k,l]),{children:n,draggableId:o,isEnabled:p,shouldRespectForcePress:q,canDragInteractiveElements:r,isClone:s,mapped:t,dropAnimationFinished:u}=a;!function(a,b,c){cX(()=>{let d=a.draggableId;d||av(!1),"string"!=typeof d&&av(!1),Number.isInteger(a.index)||av(!1),"DRAGGING"!==a.mapped.type&&(dg(c()),a.isEnabled&&(cM(b,d)||av(!1)))})}(a,h,d),s||function(a){let b=cS("draggable"),{descriptor:c,registry:d,getDraggableRef:f,canDragInteractiveElements:g,shouldRespectForcePress:h,isEnabled:i}=a,j=aC(()=>({canDragInteractiveElements:g,shouldRespectForcePress:h,isEnabled:i}),[g,i,h]),k=aD(a=>{let b=f();return b||av(),function(a,b,c=aE){let d=window.getComputedStyle(b),e=an(b.getBoundingClientRect(),d),f=am(e,c),g={client:e,tagName:b.tagName.toLowerCase(),display:d.display};return{descriptor:a,placeholder:g,displaceBy:{x:e.marginBox.width,y:e.marginBox.height},client:e,page:f}}(c,b,a)},[c,f]),l=aC(()=>({uniqueId:b,descriptor:c,options:j,getDimension:k}),[c,k,j,b]),m=(0,e.useRef)(l),n=(0,e.useRef)(!0);cG(()=>(d.draggable.register(m.current),()=>d.draggable.unregister(m.current)),[d.draggable]),cG(()=>{if(n.current){n.current=!1;return}let a=m.current;m.current=l,d.draggable.update(l,a)},[l,d.draggable])}(aC(()=>({descriptor:m,registry:j,getDraggableRef:d,canDragInteractiveElements:r,shouldRespectForcePress:q,isEnabled:p}),[m,j,d,r,q,p]));let v=aC(()=>p?{tabIndex:0,role:"button","aria-describedby":i,"data-rfd-drag-handle-draggable-id":o,"data-rfd-drag-handle-context-id":h,draggable:!1,onDragStart:di}:null,[h,i,o,p]),w=aD(a=>{"DRAGGING"===t.type&&t.dropping&&"transform"===a.propertyName&&(0,g.flushSync)(u)},[u,t]),x=aC(()=>{let a=function(a){return"DRAGGING"===a.type?function(a){let b=a.dimension.client,{offset:c,combineWith:d,dropping:e}=a,f=!!d,g=null!=a.forceShouldAnimate?a.forceShouldAnimate:"SNAP"===a.mode,h=!!e,i=h?b3.drop(c,f):b3.moveTo(c);return{position:"fixed",top:b.marginBox.top,left:b.marginBox.left,boxSizing:"border-box",width:b.borderBox.width,height:b.borderBox.height,transition:e?b1.drop(e.duration):g?b1.snap:b1.fluid,transform:i,opacity:((a,b)=>{if(a)return b?b$.opacity.drop:b$.opacity.combining})(f,h),zIndex:h?de.dropAnimating:de.dragging,pointerEvents:"none"}}(a):{transform:b3.moveTo(a.offset),transition:a.shouldAnimateDisplacement?void 0:"none"}}(t);return{innerRef:c,draggableProps:{"data-rfd-draggable-context-id":h,"data-rfd-draggable-id":o,style:a,onTransitionEnd:"DRAGGING"===t.type&&t.dropping?w:void 0},dragHandleProps:v}},[h,v,o,t,w,c]),y=aC(()=>({draggableId:m.id,type:m.type,source:{index:m.index,droppableId:m.droppableId}}),[m.droppableId,m.id,m.index,m.type]);return f().createElement(f().Fragment,null,n(x,t.snapshot,y))});function dp(a){return dh(df).isUsingCloneFor!==a.draggableId||a.isClone?f().createElement(dn,a):null}function dq(a){let b="boolean"!=typeof a.isDragDisabled||!a.isDragDisabled,c=!!a.disableInteractiveElementBlocking,d=!!a.shouldRespectForcePress;return f().createElement(dp,aq({},a,{isClone:!1,isEnabled:b,canDragInteractiveElements:c,shouldRespectForcePress:d}))}let dr=a=>b=>a===b,ds=dr("scroll"),dt=dr("auto");dr("visible");let du=(a,b)=>b(a.overflowX)||b(a.overflowY),dv=a=>null==a||a===document.body||a===document.documentElement?null:(a=>{let b=window.getComputedStyle(a),c={overflowX:b.overflowX,overflowY:b.overflowY};return du(c,ds)||du(c,dt)})(a)?a:dv(a.parentElement);var dw=a=>({x:a.scrollLeft,y:a.scrollTop});let dx=a=>!!a&&("fixed"===window.getComputedStyle(a).position||dx(a.parentElement)),dy={passive:!1},dz={passive:!0};var dA=a=>a.shouldPublishImmediately?dy:dz;let dB=a=>a&&a.env.closestScrollable||null;function dC(){}let dD={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}};var dE=f().memo(a=>{let b=(0,e.useRef)(null),c=aD(()=>{b.current&&(clearTimeout(b.current),b.current=null)},[]),{animate:d,onTransitionEnd:g,onClose:h,contextId:i}=a,[j,k]=(0,e.useState)("open"===a.animate);(0,e.useEffect)(()=>j?"open"!==d?(c(),k(!1),dC):b.current?dC:(b.current=setTimeout(()=>{b.current=null,k(!1)}),c):dC,[d,j,c]);let l=aD(a=>{"height"===a.propertyName&&(g(),"close"===d&&h())},[d,h,g]),m=(({isAnimatingOpenOnMount:a,placeholder:b,animate:c})=>{let d=(({isAnimatingOpenOnMount:a,placeholder:b,animate:c})=>a||"close"===c?dD:{height:b.client.borderBox.height,width:b.client.borderBox.width,margin:b.client.margin})({isAnimatingOpenOnMount:a,placeholder:b,animate:c});return{display:b.display,boxSizing:"border-box",width:d.width,height:d.height,marginTop:d.margin.top,marginRight:d.margin.right,marginBottom:d.margin.bottom,marginLeft:d.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==c?b1.placeholder:null}})({isAnimatingOpenOnMount:j,animate:a.animate,placeholder:a.placeholder});return f().createElement(a.placeholder.tagName,{style:m,"data-rfd-placeholder-context-id":i,onTransitionEnd:l,ref:a.innerRef})});function dF(a){return"boolean"==typeof a}function dG(a,b){b.forEach(b=>b(a))}let dH=[function({props:a}){a.droppableId||av(),"string"!=typeof a.droppableId&&av()},function({props:a}){dF(a.isDropDisabled)||av(),dF(a.isCombineEnabled)||av(),dF(a.ignoreContainerClipping)||av()},function({getDroppableRef:a}){dg(a())}],dI=[function({props:a,getPlaceholderRef:b}){if(!a.placeholder||b())return}],dJ=[function({props:a}){a.renderClone||av()},function({getPlaceholderRef:a}){a()&&av()}];class dK extends f().PureComponent{constructor(...a){super(...a),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(a,b){return a.shouldAnimate?a.on?{isVisible:!0,data:a.on,animate:"open"}:b.isVisible?{isVisible:!0,data:b.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!a.on,data:a.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let a={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(a)}}let dL={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||av(),document.body}},dM=a=>{let b,c={...a};for(b in dL)void 0===a[b]&&(c={...c,[b]:dL[b]});return c},dN=(a,b)=>b.draggables[a.draggable.id],dO=ad(()=>{let a={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},b={...a,shouldAnimatePlaceholder:!1},c=aR(a=>({draggableId:a.id,type:a.type,source:{index:a.index,droppableId:a.droppableId}})),d=aR((d,e,f,g,h,i)=>{let j=h.descriptor.id;if(h.descriptor.droppableId===d){let a=i?{render:i,dragging:c(h.descriptor)}:null;return{placeholder:h.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:f,draggingOverWith:f?j:null,draggingFromThisWith:j,isUsingPlaceholder:!0},useClone:a}}return e?g?{placeholder:h.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:f,draggingOverWith:j,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:a:b});return(c,e)=>{let f=dM(e),g=f.droppableId,h=f.type,i=!f.isDropDisabled,j=f.renderClone;if(cZ(c)){let a=c.critical;if(h!==a.droppable.type)return b;let e=dN(a,c.dimensions),f=bs(c.impact)===g;return d(g,i,f,f,e,j)}if("DROP_ANIMATING"===c.phase){let a=c.completed;if(h!==a.critical.droppable.type)return b;let e=dN(a.critical,c.dimensions);return d(g,i,dk(a.result)===g,bs(a.impact)===g,e,j)}if("IDLE"===c.phase&&c.completed&&!c.shouldFlush){let d=c.completed;if(h!==d.critical.droppable.type)return b;let e=bs(d.impact)===g,f=!!(d.impact.at&&"COMBINE"===d.impact.at.type),i=d.critical.droppable.id===g;if(e)return f?a:b;if(i)return a}return b}},{updateViewportMaxScroll:a=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:a})},(a,b,c)=>({...dM(c),...a,...b}),{context:cO,areStatePropsEqual:dj})(a=>{let b=(0,e.useContext)(cT);b||av();let{contextId:c,isMovementAllowed:d}=b,g=(0,e.useRef)(null),i=(0,e.useRef)(null),{children:j,droppableId:k,type:l,mode:m,direction:n,ignoreContainerClipping:o,isDropDisabled:p,isCombineEnabled:q,snapshot:r,useClone:s,updateViewportMaxScroll:t,getContainerForClone:u}=a,v=aD(()=>g.current,[]),w=aD((a=null)=>{g.current=a},[]),x=aD(()=>i.current,[]),y=aD((a=null)=>{i.current=a},[]);!function(a){cX(()=>{dG(a,dH),"standard"===a.props.mode&&dG(a,dI),"virtual"===a.props.mode&&dG(a,dJ)})}({props:a,getDroppableRef:v,getPlaceholderRef:x});let z=aD(()=>{d()&&t({maxScroll:cj()})},[d,t]);!function(a){let b=(0,e.useRef)(null),c=dh(cT),d=cS("droppable"),{registry:f,marshal:g}=c,h=cY(a),i=aC(()=>({id:a.droppableId,type:a.type,mode:a.mode}),[a.droppableId,a.mode,a.type]),j=(0,e.useRef)(i),k=aC(()=>aR((a,c)=>{b.current||av(),g.updateDroppableScroll(i.id,{x:a,y:c})}),[i.id,g]),l=aD(()=>{let a=b.current;return a&&a.env.closestScrollable?dw(a.env.closestScrollable):aE},[]),m=aD(()=>{let a=l();k(a.x,a.y)},[l,k]),n=aC(()=>ap(m),[m]),o=aD(()=>{let a=b.current,c=dB(a);if(a&&c||av(),a.scrollOptions.shouldPublishImmediately)return void m();n()},[n,m]),p=aD((a,d)=>{var e;b.current&&av();let f=h.current,g=f.getDroppableRef();g||av();let j={closestScrollable:dv(e=g),isFixedOnPage:dx(e)},k={ref:g,descriptor:i,env:j,scrollOptions:d};b.current=k;let l=(({ref:a,descriptor:b,env:c,windowScroll:d,direction:e,isDropDisabled:f,isCombineEnabled:g,shouldClipSubject:h})=>{let i=c.closestScrollable,j=((a,b)=>{let c=ao(a);if(!b||a!==b)return c;let d=c.paddingBox.top-b.scrollTop,e=c.paddingBox.left-b.scrollLeft,f=d+b.scrollHeight;return aj({borderBox:ag({top:d,right:e+b.scrollWidth,bottom:f,left:e},c.border),margin:c.margin,border:c.border,padding:c.padding})})(a,i),k=am(j,d),l=(()=>{if(!i)return null;let a=ao(i),b={scrollHeight:i.scrollHeight,scrollWidth:i.scrollWidth};return{client:a,page:am(a,d),scroll:dw(i),scrollSize:b,shouldClipSubject:h}})();return(({descriptor:a,isEnabled:b,isCombineEnabled:c,isFixedOnPage:d,direction:e,client:f,page:g,closest:h})=>{let i=(()=>{if(!h)return null;let{scrollSize:a,client:b}=h,c=ch({scrollHeight:a.scrollHeight,scrollWidth:a.scrollWidth,height:b.paddingBox.height,width:b.paddingBox.width});return{pageMarginBox:h.page.marginBox,frameClient:b,scrollSize:a,shouldClipSubject:h.shouldClipSubject,scroll:{initial:h.scroll,current:h.scroll,max:c,diff:{value:aE,displacement:aE}}}})(),j="vertical"===e?a5:a6,k=aP({page:g,withPlaceholder:null,axis:j,frame:i});return{descriptor:a,isCombineEnabled:c,isFixedOnPage:d,axis:j,isEnabled:b,client:f,page:g,frame:i,subject:k}})({descriptor:b,isEnabled:!f,isCombineEnabled:g,isFixedOnPage:c.isFixedOnPage,direction:e,client:j,page:k,closest:l})})({ref:g,descriptor:i,env:j,windowScroll:a,direction:f.direction,isDropDisabled:f.isDropDisabled,isCombineEnabled:f.isCombineEnabled,shouldClipSubject:!f.ignoreContainerClipping}),m=j.closestScrollable;return m&&(m.setAttribute(cE.contextId,c.contextId),m.addEventListener("scroll",o,dA(k.scrollOptions))),l},[c.contextId,i,o,h]),q=aD(()=>{let a=b.current,c=dB(a);return a&&c||av(),dw(c)},[]),r=aD(()=>{let a=b.current;a||av();let c=dB(a);b.current=null,c&&(n.cancel(),c.removeAttribute(cE.contextId),c.removeEventListener("scroll",o,dA(a.scrollOptions)))},[o,n]),s=aD(a=>{let c=b.current;c||av();let d=dB(c);d||av(),d.scrollTop+=a.y,d.scrollLeft+=a.x},[]),t=aC(()=>({getDimensionAndWatchScroll:p,getScrollWhileDragging:q,dragStopped:r,scroll:s}),[r,p,q,s]),u=aC(()=>({uniqueId:d,descriptor:i,callbacks:t}),[t,i,d]);cG(()=>(j.current=u.descriptor,f.droppable.register(u),()=>{b.current&&r(),f.droppable.unregister(u)}),[t,i,r,u,g,f.droppable]),cG(()=>{b.current&&g.updateDroppableIsEnabled(j.current.id,!a.isDropDisabled)},[a.isDropDisabled,g]),cG(()=>{b.current&&g.updateDroppableIsCombineEnabled(j.current.id,a.isCombineEnabled)},[a.isCombineEnabled,g])}({droppableId:k,type:l,mode:m,direction:n,isDropDisabled:p,isCombineEnabled:q,ignoreContainerClipping:o,getDroppableRef:v});let A=aC(()=>f().createElement(dK,{on:a.placeholder,shouldAnimate:a.shouldAnimatePlaceholder},({onClose:a,data:b,animate:d})=>f().createElement(dE,{placeholder:b,onClose:a,innerRef:y,animate:d,contextId:c,onTransitionEnd:z})),[c,z,a.placeholder,a.shouldAnimatePlaceholder,y]),B=aC(()=>({innerRef:w,placeholder:A,droppableProps:{"data-rfd-droppable-id":k,"data-rfd-droppable-context-id":c}}),[c,k,A,w]),C=s?s.dragging.draggableId:null,D=aC(()=>({droppableId:k,type:l,isUsingCloneFor:C}),[k,C,l]);return f().createElement(df.Provider,{value:D},j(B,r),function(){if(!s)return null;let{dragging:a,render:b}=s,c=f().createElement(dp,{draggableId:a.draggableId,index:a.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(c,d)=>b(c,d,a));return h().createPortal(c,u())}())});var dP=c(58869);let dQ=(0,c(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var dR=c(40228),dS=c(48730),dT=c(25541),dU=c(53411),dV=c(13861),dW=c(88233);let dX=[{id:"func_nome",name:"Nome do Funcion\xe1rio",type:"text",category:"funcionario",icon:(0,d.jsx)(dP.A,{className:"w-4 h-4"})},{id:"func_matricula",name:"Matr\xedcula",type:"text",category:"funcionario",icon:(0,d.jsx)(dQ,{className:"w-4 h-4"})},{id:"func_departamento",name:"Departamento",type:"text",category:"funcionario",icon:(0,d.jsx)(dQ,{className:"w-4 h-4"})},{id:"func_cargo",name:"Cargo",type:"text",category:"funcionario",icon:(0,d.jsx)(dQ,{className:"w-4 h-4"})},{id:"ponto_data",name:"Data",type:"date",category:"ponto",icon:(0,d.jsx)(dR.A,{className:"w-4 h-4"})},{id:"ponto_entrada",name:"Hor\xe1rio de Entrada",type:"text",category:"ponto",icon:(0,d.jsx)(dS.A,{className:"w-4 h-4"})},{id:"ponto_saida",name:"Hor\xe1rio de Sa\xedda",type:"text",category:"ponto",icon:(0,d.jsx)(dS.A,{className:"w-4 h-4"})},{id:"ponto_horas_normais",name:"Horas Normais",type:"number",category:"ponto",icon:(0,d.jsx)(dS.A,{className:"w-4 h-4"})},{id:"ponto_horas_extras",name:"Horas Extras",type:"number",category:"ponto",icon:(0,d.jsx)(dT.A,{className:"w-4 h-4"})},{id:"ponto_atrasos",name:"Atrasos (min)",type:"number",category:"ponto",icon:(0,d.jsx)(dS.A,{className:"w-4 h-4"})},{id:"periodo_inicio",name:"In\xedcio do Per\xedodo",type:"date",category:"periodo",icon:(0,d.jsx)(dR.A,{className:"w-4 h-4"})},{id:"periodo_fim",name:"Fim do Per\xedodo",type:"date",category:"periodo",icon:(0,d.jsx)(dR.A,{className:"w-4 h-4"})},{id:"calc_total_horas",name:"Total de Horas",type:"number",category:"calculo",icon:(0,d.jsx)(dU.A,{className:"w-4 h-4"})},{id:"calc_percentual_presenca",name:"Percentual de Presen\xe7a",type:"number",category:"calculo",icon:(0,d.jsx)(dU.A,{className:"w-4 h-4"})},{id:"calc_media_horas_dia",name:"M\xe9dia Horas/Dia",type:"number",category:"calculo",icon:(0,d.jsx)(dU.A,{className:"w-4 h-4"})}],dY={funcionario:"bg-blue-100 text-blue-800 border-blue-200",ponto:"bg-green-100 text-green-800 border-green-200",periodo:"bg-yellow-100 text-yellow-800 border-yellow-200",calculo:"bg-purple-100 text-purple-800 border-purple-200"},dZ={funcionario:"Funcion\xe1rio",ponto:"Registros de Ponto",periodo:"Per\xedodo",calculo:"C\xe1lculos"};function d$(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!1),g=(0,e.useCallback)(c=>{if(!c.destination)return;let{source:d,destination:e}=c;if("available"===d.droppableId&&"selected"===e.droppableId){let c=dX[d.index],f={id:`${c.id}_${Date.now()}`,fieldId:c.id,type:"table",config:{}},g=[...a];g.splice(e.index,0,f),b(g)}if("selected"===d.droppableId&&"selected"===e.droppableId){let c=[...a],[f]=c.splice(d.index,1);c.splice(e.index,0,f),b(c)}},[a]),h=(0,e.useCallback)(a=>{b(b=>b.filter(b=>b.id!==a))},[]),i=(0,e.useCallback)((a,c)=>{b(b=>b.map(b=>b.id===a?{...b,type:c}:b))},[]);return(0,d.jsx)(dd,{onDragEnd:g,children:(0,d.jsxs)("div",{className:"flex h-96 border border-gray-200 rounded-lg overflow-hidden",children:[(0,d.jsxs)("div",{className:"w-80 bg-gray-50 border-r border-gray-200 p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Biblioteca de Campos"}),Object.entries(dZ).map(([a,b])=>(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:b}),(0,d.jsx)(dO,{droppableId:"available",isDropDisabled:!0,children:b=>(0,d.jsxs)("div",{ref:b.innerRef,...b.droppableProps,className:"space-y-2",children:[dX.filter(b=>b.category===a).map((a,b)=>(0,d.jsx)(dq,{draggableId:a.id,index:b,children:(b,c)=>(0,d.jsxs)("div",{ref:b.innerRef,...b.draggableProps,...b.dragHandleProps,className:`
                            p-3 rounded-lg border cursor-move transition-all
                            ${dY[a.category]}
                            ${c.isDragging?"shadow-lg scale-105":"hover:shadow-md"}
                          `,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[a.icon,(0,d.jsx)("span",{className:"text-sm font-medium",children:a.name})]}),(0,d.jsx)("div",{className:"text-xs mt-1 opacity-75",children:a.type})]})},a.id)),b.placeholder]})})]},a))]}),(0,d.jsxs)("div",{className:"flex-1 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"\xc1rea de Design"}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)("button",{onClick:()=>f(!c),className:`px-3 py-2 rounded-md text-sm font-medium ${c?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:[(0,d.jsx)(dV.A,{className:"w-4 h-4 mr-1 inline"}),c?"Editar":"Preview"]})})]}),(0,d.jsx)(dO,{droppableId:"selected",children:(b,e)=>(0,d.jsxs)("div",{ref:b.innerRef,...b.droppableProps,className:`
              min-h-96 border-2 border-dashed rounded-lg p-4 transition-colors
              ${e.isDraggingOver?"border-blue-400 bg-blue-50":"border-gray-300"}
              ${0===a.length?"flex items-center justify-center":""}
            `,children:[0===a.length?(0,d.jsxs)("div",{className:"text-center text-gray-500",children:[(0,d.jsx)(dQ,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),(0,d.jsx)("p",{className:"text-lg font-medium mb-2",children:"Arraste campos aqui"}),(0,d.jsx)("p",{className:"text-sm",children:"Comece arrastando campos da biblioteca para criar seu relat\xf3rio"})]}):(0,d.jsx)("div",{className:"space-y-4",children:a.map((a,b)=>{let e,f=(e=a.fieldId,dX.find(a=>a.id===e));return f?(0,d.jsx)(dq,{draggableId:a.id,index:b,children:(b,e)=>(0,d.jsxs)("div",{ref:b.innerRef,...b.draggableProps,className:`
                            bg-white border rounded-lg p-4 transition-all
                            ${e.isDragging?"shadow-lg scale-105":"shadow-sm hover:shadow-md"}
                          `,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",...b.dragHandleProps,children:[f.icon,(0,d.jsx)("span",{className:"font-medium",children:f.name}),(0,d.jsx)("span",{className:`px-2 py-1 rounded text-xs ${dY[f.category]}`,children:dZ[f.category]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("select",{value:a.type,onChange:b=>i(a.id,b.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[(0,d.jsx)("option",{value:"table",children:"Tabela"}),(0,d.jsx)("option",{value:"chart",children:"Gr\xe1fico"}),(0,d.jsx)("option",{value:"card",children:"Card"}),(0,d.jsx)("option",{value:"filter",children:"Filtro"})]}),(0,d.jsx)("button",{onClick:()=>h(a.id),className:"text-red-600 hover:text-red-800",children:(0,d.jsx)(dW.A,{className:"w-4 h-4"})})]})]}),c&&(0,d.jsxs)("div",{className:"border-t pt-3 mt-3",children:["table"===a.type&&(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDCCA Tabela: ",f.name]}),"chart"===a.type&&(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDCC8 Gr\xe1fico: ",f.name]}),"card"===a.type&&(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83C\uDCCF Card: ",f.name]}),"filter"===a.type&&(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDD0D Filtro: ",f.name]})]})]})},a.id):null})}),b.placeholder]})})]})]})})}},32769:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},70908:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,88939))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88939:(a,b,c)=>{"use strict";c.d(b,{ReportBuilder:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ReportBuilder() from the server but ReportBuilder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-builder.tsx","ReportBuilder")},92038:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n,metadata:()=>m});var d=c(37413),e=c(51465),f=c(32769);let g=(0,c(26373).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var h=c(59574),i=c(65516),j=c(4536),k=c.n(j),l=c(88939);let m={title:"Construtor de Relat\xf3rios - RLPONTO",description:"Construa relat\xf3rios personalizados com interface visual intuitiva"};function n(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(k(),{href:"/relatorios",children:(0,d.jsxs)("button",{className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:[(0,d.jsx)(e.A,{className:"h-4 w-4 mr-2 inline"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-600 rounded-lg",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Construtor de Relat\xf3rios"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Crie relat\xf3rios personalizados com interface visual"})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)("button",{className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:[(0,d.jsx)(g,{className:"h-4 w-4 mr-2 inline"}),"Salvar"]}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700",children:[(0,d.jsx)(h.A,{className:"h-4 w-4 mr-2 inline"}),"Executar"]}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2 inline"}),"Exportar"]})]})]}),(0,d.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-purple-900 mb-3",children:"Construtor Visual de Relat\xf3rios"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-purple-700",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"\uD83C\uDFA8 Interface Visual"}),(0,d.jsx)("p",{className:"mt-1",children:"Arraste e solte campos para criar relat\xf3rios personalizados"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"\uD83D\uDCCA M\xfaltiplas Visualiza\xe7\xf5es"}),(0,d.jsx)("p",{className:"mt-1",children:"Tabelas, gr\xe1ficos, cards e dashboards interativos"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"⚡ Tempo Real"}),(0,d.jsx)("p",{className:"mt-1",children:"Preview instant\xe2neo das altera\xe7\xf5es conforme voc\xea constr\xf3i"})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(f.A,{className:"w-4 h-4 text-blue-600"})})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Templates"}),(0,d.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:"12"})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-3 h-3 bg-green-600 rounded-full"})})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Campos Dispon\xedveis"}),(0,d.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:"45"})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-3 h-3 bg-yellow-600 rounded-full"})})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Relat\xf3rios Salvos"}),(0,d.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:"8"})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-3 h-3 bg-purple-600 rounded-full"})})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Execu\xe7\xf5es Hoje"}),(0,d.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:"23"})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Construtor Visual"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Arraste campos da biblioteca para a \xe1rea de design e configure as visualiza\xe7\xf5es"})]}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)(l.ReportBuilder,{})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Templates R\xe1pidos"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Relat\xf3rio de Presen\xe7a"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"An\xe1lise de frequ\xeancia e pontualidade por funcion\xe1rio"}),(0,d.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,d.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"Frequ\xeancia"}),(0,d.jsx)("span",{className:"ml-2",children:"5 campos"})]})]}),(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Horas Trabalhadas"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Consolidado de horas normais e extras por per\xedodo"}),(0,d.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,d.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded",children:"Horas"}),(0,d.jsx)("span",{className:"ml-2",children:"7 campos"})]})]}),(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Dashboard Executivo"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"KPIs e m\xe9tricas principais para gestores"}),(0,d.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,d.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded",children:"KPIs"}),(0,d.jsx)("span",{className:"ml-2",children:"12 campos"})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Como Usar o Construtor"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Passos B\xe1sicos"}),(0,d.jsxs)("ol",{className:"space-y-1 list-decimal list-inside",children:[(0,d.jsx)("li",{children:"Selecione um template ou comece do zero"}),(0,d.jsx)("li",{children:"Arraste campos da biblioteca para o design"}),(0,d.jsx)("li",{children:"Configure filtros e agrupamentos"}),(0,d.jsx)("li",{children:"Escolha o tipo de visualiza\xe7\xe3o"}),(0,d.jsx)("li",{children:"Execute o preview e ajuste conforme necess\xe1rio"}),(0,d.jsx)("li",{children:"Salve e exporte o relat\xf3rio"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Recursos Avan\xe7ados"}),(0,d.jsxs)("ul",{className:"space-y-1",children:[(0,d.jsx)("li",{children:"• F\xf3rmulas personalizadas e c\xe1lculos"}),(0,d.jsx)("li",{children:"• Filtros din\xe2micos e condicionais"}),(0,d.jsx)("li",{children:"• Agrupamentos e subtotais autom\xe1ticos"}),(0,d.jsx)("li",{children:"• Gr\xe1ficos interativos e dashboards"}),(0,d.jsx)("li",{children:"• Agendamento de execu\xe7\xe3o autom\xe1tica"}),(0,d.jsx)("li",{children:"• Compartilhamento e colabora\xe7\xe3o"})]})]})]})]})]})})})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,327,40,322],()=>b(b.s=4082));module.exports=c})();