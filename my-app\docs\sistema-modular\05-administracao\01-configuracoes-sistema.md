# ⚙️ MÓDULO CONFIGURAÇÕES DO SISTEMA - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável por todas as configurações do sistema, incluindo parâmetros gerais, biometria, jornadas e relatórios.

## 🎯 Funcionalidades
- Configurações do sistema
- Configurações de biometria
- Configurações de jornada
- Configurações de relatórios
- Configurações de rede
- Configurações de segurança
- Backup e restore
- Logs de auditoria

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── configuracoes/
│           ├── page.tsx                    # Dashboard de configurações
│           ├── sistema/
│           │   └── page.tsx                # Configurações gerais
│           ├── biometria/
│           │   └── page.tsx                # Configurações biométricas
│           ├── jornada/
│           │   └── page.tsx                # Configurações de jornada
│           ├── relatorios/
│           │   └── page.tsx                # Configurações de relatórios
│           └── components/
│               ├── config-form.tsx         # Formulário de configuração
│               ├── config-section.tsx      # Seção de configuração
│               └── backup-manager.tsx      # Gerenciador de backup
├── components/
│   └── configuracoes/
│       ├── sistema-config.tsx             # Config do sistema
│       ├── biometria-config.tsx           # Config biometria
│       ├── jornada-config.tsx             # Config jornada
│       └── relatorios-config.tsx          # Config relatórios
└── api/
    └── configuracoes/
        ├── route.ts                       # API principal
        ├── sistema/
        │   └── route.ts                   # Config sistema
        ├── biometria/
        │   └── route.ts                   # Config biometria
        ├── jornada/
        │   └── route.ts                   # Config jornada
        └── backup/
            └── route.ts                   # Backup/restore
```

## 🔧 Implementação Técnica

### ⚙️ Dashboard de Configurações (page.tsx)
```typescript
// app/(dashboard)/configuracoes/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Settings, Server, Fingerprint, Clock, FileText, Database, Shield } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Configurações - RLPONTO',
  description: 'Configurações do sistema RLPONTO',
};

export default function ConfiguracoesPage() {
  const configSections = [
    {
      title: 'Sistema',
      description: 'Configurações gerais do sistema',
      icon: Server,
      href: '/configuracoes/sistema',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Biometria',
      description: 'Configurações de dispositivos biométricos',
      icon: Fingerprint,
      href: '/configuracoes/biometria',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Jornada',
      description: 'Configurações de jornada de trabalho',
      icon: Clock,
      href: '/configuracoes/jornada',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Relatórios',
      description: 'Configurações de relatórios e exportação',
      icon: FileText,
      href: '/configuracoes/relatorios',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Backup',
      description: 'Configurações de backup e restore',
      icon: Database,
      href: '/configuracoes/backup',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
    {
      title: 'Segurança',
      description: 'Configurações de segurança e auditoria',
      icon: Shield,
      href: '/configuracoes/seguranca',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <Settings className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Configurações</h1>
          <p className="text-gray-600">Gerencie as configurações do sistema RLPONTO</p>
        </div>
      </div>

      {/* Grid de Configurações */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {configSections.map((section) => {
          const Icon = section.icon;
          return (
            <Card key={section.title} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className={`w-12 h-12 rounded-lg ${section.bgColor} flex items-center justify-center mb-4`}>
                  <Icon className={`h-6 w-6 ${section.color}`} />
                </div>
                <CardTitle className="text-lg">{section.title}</CardTitle>
                <CardDescription>{section.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link href={section.href}>
                    Configurar
                  </Link>
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Status do Sistema */}
      <Card>
        <CardHeader>
          <CardTitle>Status do Sistema</CardTitle>
          <CardDescription>Informações sobre o estado atual do sistema</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SystemStatusSkeleton />}>
            <SystemStatus />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function SystemStatusSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}

function SystemStatus() {
  // Implementar status do sistema
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        <div>
          <p className="text-sm font-medium text-gray-900">Banco de Dados</p>
          <p className="text-xs text-green-600">Online</p>
        </div>
      </div>
      <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        <div>
          <p className="text-sm font-medium text-gray-900">API</p>
          <p className="text-xs text-green-600">Funcionando</p>
        </div>
      </div>
      <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
        <div>
          <p className="text-sm font-medium text-gray-900">Biometria</p>
          <p className="text-xs text-yellow-600">2 de 3 dispositivos</p>
        </div>
      </div>
      <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        <div>
          <p className="text-sm font-medium text-gray-900">Backup</p>
          <p className="text-xs text-green-600">Último: hoje 02:00</p>
        </div>
      </div>
    </div>
  );
}
```

### 🖥️ Configurações do Sistema (sistema/page.tsx)
```typescript
// app/(dashboard)/configuracoes/sistema/page.tsx
import { Metadata } from 'next/metadata';
import { SistemaConfig } from '@/components/configuracoes/sistema-config';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Server } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Configurações do Sistema - RLPONTO',
  description: 'Configurações gerais do sistema',
};

export default function SistemaConfigPage() {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/configuracoes">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Link>
          </Button>
          <div className="flex items-center space-x-2">
            <Server className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Configurações do Sistema</h1>
              <p className="text-gray-600">Configure parâmetros gerais do sistema</p>
            </div>
          </div>
        </div>
      </div>

      {/* Formulário de Configurações */}
      <Card>
        <CardHeader>
          <CardTitle>Parâmetros do Sistema</CardTitle>
        </CardHeader>
        <CardContent>
          <SistemaConfig />
        </CardContent>
      </Card>
    </div>
  );
}
```

### 🔧 Componente de Configuração do Sistema (sistema-config.tsx)
```typescript
// components/configuracoes/sistema-config.tsx
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save } from 'lucide-react';

const sistemaConfigSchema = z.object({
  nomeEmpresa: z.string().min(1, 'Nome da empresa é obrigatório'),
  timeoutSessao: z.number().min(300).max(86400), // 5 min a 24h
  backupAutomatico: z.boolean(),
  logsDetalhados: z.boolean(),
  formatoData: z.enum(['dd/mm/yyyy', 'mm/dd/yyyy', 'yyyy-mm-dd']),
  fusoHorario: z.string(),
  idioma: z.enum(['pt-BR', 'en-US', 'es-ES']),
  registrosPorPagina: z.number().min(10).max(100),
  manterLogsAuditoria: z.number().min(30).max(365), // dias
});

type SistemaConfigData = z.infer<typeof sistemaConfigSchema>;

export function SistemaConfig() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty },
  } = useForm<SistemaConfigData>({
    resolver: zodResolver(sistemaConfigSchema),
    defaultValues: {
      nomeEmpresa: '',
      timeoutSessao: 3600,
      backupAutomatico: true,
      logsDetalhados: true,
      formatoData: 'dd/mm/yyyy',
      fusoHorario: 'America/Sao_Paulo',
      idioma: 'pt-BR',
      registrosPorPagina: 50,
      manterLogsAuditoria: 90,
    },
  });

  const backupAutomatico = watch('backupAutomatico');
  const logsDetalhados = watch('logsDetalhados');

  useEffect(() => {
    loadConfiguracoes();
  }, []);

  const loadConfiguracoes = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/configuracoes/sistema');
      if (response.ok) {
        const config = await response.json();
        Object.keys(config).forEach((key) => {
          setValue(key as keyof SistemaConfigData, config[key]);
        });
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
      setMessage({ type: 'error', text: 'Erro ao carregar configurações' });
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: SistemaConfigData) => {
    setIsSaving(true);
    setMessage(null);

    try {
      const response = await fetch('/api/configuracoes/sistema', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar configurações');
      }

      setMessage({ type: 'success', text: 'Configurações salvas com sucesso!' });
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      setMessage({ type: 'error', text: 'Erro ao salvar configurações' });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Carregando configurações...</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Message Alert */}
      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Configurações Gerais */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Configurações Gerais</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="nomeEmpresa">Nome da Empresa</Label>
            <Input
              id="nomeEmpresa"
              {...register('nomeEmpresa')}
              placeholder="Nome da sua empresa"
            />
            {errors.nomeEmpresa && (
              <p className="text-sm text-red-600 mt-1">{errors.nomeEmpresa.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="timeoutSessao">Timeout de Sessão (segundos)</Label>
            <Input
              id="timeoutSessao"
              type="number"
              {...register('timeoutSessao', { valueAsNumber: true })}
              min={300}
              max={86400}
            />
            {errors.timeoutSessao && (
              <p className="text-sm text-red-600 mt-1">{errors.timeoutSessao.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="formatoData">Formato de Data</Label>
            <Select onValueChange={(value) => setValue('formatoData', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o formato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dd/mm/yyyy">DD/MM/AAAA</SelectItem>
                <SelectItem value="mm/dd/yyyy">MM/DD/AAAA</SelectItem>
                <SelectItem value="yyyy-mm-dd">AAAA-MM-DD</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="idioma">Idioma</Label>
            <Select onValueChange={(value) => setValue('idioma', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o idioma" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                <SelectItem value="en-US">English (US)</SelectItem>
                <SelectItem value="es-ES">Español</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="registrosPorPagina">Registros por Página</Label>
            <Input
              id="registrosPorPagina"
              type="number"
              {...register('registrosPorPagina', { valueAsNumber: true })}
              min={10}
              max={100}
            />
          </div>

          <div>
            <Label htmlFor="manterLogsAuditoria">Manter Logs de Auditoria (dias)</Label>
            <Input
              id="manterLogsAuditoria"
              type="number"
              {...register('manterLogsAuditoria', { valueAsNumber: true })}
              min={30}
              max={365}
            />
          </div>
        </div>
      </div>

      {/* Configurações de Sistema */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Sistema</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="backupAutomatico">Backup Automático</Label>
              <p className="text-sm text-gray-600">Realizar backup automático diário</p>
            </div>
            <Switch
              id="backupAutomatico"
              checked={backupAutomatico}
              onCheckedChange={(checked) => setValue('backupAutomatico', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="logsDetalhados">Logs Detalhados</Label>
              <p className="text-sm text-gray-600">Registrar logs detalhados de auditoria</p>
            </div>
            <Switch
              id="logsDetalhados"
              checked={logsDetalhados}
              onCheckedChange={(checked) => setValue('logsDetalhados', checked)}
            />
          </div>
        </div>
      </div>

      {/* Botão de Salvar */}
      <div className="flex justify-end">
        <Button type="submit" disabled={!isDirty || isSaving}>
          {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Salvar Configurações
        </Button>
      </div>
    </form>
  );
}
```

## 🔌 API Routes

### ⚙️ API de Configurações do Sistema (sistema/route.ts)
```typescript
// app/api/configuracoes/sistema/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { sistemaConfigSchema } from '@/lib/validations/configuracoes';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const configuracoes = await prisma.configuracao.findMany({
      where: {
        categoria: 'sistema',
      },
    });

    // Converter array de configurações em objeto
    const config = configuracoes.reduce((acc, item) => {
      acc[item.chave] = item.valor;
      return acc;
    }, {} as Record<string, any>);

    return NextResponse.json(config);
  } catch (error) {
    console.error('Erro ao buscar configurações:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = sistemaConfigSchema.parse(body);

    // Atualizar configurações no banco
    const updates = Object.entries(validatedData).map(([chave, valor]) =>
      prisma.configuracao.upsert({
        where: {
          categoria_chave: {
            categoria: 'sistema',
            chave,
          },
        },
        update: {
          valor: JSON.stringify(valor),
          atualizadoEm: new Date(),
          atualizadoPor: parseInt(session.user.id),
        },
        create: {
          categoria: 'sistema',
          chave,
          valor: JSON.stringify(valor),
          criadoPor: parseInt(session.user.id),
        },
      })
    );

    await Promise.all(updates);

    // Log da alteração
    await prisma.logAuditoria.create({
      data: {
        acao: 'configuracao_sistema_atualizada',
        usuarioId: parseInt(session.user.id),
        detalhes: JSON.stringify(validatedData),
        timestamp: new Date(),
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Erro ao salvar configurações:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Schema do Banco de Dados

### 📋 Modelo Prisma
```prisma
model Configuracao {
  id          Int      @id @default(autoincrement())
  categoria   String   // 'sistema', 'biometria', 'jornada', 'relatorios'
  chave       String   // nome da configuração
  valor       String   // valor em JSON
  descricao   String?  // descrição da configuração
  criadoEm    DateTime @default(now()) @map("criado_em")
  atualizadoEm DateTime @updatedAt @map("atualizado_em")
  criadoPor   Int      @map("criado_por")
  atualizadoPor Int?   @map("atualizado_por")

  // Relacionamentos
  criador     Usuario  @relation("ConfiguracaoCriador", fields: [criadoPor], references: [id])
  atualizador Usuario? @relation("ConfiguracaoAtualizador", fields: [atualizadoPor], references: [id])

  @@unique([categoria, chave], name: "categoria_chave")
  @@map("configuracoes")
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Dashboard de configurações
- [ ] Configurações do sistema
- [ ] Configurações de biometria
- [ ] Configurações de jornada
- [ ] Configurações de relatórios
- [ ] Backup e restore
- [ ] Logs de auditoria
- [ ] Status do sistema

### 🔧 Validações
- [ ] Apenas admins podem alterar
- [ ] Validação de valores
- [ ] Backup antes de alterações
- [ ] Log de todas as mudanças
- [ ] Rollback em caso de erro

## 🚀 Próximos Passos
1. **Gerenciar Usuários** - Módulo de gestão de usuários
2. **Configurações de Biometria** - Parâmetros biométricos
3. **Backup Automático** - Sistema de backup
