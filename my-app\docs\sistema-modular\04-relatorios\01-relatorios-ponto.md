# 📊 MÓDULO RELATÓRIOS DE PONTO - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável pela geração de relatórios detalhados de ponto, incluindo relatórios individuais, por período, analíticos e exportação em múltiplos formatos.

## 🎯 Funcionalidades
- Relatórios individuais por funcionário
- Relatórios por período e departamento
- Relatórios de frequência e pontualidade
- Relatórios de horas extras e faltas
- Relatórios analíticos e comparativos
- Exportação em PDF, Excel e CSV
- Agendamento de relatórios
- Relatórios personalizados
- Gráficos e visualizações

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── relatorios/
│           ├── page.tsx                    # Dashboard de relatórios
│           ├── funcionario/
│           │   └── page.tsx                # Relatórios por funcionário
│           ├── periodo/
│           │   └── page.tsx                # Relatórios por período
│           ├── frequencia/
│           │   └── page.tsx                # Relatórios de frequência
│           ├── analiticos/
│           │   └── page.tsx                # Relatórios analíticos
│           └── components/
│               ├── report-builder.tsx      # Construtor de relatórios
│               ├── report-filters.tsx      # Filtros de relatórios
│               ├── report-preview.tsx      # Preview do relatório
│               └── export-options.tsx      # Opções de exportação
├── components/
│   └── relatorios/
│       ├── report-card.tsx                # Card de relatório
│       ├── chart-components.tsx           # Componentes de gráficos
│       ├── data-table.tsx                 # Tabela de dados
│       └── schedule-modal.tsx             # Modal de agendamento
└── api/
    └── relatorios/
        ├── route.ts                       # API principal
        ├── funcionario/
        │   └── route.ts                   # Relatórios por funcionário
        ├── periodo/
        │   └── route.ts                   # Relatórios por período
        ├── export/
        │   └── route.ts                   # Exportação
        └── schedule/
            └── route.ts                   # Agendamento
```

## 🔧 Implementação Técnica

### 📊 Dashboard de Relatórios (page.tsx)
```typescript
// app/(dashboard)/relatorios/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Users, Calendar, TrendingUp, Download, Clock, BarChart3, PieChart } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Relatórios - RLPONTO',
  description: 'Relatórios detalhados do sistema de ponto',
};

interface RelatorioTemplate {
  id: string;
  title: string;
  description: string;
  icon: any;
  category: string;
  href: string;
  color: string;
  bgColor: string;
  popular?: boolean;
}

export default function RelatoriosPage() {
  const relatorioTemplates: RelatorioTemplate[] = [
    {
      id: 'funcionario',
      title: 'Relatório Individual',
      description: 'Relatório detalhado por funcionário',
      icon: Users,
      category: 'Individual',
      href: '/relatorios/funcionario',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      popular: true,
    },
    {
      id: 'periodo',
      title: 'Relatório por Período',
      description: 'Relatórios consolidados por período',
      icon: Calendar,
      category: 'Período',
      href: '/relatorios/periodo',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      popular: true,
    },
    {
      id: 'frequencia',
      title: 'Frequência e Pontualidade',
      description: 'Análise de frequência e pontualidade',
      icon: Clock,
      category: 'Frequência',
      href: '/relatorios/frequencia',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      id: 'horas-extras',
      title: 'Horas Extras',
      description: 'Relatório de horas extras trabalhadas',
      icon: TrendingUp,
      category: 'Horas',
      href: '/relatorios/horas-extras',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      id: 'analitico',
      title: 'Relatórios Analíticos',
      description: 'Análises avançadas e comparativos',
      icon: BarChart3,
      category: 'Analítico',
      href: '/relatorios/analiticos',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
    {
      id: 'dashboard',
      title: 'Dashboard Executivo',
      description: 'Visão executiva com KPIs',
      icon: PieChart,
      category: 'Executivo',
      href: '/relatorios/dashboard',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  const relatoriosRecentes = [
    {
      id: 1,
      nome: 'Relatório Mensal - Janeiro 2024',
      tipo: 'Período',
      geradoEm: '2024-02-01T10:30:00Z',
      status: 'Concluído',
      formato: 'PDF',
    },
    {
      id: 2,
      nome: 'Frequência - Departamento TI',
      tipo: 'Frequência',
      geradoEm: '2024-01-28T14:15:00Z',
      status: 'Concluído',
      formato: 'Excel',
    },
    {
      id: 3,
      nome: 'Horas Extras - Q4 2023',
      tipo: 'Horas',
      geradoEm: '2024-01-25T09:45:00Z',
      status: 'Processando',
      formato: 'PDF',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FileText className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Relatórios</h1>
            <p className="text-gray-600">Gere relatórios detalhados do sistema de ponto</p>
          </div>
        </div>
        <Button asChild>
          <Link href="/relatorios/personalizado">
            <FileText className="h-4 w-4 mr-2" />
            Relatório Personalizado
          </Link>
        </Button>
      </div>

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Relatórios Gerados</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">este mês</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Downloads</CardTitle>
            <Download className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5,678</div>
            <p className="text-xs text-muted-foreground">arquivos baixados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Agendados</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23</div>
            <p className="text-xs text-muted-foreground">relatórios automáticos</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processando</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">em andamento</p>
          </CardContent>
        </Card>
      </div>

      {/* Templates de Relatórios */}
      <Card>
        <CardHeader>
          <CardTitle>Templates de Relatórios</CardTitle>
          <CardDescription>
            Escolha um template para gerar seu relatório
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {relatorioTemplates.map((template) => {
              const Icon = template.icon;
              return (
                <Card key={template.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className={`w-12 h-12 rounded-lg ${template.bgColor} flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 ${template.color}`} />
                      </div>
                      {template.popular && (
                        <Badge variant="secondary">Popular</Badge>
                      )}
                    </div>
                    <CardTitle className="text-lg">{template.title}</CardTitle>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{template.category}</Badge>
                      <Button asChild size="sm">
                        <Link href={template.href}>
                          Gerar
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Relatórios Recentes */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Relatórios Recentes</CardTitle>
              <CardDescription>
                Seus relatórios gerados recentemente
              </CardDescription>
            </div>
            <Button variant="outline" asChild>
              <Link href="/relatorios/historico">
                Ver Todos
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {relatoriosRecentes.map((relatorio) => (
              <div
                key={relatorio.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{relatorio.nome}</h4>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>{relatorio.tipo}</span>
                      <span>•</span>
                      <span>{new Date(relatorio.geradoEm).toLocaleDateString('pt-BR')}</span>
                      <span>•</span>
                      <span>{relatorio.formato}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Badge 
                    variant={relatorio.status === 'Concluído' ? 'default' : 'secondary'}
                    className={relatorio.status === 'Concluído' ? 'bg-green-100 text-green-800' : ''}
                  >
                    {relatorio.status}
                  </Badge>
                  {relatorio.status === 'Concluído' && (
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Ações Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Relatório Rápido</CardTitle>
            <CardDescription>Gere um relatório básico rapidamente</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" asChild>
              <Link href="/relatorios/rapido">
                <FileText className="h-4 w-4 mr-2" />
                Gerar Agora
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Agendar Relatório</CardTitle>
            <CardDescription>Configure relatórios automáticos</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/relatorios/agendar">
                <Clock className="h-4 w-4 mr-2" />
                Agendar
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Histórico</CardTitle>
            <CardDescription>Visualize todos os relatórios gerados</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/relatorios/historico">
                <BarChart3 className="h-4 w-4 mr-2" />
                Ver Histórico
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

### 🏗️ Construtor de Relatórios (report-builder.tsx)
```typescript
// app/(dashboard)/relatorios/components/report-builder.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Download, Eye, Settings, Filter, Columns } from 'lucide-react';

const reportSchema = z.object({
  nome: z.string().min(1, 'Nome é obrigatório'),
  tipo: z.enum(['funcionario', 'periodo', 'frequencia', 'horas-extras', 'analitico']),
  formato: z.enum(['pdf', 'excel', 'csv']),
  periodo: z.object({
    inicio: z.date(),
    fim: z.date(),
  }),
  filtros: z.object({
    funcionarios: z.array(z.number()).optional(),
    departamentos: z.array(z.string()).optional(),
    status: z.array(z.string()).optional(),
  }),
  colunas: z.array(z.string()),
  agrupamento: z.string().optional(),
  ordenacao: z.string().optional(),
  observacoes: z.string().optional(),
});

type ReportFormData = z.infer<typeof reportSchema>;

interface ReportBuilderProps {
  onGenerate: (data: ReportFormData) => void;
  isGenerating?: boolean;
}

export function ReportBuilder({ onGenerate, isGenerating = false }: ReportBuilderProps) {
  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'funcionario',
    'data',
    'entrada',
    'saida',
    'horas_trabalhadas',
  ]);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ReportFormData>({
    resolver: zodResolver(reportSchema),
    defaultValues: {
      nome: '',
      tipo: 'funcionario',
      formato: 'pdf',
      colunas: selectedColumns,
    },
  });

  const tipoRelatorio = watch('tipo');
  const formato = watch('formato');

  const tiposRelatorio = [
    { value: 'funcionario', label: 'Por Funcionário', description: 'Relatório individual detalhado' },
    { value: 'periodo', label: 'Por Período', description: 'Consolidado por período' },
    { value: 'frequencia', label: 'Frequência', description: 'Análise de frequência e pontualidade' },
    { value: 'horas-extras', label: 'Horas Extras', description: 'Relatório de horas extras' },
    { value: 'analitico', label: 'Analítico', description: 'Análises avançadas e comparativos' },
  ];

  const colunasDisponiveis = [
    { id: 'funcionario', label: 'Funcionário', categoria: 'Básico' },
    { id: 'matricula', label: 'Matrícula', categoria: 'Básico' },
    { id: 'departamento', label: 'Departamento', categoria: 'Básico' },
    { id: 'data', label: 'Data', categoria: 'Básico' },
    { id: 'entrada', label: 'Entrada', categoria: 'Horários' },
    { id: 'saida', label: 'Saída', categoria: 'Horários' },
    { id: 'intervalo_inicio', label: 'Início Intervalo', categoria: 'Horários' },
    { id: 'intervalo_fim', label: 'Fim Intervalo', categoria: 'Horários' },
    { id: 'horas_trabalhadas', label: 'Horas Trabalhadas', categoria: 'Cálculos' },
    { id: 'horas_extras', label: 'Horas Extras', categoria: 'Cálculos' },
    { id: 'atrasos', label: 'Atrasos', categoria: 'Cálculos' },
    { id: 'faltas', label: 'Faltas', categoria: 'Cálculos' },
    { id: 'status', label: 'Status', categoria: 'Status' },
    { id: 'observacoes', label: 'Observações', categoria: 'Status' },
  ];

  const handleColumnToggle = (columnId: string, checked: boolean) => {
    let newColumns;
    if (checked) {
      newColumns = [...selectedColumns, columnId];
    } else {
      newColumns = selectedColumns.filter(id => id !== columnId);
    }
    setSelectedColumns(newColumns);
    setValue('colunas', newColumns);
  };

  const onSubmit = (data: ReportFormData) => {
    onGenerate({
      ...data,
      colunas: selectedColumns,
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Configurações do Relatório</span>
          </CardTitle>
          <CardDescription>
            Configure os parâmetros do seu relatório personalizado
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basico" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basico">Básico</TabsTrigger>
              <TabsTrigger value="filtros">Filtros</TabsTrigger>
              <TabsTrigger value="colunas">Colunas</TabsTrigger>
              <TabsTrigger value="formato">Formato</TabsTrigger>
            </TabsList>

            {/* Configurações Básicas */}
            <TabsContent value="basico" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="nome">Nome do Relatório</Label>
                  <Input
                    id="nome"
                    {...register('nome')}
                    placeholder="Ex: Relatório Mensal Janeiro 2024"
                  />
                  {errors.nome && (
                    <p className="text-sm text-red-600 mt-1">{errors.nome.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="tipo">Tipo de Relatório</Label>
                  <Select onValueChange={(value) => setValue('tipo', value as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      {tiposRelatorio.map((tipo) => (
                        <SelectItem key={tipo.value} value={tipo.value}>
                          <div>
                            <div className="font-medium">{tipo.label}</div>
                            <div className="text-sm text-gray-500">{tipo.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="md:col-span-2">
                  <Label>Período</Label>
                  <DatePickerWithRange
                    onSelect={(range) => {
                      if (range?.from && range?.to) {
                        setValue('periodo', {
                          inicio: range.from,
                          fim: range.to,
                        });
                      }
                    }}
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="observacoes">Observações</Label>
                  <Textarea
                    id="observacoes"
                    {...register('observacoes')}
                    placeholder="Observações adicionais sobre o relatório..."
                    rows={3}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Filtros */}
            <TabsContent value="filtros" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Funcionários</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Todos os funcionários" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todos">Todos os funcionários</SelectItem>
                      <SelectItem value="ativos">Apenas ativos</SelectItem>
                      <SelectItem value="inativos">Apenas inativos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Departamentos</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Todos os departamentos" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todos">Todos os departamentos</SelectItem>
                      <SelectItem value="ti">Tecnologia da Informação</SelectItem>
                      <SelectItem value="rh">Recursos Humanos</SelectItem>
                      <SelectItem value="financeiro">Financeiro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Status dos Registros</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Todos os status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todos">Todos os status</SelectItem>
                      <SelectItem value="aprovados">Apenas aprovados</SelectItem>
                      <SelectItem value="pendentes">Pendentes de aprovação</SelectItem>
                      <SelectItem value="rejeitados">Rejeitados</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Tipo de Registro</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Todos os tipos" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todos">Todos os tipos</SelectItem>
                      <SelectItem value="biometrico">Biométrico</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Colunas */}
            <TabsContent value="colunas" className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <Label className="text-base font-medium">Colunas do Relatório</Label>
                  <Badge variant="secondary">
                    {selectedColumns.length} selecionadas
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(
                    colunasDisponiveis.reduce((acc, coluna) => {
                      if (!acc[coluna.categoria]) acc[coluna.categoria] = [];
                      acc[coluna.categoria].push(coluna);
                      return acc;
                    }, {} as Record<string, typeof colunasDisponiveis>)
                  ).map(([categoria, colunas]) => (
                    <Card key={categoria}>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">{categoria}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {colunas.map((coluna) => (
                          <div key={coluna.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={coluna.id}
                              checked={selectedColumns.includes(coluna.id)}
                              onCheckedChange={(checked) => 
                                handleColumnToggle(coluna.id, checked as boolean)
                              }
                            />
                            <Label htmlFor={coluna.id} className="text-sm">
                              {coluna.label}
                            </Label>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Formato */}
            <TabsContent value="formato" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className={`cursor-pointer border-2 ${formato === 'pdf' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                  <CardContent className="p-4 text-center">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-red-600" />
                    <h3 className="font-medium">PDF</h3>
                    <p className="text-sm text-gray-600">Formato ideal para impressão</p>
                    <Button
                      type="button"
                      variant={formato === 'pdf' ? 'default' : 'outline'}
                      size="sm"
                      className="mt-2"
                      onClick={() => setValue('formato', 'pdf')}
                    >
                      Selecionar
                    </Button>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer border-2 ${formato === 'excel' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                  <CardContent className="p-4 text-center">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-green-600" />
                    <h3 className="font-medium">Excel</h3>
                    <p className="text-sm text-gray-600">Para análises e cálculos</p>
                    <Button
                      type="button"
                      variant={formato === 'excel' ? 'default' : 'outline'}
                      size="sm"
                      className="mt-2"
                      onClick={() => setValue('formato', 'excel')}
                    >
                      Selecionar
                    </Button>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer border-2 ${formato === 'csv' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                  <CardContent className="p-4 text-center">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                    <h3 className="font-medium">CSV</h3>
                    <p className="text-sm text-gray-600">Para importação em outros sistemas</p>
                    <Button
                      type="button"
                      variant={formato === 'csv' ? 'default' : 'outline'}
                      size="sm"
                      className="mt-2"
                      onClick={() => setValue('formato', 'csv')}
                    >
                      Selecionar
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Ações */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline">
          <Eye className="h-4 w-4 mr-2" />
          Visualizar
        </Button>
        <Button type="submit" disabled={isGenerating}>
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Gerando...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Gerar Relatório
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
```

## 🔌 API Routes

### 📊 API Principal de Relatórios (route.ts)
```typescript
// app/api/relatorios/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { generatePDF, generateExcel, generateCSV } from '@/lib/report-generators';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      nome,
      tipo,
      formato,
      periodo,
      filtros,
      colunas,
      agrupamento,
      ordenacao,
      observacoes,
    } = body;

    // Buscar dados baseado no tipo de relatório
    const dados = await buscarDadosRelatorio(tipo, periodo, filtros);

    // Gerar relatório no formato solicitado
    let arquivo;
    let mimeType;
    let extensao;

    switch (formato) {
      case 'pdf':
        arquivo = await generatePDF(dados, colunas, {
          titulo: nome,
          tipo,
          periodo,
          observacoes,
        });
        mimeType = 'application/pdf';
        extensao = 'pdf';
        break;
      
      case 'excel':
        arquivo = await generateExcel(dados, colunas, {
          titulo: nome,
          tipo,
          periodo,
          observacoes,
        });
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        extensao = 'xlsx';
        break;
      
      case 'csv':
        arquivo = await generateCSV(dados, colunas);
        mimeType = 'text/csv';
        extensao = 'csv';
        break;
      
      default:
        throw new Error('Formato não suportado');
    }

    // Salvar registro do relatório
    const relatorio = await prisma.relatorio.create({
      data: {
        nome,
        tipo,
        formato,
        periodoInicio: new Date(periodo.inicio),
        periodoFim: new Date(periodo.fim),
        filtros: JSON.stringify(filtros),
        colunas: JSON.stringify(colunas),
        observacoes,
        status: 'concluido',
        tamanhoArquivo: arquivo.length,
        geradoPorId: parseInt(session.user.id),
        geradoEm: new Date(),
      },
    });

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        acao: 'relatorio_gerado',
        usuarioId: parseInt(session.user.id),
        detalhes: JSON.stringify({
          relatorioId: relatorio.id,
          nome,
          tipo,
          formato,
          registros: dados.length,
        }),
        timestamp: new Date(),
      },
    });

    // Retornar arquivo
    const nomeArquivo = `${nome.replace(/[^a-zA-Z0-9]/g, '_')}.${extensao}`;
    
    return new NextResponse(arquivo, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `attachment; filename="${nomeArquivo}"`,
        'Content-Length': arquivo.length.toString(),
      },
    });

  } catch (error) {
    console.error('Erro ao gerar relatório:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

async function buscarDadosRelatorio(tipo: string, periodo: any, filtros: any) {
  const where: any = {
    data: {
      gte: new Date(periodo.inicio),
      lte: new Date(periodo.fim),
    },
  };

  // Aplicar filtros específicos
  if (filtros.funcionarios?.length > 0) {
    where.funcionarioId = { in: filtros.funcionarios };
  }

  if (filtros.departamentos?.length > 0) {
    where.funcionario = {
      departamento: { in: filtros.departamentos },
    };
  }

  if (filtros.status?.length > 0) {
    where.status = { in: filtros.status };
  }

  // Buscar dados baseado no tipo
  switch (tipo) {
    case 'funcionario':
      return await prisma.registroPonto.findMany({
        where,
        include: {
          funcionario: {
            select: {
              nome: true,
              matricula: true,
              departamento: true,
            },
          },
        },
        orderBy: [
          { funcionario: { nome: 'asc' } },
          { data: 'asc' },
        ],
      });

    case 'periodo':
      return await prisma.registroPonto.findMany({
        where,
        include: {
          funcionario: {
            select: {
              nome: true,
              matricula: true,
              departamento: true,
            },
          },
        },
        orderBy: [
          { data: 'asc' },
          { funcionario: { nome: 'asc' } },
        ],
      });

    case 'frequencia':
      return await prisma.registroPonto.findMany({
        where: {
          ...where,
          OR: [
            { atraso: { gt: 0 } },
            { falta: true },
          ],
        },
        include: {
          funcionario: {
            select: {
              nome: true,
              matricula: true,
              departamento: true,
            },
          },
        },
        orderBy: [
          { data: 'desc' },
        ],
      });

    default:
      return await prisma.registroPonto.findMany({
        where,
        include: {
          funcionario: {
            select: {
              nome: true,
              matricula: true,
              departamento: true,
            },
          },
        },
      });
  }
}
```

## 🗄️ Schema do Banco de Dados

### 📊 Modelo Prisma
```prisma
model Relatorio {
  id              Int       @id @default(autoincrement())
  nome            String
  tipo            String    // 'funcionario', 'periodo', 'frequencia', etc.
  formato         String    // 'pdf', 'excel', 'csv'
  periodoInicio   DateTime  @map("periodo_inicio")
  periodoFim      DateTime  @map("periodo_fim")
  filtros         String    // JSON com filtros aplicados
  colunas         String    // JSON com colunas selecionadas
  observacoes     String?
  status          String    @default("processando") // 'processando', 'concluido', 'erro'
  tamanhoArquivo  Int?      @map("tamanho_arquivo") // em bytes
  caminhoArquivo  String?   @map("caminho_arquivo")
  geradoEm        DateTime  @map("gerado_em")
  geradoPorId     Int       @map("gerado_por_id")
  downloadCount   Int       @default(0) @map("download_count")
  ultimoDownload  DateTime? @map("ultimo_download")

  // Relacionamentos
  geradoPor       Usuario   @relation(fields: [geradoPorId], references: [id])

  @@map("relatorios")
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Dashboard de relatórios
- [ ] Construtor de relatórios
- [ ] Templates predefinidos
- [ ] Filtros avançados
- [ ] Seleção de colunas
- [ ] Múltiplos formatos (PDF, Excel, CSV)
- [ ] Agendamento de relatórios
- [ ] Histórico de relatórios
- [ ] Download e compartilhamento

### 🔧 Validações
- [ ] Validação de períodos
- [ ] Controle de permissões
- [ ] Limite de registros
- [ ] Otimização de consultas
- [ ] Cache de relatórios

## 🚀 Próximos Passos
1. **Estatísticas** - Análises avançadas
2. **Empresa Principal** - Gestão da empresa
3. **Configurações de Ponto** - Parâmetros específicos
