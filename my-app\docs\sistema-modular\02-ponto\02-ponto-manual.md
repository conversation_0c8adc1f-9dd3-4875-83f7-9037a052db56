# ✋ MÓDULO PONTO MANUAL - Sistema RLPONTO

## 📋 Visão Geral
Módulo para registro manual de ponto quando a biometria não está disponível ou como fallback.

## 🎯 Funcionalidades
- Registro manual com justificativa
- Seleção de funcionário
- Validação de horários
- Aprovação de supervisor
- Geolocalização opcional
- Foto comprobatória
- Histórico de registros manuais

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── ponto/
│           ├── manual/
│           │   ├── page.tsx                # Página principal
│           │   └── components/
│           │       ├── funcionario-selector.tsx
│           │       ├── horario-input.tsx
│           │       ├── justificativa-form.tsx
│           │       └── foto-comprobatoria.tsx
│           └── api/
│               └── manual/
│                   ├── route.ts            # API principal
│                   ├── funcionarios/
│                   │   └── route.ts        # Busca funcionários
│                   └── aprovar/
│                       └── route.ts        # Aprovação supervisor
├── components/
│   └── ponto/
│       ├── manual-form.tsx                # Formulário principal
│       ├── geolocation-capture.tsx        # Captura de localização
│       ├── photo-capture.tsx              # Captura de foto
│       └── approval-status.tsx            # Status de aprovação
└── lib/
    └── validations/
        └── ponto-manual.ts                # Validações específicas
```

## 🔧 Implementação Técnica

### 📝 Página Principal (page.tsx)
```typescript
// app/(dashboard)/ponto/manual/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { ManualForm } from '@/components/ponto/manual-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Hand, AlertTriangle, Clock } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Ponto Manual - RLPONTO',
  description: 'Registro manual de ponto',
};

export default function PontoManualPage() {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Hand className="h-8 w-8 text-orange-600" />
          <h1 className="text-3xl font-bold text-gray-900">Ponto Manual</h1>
        </div>
        <p className="text-gray-600">Registre o ponto manualmente quando necessário</p>
      </div>

      {/* Aviso */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          O registro manual de ponto requer justificativa e pode necessitar aprovação do supervisor.
        </AlertDescription>
      </Alert>

      {/* Formulário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-orange-600" />
            <span>Registro Manual</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<FormSkeleton />}>
            <ManualForm />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function FormSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}
```

### 📝 Formulário Manual (manual-form.tsx)
```typescript
// components/ponto/manual-form.tsx
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FuncionarioSelector } from './funcionario-selector';
import { GeolocationCapture } from './geolocation-capture';
import { PhotoCapture } from './photo-capture';
import { Loader2, MapPin, Camera } from 'lucide-react';

const pontoManualSchema = z.object({
  funcionarioId: z.number().min(1, 'Selecione um funcionário'),
  tipoRegistro: z.enum(['entrada', 'saida', 'intervalo_inicio', 'intervalo_fim']),
  dataHora: z.string().min(1, 'Data e hora são obrigatórias'),
  justificativa: z.string().min(10, 'Justificativa deve ter pelo menos 10 caracteres'),
  localizacao: z.object({
    latitude: z.number(),
    longitude: z.number(),
    endereco: z.string().optional(),
  }).optional(),
  fotoUrl: z.string().optional(),
});

type PontoManualData = z.infer<typeof pontoManualSchema>;

export function ManualForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [localizacao, setLocalizacao] = useState<any>(null);
  const [fotoUrl, setFotoUrl] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<PontoManualData>({
    resolver: zodResolver(pontoManualSchema),
    defaultValues: {
      dataHora: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:mm
    },
  });

  const funcionarioId = watch('funcionarioId');
  const tipoRegistro = watch('tipoRegistro');

  useEffect(() => {
    if (localizacao) {
      setValue('localizacao', localizacao);
    }
  }, [localizacao, setValue]);

  useEffect(() => {
    if (fotoUrl) {
      setValue('fotoUrl', fotoUrl);
    }
  }, [fotoUrl, setValue]);

  const onSubmit = async (data: PontoManualData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/ponto/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao registrar ponto');
      }

      const result = await response.json();
      
      setSuccess(true);
      reset();
      setLocalizacao(null);
      setFotoUrl(null);

      // Mostrar mensagem de sucesso por 3 segundos
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Erro ao registrar ponto manual:', error);
      setError(error instanceof Error ? error.message : 'Erro interno do servidor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTipoRegistroLabel = (tipo: string) => {
    switch (tipo) {
      case 'entrada':
        return 'Entrada';
      case 'saida':
        return 'Saída';
      case 'intervalo_inicio':
        return 'Início do Intervalo';
      case 'intervalo_fim':
        return 'Fim do Intervalo';
      default:
        return tipo;
    }
  };

  if (success) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <AlertDescription className="text-green-800">
          Ponto registrado com sucesso! O registro será enviado para aprovação.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Seleção de Funcionário */}
      <div className="space-y-2">
        <Label>Funcionário *</Label>
        <FuncionarioSelector
          onSelect={(funcionario) => setValue('funcionarioId', funcionario.id)}
          selectedId={funcionarioId}
        />
        {errors.funcionarioId && (
          <p className="text-sm text-red-600">{errors.funcionarioId.message}</p>
        )}
      </div>

      {/* Tipo de Registro */}
      <div className="space-y-2">
        <Label htmlFor="tipoRegistro">Tipo de Registro *</Label>
        <Select onValueChange={(value) => setValue('tipoRegistro', value as any)}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione o tipo de registro" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="entrada">Entrada</SelectItem>
            <SelectItem value="saida">Saída</SelectItem>
            <SelectItem value="intervalo_inicio">Início do Intervalo</SelectItem>
            <SelectItem value="intervalo_fim">Fim do Intervalo</SelectItem>
          </SelectContent>
        </Select>
        {errors.tipoRegistro && (
          <p className="text-sm text-red-600">{errors.tipoRegistro.message}</p>
        )}
      </div>

      {/* Data e Hora */}
      <div className="space-y-2">
        <Label htmlFor="dataHora">Data e Hora *</Label>
        <Input
          id="dataHora"
          type="datetime-local"
          {...register('dataHora')}
          max={new Date().toISOString().slice(0, 16)}
        />
        {errors.dataHora && (
          <p className="text-sm text-red-600">{errors.dataHora.message}</p>
        )}
      </div>

      {/* Justificativa */}
      <div className="space-y-2">
        <Label htmlFor="justificativa">Justificativa *</Label>
        <Textarea
          id="justificativa"
          {...register('justificativa')}
          placeholder="Explique o motivo do registro manual (ex: problema na biometria, esquecimento, etc.)"
          rows={3}
        />
        {errors.justificativa && (
          <p className="text-sm text-red-600">{errors.justificativa.message}</p>
        )}
      </div>

      {/* Localização */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-2">
          <MapPin className="h-4 w-4" />
          <span>Localização (Opcional)</span>
        </Label>
        <GeolocationCapture
          onLocationCapture={setLocalizacao}
          currentLocation={localizacao}
        />
      </div>

      {/* Foto Comprobatória */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-2">
          <Camera className="h-4 w-4" />
          <span>Foto Comprobatória (Opcional)</span>
        </Label>
        <PhotoCapture
          onPhotoCapture={setFotoUrl}
          currentPhoto={fotoUrl}
        />
      </div>

      {/* Botão de Envio */}
      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        Registrar Ponto Manual
      </Button>

      {/* Informação sobre aprovação */}
      <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
        <p className="font-medium">ℹ️ Informações importantes:</p>
        <ul className="mt-1 space-y-1 text-xs">
          <li>• Registros manuais podem necessitar aprovação do supervisor</li>
          <li>• A localização e foto ajudam na validação do registro</li>
          <li>• Justificativas detalhadas aceleram o processo de aprovação</li>
        </ul>
      </div>
    </form>
  );
}
```

### 👤 Seletor de Funcionário (funcionario-selector.tsx)
```typescript
// components/ponto/funcionario-selector.tsx
'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Search, User, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Funcionario {
  id: number;
  nomeCompleto: string;
  cargo: string;
  setor: string;
  matriculaEmpresa?: string;
  fotoUrl?: string;
}

interface FuncionarioSelectorProps {
  onSelect: (funcionario: Funcionario) => void;
  selectedId?: number;
}

export function FuncionarioSelector({ onSelect, selectedId }: FuncionarioSelectorProps) {
  const [search, setSearch] = useState('');
  const [funcionarios, setFuncionarios] = useState<Funcionario[]>([]);
  const [selectedFuncionario, setSelectedFuncionario] = useState<Funcionario | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (search.length >= 2) {
      searchFuncionarios();
    } else {
      setFuncionarios([]);
      setShowResults(false);
    }
  }, [search]);

  useEffect(() => {
    if (selectedId && !selectedFuncionario) {
      fetchFuncionario(selectedId);
    }
  }, [selectedId, selectedFuncionario]);

  const searchFuncionarios = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/ponto/manual/funcionarios?search=${encodeURIComponent(search)}`);
      if (response.ok) {
        const data = await response.json();
        setFuncionarios(data.funcionarios);
        setShowResults(true);
      }
    } catch (error) {
      console.error('Erro ao buscar funcionários:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFuncionario = async (id: number) => {
    try {
      const response = await fetch(`/api/funcionarios/${id}`);
      if (response.ok) {
        const funcionario = await response.json();
        setSelectedFuncionario(funcionario);
      }
    } catch (error) {
      console.error('Erro ao buscar funcionário:', error);
    }
  };

  const handleSelect = (funcionario: Funcionario) => {
    setSelectedFuncionario(funcionario);
    setShowResults(false);
    setSearch('');
    onSelect(funcionario);
  };

  const handleClear = () => {
    setSelectedFuncionario(null);
    setSearch('');
    setShowResults(false);
  };

  if (selectedFuncionario) {
    return (
      <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50 border-blue-200">
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={selectedFuncionario.fotoUrl} />
            <AvatarFallback>
              {selectedFuncionario.nomeCompleto.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-gray-900">{selectedFuncionario.nomeCompleto}</p>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>{selectedFuncionario.cargo}</span>
              <span>•</span>
              <span>{selectedFuncionario.setor}</span>
              {selectedFuncionario.matriculaEmpresa && (
                <>
                  <span>•</span>
                  <Badge variant="outline" className="text-xs">
                    {selectedFuncionario.matriculaEmpresa}
                  </Badge>
                </>
              )}
            </div>
          </div>
        </div>
        <Button variant="ghost" size="sm" onClick={handleClear}>
          <X className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder="Buscar funcionário por nome, CPF ou matrícula..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Resultados da busca */}
      {showResults && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {isLoading ? (
            <div className="p-3 text-center text-gray-500">
              Buscando funcionários...
            </div>
          ) : funcionarios.length > 0 ? (
            funcionarios.map((funcionario) => (
              <button
                key={funcionario.id}
                type="button"
                onClick={() => handleSelect(funcionario)}
                className="w-full p-3 text-left hover:bg-gray-50 border-b last:border-b-0 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={funcionario.fotoUrl} />
                    <AvatarFallback className="text-xs">
                      {funcionario.nomeCompleto.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {funcionario.nomeCompleto}
                    </p>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="truncate">{funcionario.cargo}</span>
                      <span>•</span>
                      <span className="truncate">{funcionario.setor}</span>
                    </div>
                  </div>
                  {funcionario.matriculaEmpresa && (
                    <Badge variant="outline" className="text-xs">
                      {funcionario.matriculaEmpresa}
                    </Badge>
                  )}
                </div>
              </button>
            ))
          ) : (
            <div className="p-3 text-center text-gray-500">
              <User className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p>Nenhum funcionário encontrado</p>
              <p className="text-xs">Tente buscar por nome, CPF ou matrícula</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
```

## 🔌 API Routes

### 📝 API Principal (route.ts)
```typescript
// app/api/ponto/manual/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { pontoManualSchema } from '@/lib/validations/ponto-manual';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = pontoManualSchema.parse(body);

    // Verificar se o funcionário existe e está ativo
    const funcionario = await prisma.funcionario.findFirst({
      where: {
        id: validatedData.funcionarioId,
        ativo: true,
      },
    });

    if (!funcionario) {
      return NextResponse.json(
        { error: 'Funcionário não encontrado ou inativo' },
        { status: 400 }
      );
    }

    // Verificar se não há registro duplicado no mesmo horário
    const dataHora = new Date(validatedData.dataHora);
    const existingRecord = await prisma.registroPonto.findFirst({
      where: {
        funcionarioId: validatedData.funcionarioId,
        timestamp: {
          gte: new Date(dataHora.getTime() - 5 * 60 * 1000), // 5 minutos antes
          lte: new Date(dataHora.getTime() + 5 * 60 * 1000), // 5 minutos depois
        },
      },
    });

    if (existingRecord) {
      return NextResponse.json(
        { error: 'Já existe um registro próximo a este horário' },
        { status: 400 }
      );
    }

    // Criar registro de ponto manual
    const registroPonto = await prisma.registroPonto.create({
      data: {
        funcionarioId: validatedData.funcionarioId,
        timestamp: dataHora,
        tipo: validatedData.tipoRegistro,
        metodo: 'manual',
        justificativa: validatedData.justificativa,
        localizacao: validatedData.localizacao ? JSON.stringify(validatedData.localizacao) : null,
        fotoUrl: validatedData.fotoUrl,
        aprovado: false, // Requer aprovação
        criadoPor: parseInt(session.user.id),
        criadoEm: new Date(),
      },
    });

    // Log da ação
    await prisma.logAcesso.create({
      data: {
        tipo: 'ponto_manual',
        funcionarioId: validatedData.funcionarioId,
        usuarioId: parseInt(session.user.id),
        timestamp: new Date(),
        sucesso: true,
        detalhes: {
          tipoRegistro: validatedData.tipoRegistro,
          justificativa: validatedData.justificativa,
        },
      },
    });

    return NextResponse.json({
      success: true,
      registro: {
        id: registroPonto.id,
        timestamp: registroPonto.timestamp,
        tipo: registroPonto.tipo,
        aprovado: registroPonto.aprovado,
      },
    });
  } catch (error) {
    console.error('Erro ao registrar ponto manual:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Formulário de registro manual
- [ ] Seleção de funcionário
- [ ] Validação de horários
- [ ] Justificativa obrigatória
- [ ] Captura de localização
- [ ] Foto comprobatória
- [ ] Sistema de aprovação
- [ ] Histórico de registros manuais

### 🔧 Validações
- [ ] Funcionário ativo
- [ ] Horário válido (não futuro)
- [ ] Não duplicação de registros
- [ ] Justificativa mínima
- [ ] Permissões de usuário

## 🚀 Próximos Passos
1. **Período de Apuração** - Dashboard e classificação
2. **Relatórios** - Análise de registros manuais
3. **Administração** - Aprovação de registros
