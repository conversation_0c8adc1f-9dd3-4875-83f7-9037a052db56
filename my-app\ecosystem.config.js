module.exports = {
  apps: [
    {
      // Configuração para produção
      name: 'rlponto-prod',
      script: 'npm',
      args: 'start',
      cwd: '/opt/rlponto/current',
      instances: 1, // Pode ser aumentado conforme necessário
      exec_mode: 'cluster',

      // Variáveis de ambiente
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },

      // Logs
      error_file: '/var/log/rlponto/error.log',
      out_file: '/var/log/rlponto/out.log',
      log_file: '/var/log/rlponto/combined.log',
      time: true,

      // Configurações de memória e performance
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',

      // Auto restart
      autorestart: true,
      watch: false,
      max_restarts: 10,
      min_uptime: '10s',

      // Configurações de deploy
      post_update: ['npm install', 'npx prisma generate', 'npx prisma migrate deploy'],

      // Health check
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
    }
  ],

  // Configuração de deploy
  deploy: {
    production: {
      user: 'rlponto',
      host: '************',
      ref: 'origin/main',
      repo: 'https://github.com/seu-usuario/rlponto-system.git', // Ajustar conforme necessário
      path: '/opt/rlponto',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npx prisma generate && npx prisma migrate deploy && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes'
    }
  }
};
