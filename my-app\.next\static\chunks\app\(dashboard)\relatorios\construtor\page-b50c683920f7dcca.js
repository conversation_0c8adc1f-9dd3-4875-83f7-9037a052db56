(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[567],{1823:(e,a,s)=>{"use strict";s.d(a,{ReportBuilder:()=>b});var r=s(5155),t=s(2115),o=s(5294),n=s(1007),i=s(4213),c=s(9074),l=s(4186),d=s(3109),m=s(2713),x=s(2657),p=s(2525);let h=[{id:"func_nome",name:"Nome do Funcion\xe1rio",type:"text",category:"funcionario",icon:(0,r.jsx)(n.A,{className:"w-4 h-4"})},{id:"func_matricula",name:"<PERSON>r\xedcula",type:"text",category:"funcionario",icon:(0,r.jsx)(i.A,{className:"w-4 h-4"})},{id:"func_departamento",name:"Departamento",type:"text",category:"funcionario",icon:(0,r.jsx)(i.A,{className:"w-4 h-4"})},{id:"func_cargo",name:"Cargo",type:"text",category:"funcionario",icon:(0,r.jsx)(i.A,{className:"w-4 h-4"})},{id:"ponto_data",name:"Data",type:"date",category:"ponto",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{id:"ponto_entrada",name:"Hor\xe1rio de Entrada",type:"text",category:"ponto",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{id:"ponto_saida",name:"Hor\xe1rio de Sa\xedda",type:"text",category:"ponto",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{id:"ponto_horas_normais",name:"Horas Normais",type:"number",category:"ponto",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{id:"ponto_horas_extras",name:"Horas Extras",type:"number",category:"ponto",icon:(0,r.jsx)(d.A,{className:"w-4 h-4"})},{id:"ponto_atrasos",name:"Atrasos (min)",type:"number",category:"ponto",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{id:"periodo_inicio",name:"In\xedcio do Per\xedodo",type:"date",category:"periodo",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{id:"periodo_fim",name:"Fim do Per\xedodo",type:"date",category:"periodo",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{id:"calc_total_horas",name:"Total de Horas",type:"number",category:"calculo",icon:(0,r.jsx)(m.A,{className:"w-4 h-4"})},{id:"calc_percentual_presenca",name:"Percentual de Presen\xe7a",type:"number",category:"calculo",icon:(0,r.jsx)(m.A,{className:"w-4 h-4"})},{id:"calc_media_horas_dia",name:"M\xe9dia Horas/Dia",type:"number",category:"calculo",icon:(0,r.jsx)(m.A,{className:"w-4 h-4"})}],u={funcionario:"bg-blue-100 text-blue-800 border-blue-200",ponto:"bg-green-100 text-green-800 border-green-200",periodo:"bg-yellow-100 text-yellow-800 border-yellow-200",calculo:"bg-purple-100 text-purple-800 border-purple-200"},g={funcionario:"Funcion\xe1rio",ponto:"Registros de Ponto",periodo:"Per\xedodo",calculo:"C\xe1lculos"};function b(){let[e,a]=(0,t.useState)([]),[s,n]=(0,t.useState)(!1),c=(0,t.useCallback)(s=>{if(!s.destination)return;let{source:r,destination:t}=s;if("available"===r.droppableId&&"selected"===t.droppableId){let s=h[r.index],o={id:"".concat(s.id,"_").concat(Date.now()),fieldId:s.id,type:"table",config:{}},n=[...e];n.splice(t.index,0,o),a(n)}if("selected"===r.droppableId&&"selected"===t.droppableId){let s=[...e],[o]=s.splice(r.index,1);s.splice(t.index,0,o),a(s)}},[e]),l=(0,t.useCallback)(e=>{a(a=>a.filter(a=>a.id!==e))},[]),d=(0,t.useCallback)((e,s)=>{a(a=>a.map(a=>a.id===e?{...a,type:s}:a))},[]);return(0,r.jsx)(o.JY,{onDragEnd:c,children:(0,r.jsxs)("div",{className:"flex h-96 border border-gray-200 rounded-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"w-80 bg-gray-50 border-r border-gray-200 p-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Biblioteca de Campos"}),Object.entries(g).map(e=>{let[a,s]=e;return(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:s}),(0,r.jsx)(o.gL,{droppableId:"available",isDropDisabled:!0,children:e=>(0,r.jsxs)("div",{ref:e.innerRef,...e.droppableProps,className:"space-y-2",children:[h.filter(e=>e.category===a).map((e,a)=>(0,r.jsx)(o.sx,{draggableId:e.id,index:a,children:(a,s)=>(0,r.jsxs)("div",{ref:a.innerRef,...a.draggableProps,...a.dragHandleProps,className:"\n                            p-3 rounded-lg border cursor-move transition-all\n                            ".concat(u[e.category],"\n                            ").concat(s.isDragging?"shadow-lg scale-105":"hover:shadow-md","\n                          "),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.icon,(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name})]}),(0,r.jsx)("div",{className:"text-xs mt-1 opacity-75",children:e.type})]})},e.id)),e.placeholder]})})]},a)})]}),(0,r.jsxs)("div",{className:"flex-1 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"\xc1rea de Design"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("button",{onClick:()=>n(!s),className:"px-3 py-2 rounded-md text-sm font-medium ".concat(s?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-1 inline"}),s?"Editar":"Preview"]})})]}),(0,r.jsx)(o.gL,{droppableId:"selected",children:(a,t)=>(0,r.jsxs)("div",{ref:a.innerRef,...a.droppableProps,className:"\n              min-h-96 border-2 border-dashed rounded-lg p-4 transition-colors\n              ".concat(t.isDraggingOver?"border-blue-400 bg-blue-50":"border-gray-300","\n              ").concat(0===e.length?"flex items-center justify-center":"","\n            "),children:[0===e.length?(0,r.jsxs)("div",{className:"text-center text-gray-500",children:[(0,r.jsx)(i.A,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"Arraste campos aqui"}),(0,r.jsx)("p",{className:"text-sm",children:"Comece arrastando campos da biblioteca para criar seu relat\xf3rio"})]}):(0,r.jsx)("div",{className:"space-y-4",children:e.map((e,a)=>{let t,n=(t=e.fieldId,h.find(e=>e.id===t));return n?(0,r.jsx)(o.sx,{draggableId:e.id,index:a,children:(a,t)=>(0,r.jsxs)("div",{ref:a.innerRef,...a.draggableProps,className:"\n                            bg-white border rounded-lg p-4 transition-all\n                            ".concat(t.isDragging?"shadow-lg scale-105":"shadow-sm hover:shadow-md","\n                          "),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",...a.dragHandleProps,children:[n.icon,(0,r.jsx)("span",{className:"font-medium",children:n.name}),(0,r.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat(u[n.category]),children:g[n.category]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("select",{value:e.type,onChange:a=>d(e.id,a.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[(0,r.jsx)("option",{value:"table",children:"Tabela"}),(0,r.jsx)("option",{value:"chart",children:"Gr\xe1fico"}),(0,r.jsx)("option",{value:"card",children:"Card"}),(0,r.jsx)("option",{value:"filter",children:"Filtro"})]}),(0,r.jsx)("button",{onClick:()=>l(e.id),className:"text-red-600 hover:text-red-800",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})})]})]}),s&&(0,r.jsxs)("div",{className:"border-t pt-3 mt-3",children:["table"===e.type&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDCCA Tabela: ",n.name]}),"chart"===e.type&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDCC8 Gr\xe1fico: ",n.name]}),"card"===e.type&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83C\uDCCF Card: ",n.name]}),"filter"===e.type&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDD0D Filtro: ",n.name]})]})]})},e.id):null})}),a.placeholder]})})]})]})})}},2884:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,1823))}},e=>{e.O(0,[309,874,457,441,964,358],()=>e(e.s=2884)),_N_E=e.O()}]);