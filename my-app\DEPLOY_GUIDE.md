# 🚀 Guia de Deploy - Sistema RLPONTO

## 📋 Pré-requisitos

### No seu computador (Windows):
- ✅ <PERSON><PERSON> <PERSON>sh ou WSL instalado
- ✅ SSH client disponível
- ✅ Node.js 18+ instalado
- ✅ Acesso à rede local (************)

### Servidor LXC (j<PERSON> configurado):
- ✅ IP: ************
- ✅ SO: Ubuntu 22.04
- ✅ Usuário: root
- ✅ Senha: @Ric6109

## 🚀 Execução do Setup (Primeira vez)

### 1. Abrir Terminal
```bash
# No Windows, abrir Git Bash ou PowerShell com WSL
# Navegar para o diretório do projeto
cd /c/Users/<USER>/Documents/01projeto/my-app
```

### 2. Verificar Scripts
```bash
# Verificar se os scripts existem
ls -la scripts/

# Dar permissões (se necessário)
chmod +x scripts/*.sh
```

### 3. Executar Setup Completo
```bash
# Executar o script principal que faz tudo
./scripts/setup-complete.sh
```

**O script irá:**
1. ✅ Configurar SSH sem senha
2. ✅ Instalar Node.js, MySQL, Nginx no servidor
3. ✅ Criar usuário `rlponto`
4. ✅ Configurar estrutura de diretórios
5. ✅ Configurar backup automático
6. ✅ Configurar monitoramento

### 4. Primeiro Deploy
```bash
# Após o setup, fazer o primeiro deploy
./scripts/deploy.sh
```

## 🔄 Deploy Regular (Próximas vezes)

Para deploys futuros, apenas execute:

```bash
./scripts/deploy.sh
```

## 🔍 Verificação do Deploy

### 1. Acessar o Sistema
```
URL: http://************
```

### 2. Verificar Serviços
```bash
# Conectar ao servidor
ssh rlponto-prod

# Verificar aplicação
pm2 status

# Verificar logs
pm2 logs rlponto

# Verificar serviços do sistema
systemctl status nginx
systemctl status mysql
```

## 🛠️ Comandos Úteis

### SSH e Conexão:
```bash
# Conectar ao servidor
ssh rlponto-prod

# Executar comando remoto
ssh rlponto-prod 'pm2 status'

# Copiar arquivo para servidor
scp arquivo.txt rlponto-prod:/tmp/
```

### Gerenciamento da Aplicação:
```bash
# Status da aplicação
ssh rlponto-prod 'pm2 status'

# Reiniciar aplicação
ssh rlponto-prod 'pm2 restart rlponto'

# Ver logs em tempo real
ssh rlponto-prod 'pm2 logs rlponto --lines 50'

# Parar aplicação
ssh rlponto-prod 'pm2 stop rlponto'

# Iniciar aplicação
ssh rlponto-prod 'pm2 start rlponto'
```

### Backup e Monitoramento:
```bash
# Executar backup manual
ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh backup'

# Verificar status do sistema
ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh monitor'

# Gerar relatório
ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh report'

# Ver backups existentes
ssh rlponto-prod 'ls -la /opt/rlponto/backups/'
```

## 🚨 Troubleshooting

### Problema: SSH não conecta
```bash
# Verificar chave SSH
ls -la ~/.ssh/rl-ponto-next*

# Testar conexão manual
ssh -i ~/.ssh/rl-ponto-next root@************

# Reconfigurar SSH se necessário
./scripts/setup-production.sh
```

### Problema: Aplicação não inicia
```bash
# Ver logs detalhados
ssh rlponto-prod 'pm2 logs rlponto --lines 100'

# Verificar variáveis de ambiente
ssh rlponto-prod 'cat /opt/rlponto/shared/.env.production'

# Verificar se banco está rodando
ssh rlponto-prod 'systemctl status mysql'

# Reiniciar tudo
ssh rlponto-prod 'pm2 restart rlponto'
```

### Problema: Site não carrega
```bash
# Verificar Nginx
ssh rlponto-prod 'systemctl status nginx'

# Ver logs do Nginx
ssh rlponto-prod 'tail -f /var/log/nginx/rlponto_error.log'

# Testar configuração do Nginx
ssh rlponto-prod 'nginx -t'

# Reiniciar Nginx
ssh rlponto-prod 'systemctl restart nginx'
```

### Problema: Banco de dados
```bash
# Verificar MySQL
ssh rlponto-prod 'systemctl status mysql'

# Testar conexão com banco
ssh rlponto-prod 'mysql -u rlponto_user -p rlponto -e "SHOW TABLES;"'

# Ver logs do MySQL
ssh rlponto-prod 'tail -f /var/log/mysql/error.log'
```

## 📊 Monitoramento Automático

### Backups Automáticos:
- ✅ **Diário às 2h**: Backup completo do banco e uploads
- ✅ **A cada 15min**: Verificação de serviços
- ✅ **Semanal**: Relatório de status

### Localização dos Backups:
```
/opt/rlponto/backups/
├── database/           # Backups do MySQL
│   ├── rlponto_20240115_020000.sql.gz
│   └── rlponto_20240116_020000.sql.gz
└── uploads/           # Backups dos uploads
    ├── uploads_20240115_020000.tar.gz
    └── uploads_20240116_020000.tar.gz
```

## 🔧 Configurações Importantes

### Estrutura no Servidor:
```
/opt/rlponto/
├── current/                 # Aplicação atual (symlink)
├── releases/               # Releases anteriores
├── shared/                # Arquivos compartilhados
│   ├── .env.production    # Variáveis de ambiente
│   ├── logs/             # Logs
│   └── uploads/          # Uploads
└── backups/              # Backups automáticos
```

### Portas Utilizadas:
- **80**: HTTP (Nginx)
- **3000**: Aplicação Node.js
- **3306**: MySQL
- **22**: SSH

### Usuários:
- **root**: Administração do sistema
- **rlponto**: Execução da aplicação

## 📞 Suporte

### Em caso de problemas:

1. **Verificar logs** primeiro
2. **Executar monitoramento**: 
   ```bash
   ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh monitor'
   ```
3. **Consultar este guia**
4. **Contatar equipe técnica**

### Informações para Suporte:
- IP do servidor: ************
- Versão do sistema: Ubuntu 22.04
- Aplicação: Sistema RLPONTO
- Tecnologias: Next.js, MySQL, Nginx, PM2

---

## 🎯 Resumo dos Comandos Principais

```bash
# Setup inicial (uma vez)
./scripts/setup-complete.sh

# Deploy regular
./scripts/deploy.sh

# Conectar ao servidor
ssh rlponto-prod

# Status da aplicação
ssh rlponto-prod 'pm2 status'

# Logs da aplicação
ssh rlponto-prod 'pm2 logs rlponto'

# Backup manual
ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh backup'

# Acessar sistema
# http://************
```

**🎉 Pronto! Seu ambiente de produção está configurado e funcionando!**
