#!/bin/bash

# 🖥️ Script de Configuração do Servidor - Sistema RLPONTO
# Configura o container Ubuntu 22.04 com todas as dependências necessárias

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
NODE_VERSION="18"
MYSQL_ROOT_PASSWORD="RLPonto@2024#Secure"
APP_USER="rlponto"
APP_DIR="/opt/rlponto"
DOMAIN="rlponto.local"

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

echo -e "${BLUE}🖥️ Configurando Servidor de Produção - Sistema RLPONTO${NC}"
echo -e "${BLUE}====================================================${NC}"

# 1. Atualizar sistema
log "1. Atualizando sistema Ubuntu 22.04..."
apt update && apt upgrade -y

# 2. Instalar dependências básicas
log "2. Instalando dependências básicas..."
apt install -y \
    curl \
    wget \
    git \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    ufw \
    fail2ban \
    htop \
    nano \
    vim \
    tree \
    jq \
    build-essential

# 3. Configurar firewall
log "3. Configurando firewall UFW..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000/tcp  # Next.js dev (temporário)
ufw --force enable

# 4. Configurar Fail2Ban
log "4. Configurando Fail2Ban..."
systemctl enable fail2ban
systemctl start fail2ban

# 5. Instalar Node.js
log "5. Instalando Node.js ${NODE_VERSION}..."
curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
apt install -y nodejs

# Verificar instalação
node_version=$(node --version)
npm_version=$(npm --version)
log "Node.js instalado: ${node_version}"
log "NPM instalado: ${npm_version}"

# 6. Instalar PM2
log "6. Instalando PM2..."
npm install -g pm2
pm2 startup systemd -u root --hp /root

# 7. Instalar MySQL
log "7. Instalando MySQL 8.0..."
apt install -y mysql-server mysql-client

# Configurar MySQL
log "Configurando MySQL..."
mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '${MYSQL_ROOT_PASSWORD}';"
mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "CREATE DATABASE IF NOT EXISTS rlponto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "CREATE USER IF NOT EXISTS 'rlponto_user'@'localhost' IDENTIFIED BY 'RLPonto@DB2024#';"
mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "GRANT ALL PRIVILEGES ON rlponto.* TO 'rlponto_user'@'localhost';"
mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "FLUSH PRIVILEGES;"

# 8. Instalar Nginx
log "8. Instalando Nginx..."
apt install -y nginx

# 9. Criar usuário da aplicação
log "9. Criando usuário da aplicação..."
if ! id "${APP_USER}" &>/dev/null; then
    useradd -m -s /bin/bash ${APP_USER}
    usermod -aG sudo ${APP_USER}
    log "Usuário ${APP_USER} criado"
else
    log "Usuário ${APP_USER} já existe"
fi

# 10. Criar diretórios da aplicação
log "10. Criando estrutura de diretórios..."
mkdir -p ${APP_DIR}
mkdir -p ${APP_DIR}/releases
mkdir -p ${APP_DIR}/shared/logs
mkdir -p ${APP_DIR}/shared/uploads
mkdir -p /var/log/rlponto

# Definir permissões
chown -R ${APP_USER}:${APP_USER} ${APP_DIR}
chown -R ${APP_USER}:${APP_USER} /var/log/rlponto

# 11. Configurar Nginx
log "11. Configurando Nginx..."
cat > /etc/nginx/sites-available/rlponto << 'EOF'
server {
    listen 80;
    server_name rlponto.local ************;
    
    # Logs
    access_log /var/log/nginx/rlponto_access.log;
    error_log /var/log/nginx/rlponto_error.log;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Static files
    location /_next/static {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
    
    # Uploads
    location /uploads {
        alias /opt/rlponto/shared/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        proxy_pass http://localhost:3000;
        access_log off;
    }
}
EOF

# Ativar site
ln -sf /etc/nginx/sites-available/rlponto /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Testar configuração
nginx -t

# 12. Configurar logrotate
log "12. Configurando logrotate..."
cat > /etc/logrotate.d/rlponto << 'EOF'
/var/log/rlponto/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 rlponto rlponto
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

# 13. Configurar variáveis de ambiente
log "13. Configurando variáveis de ambiente..."
cat > ${APP_DIR}/shared/.env.production << 'EOF'
# Ambiente
NODE_ENV=production
PORT=3000

# Database
DATABASE_URL="mysql://rlponto_user:RLPonto@DB2024#@localhost:3306/rlponto"

# NextAuth.js
NEXTAUTH_SECRET="super-secret-key-change-in-production-2024"
NEXTAUTH_URL="http://************"

# Upload
UPLOAD_DIR="/opt/rlponto/shared/uploads"
MAX_FILE_SIZE=10485760

# Logs
LOG_LEVEL="info"
LOG_FILE="/var/log/rlponto/app.log"

# Email (configurar conforme necessário)
EMAIL_SERVER_HOST=""
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=""
EMAIL_SERVER_PASSWORD=""
EMAIL_FROM="<EMAIL>"
EOF

chown ${APP_USER}:${APP_USER} ${APP_DIR}/shared/.env.production
chmod 600 ${APP_DIR}/shared/.env.production

# 14. Reiniciar serviços
log "14. Reiniciando serviços..."
systemctl restart nginx
systemctl restart mysql
systemctl enable nginx
systemctl enable mysql

log "15. Configurando PM2..."
# Configurar PM2 para o usuário da aplicação
sudo -u ${APP_USER} pm2 startup systemd

echo -e "${BLUE}"
echo "================================================="
echo "🖥️ SERVIDOR CONFIGURADO COM SUCESSO!"
echo "================================================="
echo "IP: ************"
echo "Usuário da aplicação: ${APP_USER}"
echo "Diretório da aplicação: ${APP_DIR}"
echo "Banco de dados: rlponto"
echo "Usuário do banco: rlponto_user"
echo ""
echo "Serviços instalados:"
echo "✅ Node.js ${node_version}"
echo "✅ NPM ${npm_version}"
echo "✅ PM2"
echo "✅ MySQL 8.0"
echo "✅ Nginx"
echo "✅ UFW Firewall"
echo "✅ Fail2Ban"
echo ""
echo "Próximos passos:"
echo "1. Executar deploy da aplicação"
echo "2. Configurar backup automático"
echo "3. Configurar monitoramento"
echo -e "${NC}"

log "✅ Configuração do servidor concluída!"
