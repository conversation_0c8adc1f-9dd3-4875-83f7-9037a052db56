{"version": 3, "sources": ["../src/utils/shallowEqual.ts", "../src/index-rsc.ts"], "sourcesContent": ["function is(x: unknown, y: unknown) {\r\n  if (x === y) {\r\n    return x !== 0 || y !== 0 || 1 / x === 1 / y\r\n  } else {\r\n    return x !== x && y !== y\r\n  }\r\n}\r\n\r\nexport default function shallowEqual(objA: any, objB: any) {\r\n  if (is(objA, objB)) return true\r\n\r\n  if (\r\n    typeof objA !== 'object' ||\r\n    objA === null ||\r\n    typeof objB !== 'object' ||\r\n    objB === null\r\n  ) {\r\n    return false\r\n  }\r\n\r\n  const keysA = Object.keys(objA)\r\n  const keysB = Object.keys(objB)\r\n\r\n  if (keysA.length !== keysB.length) return false\r\n\r\n  for (let i = 0; i < keysA.length; i++) {\r\n    if (\r\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i]) ||\r\n      !is(objA[keysA[i]], objB[keysA[i]])\r\n    ) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  return true\r\n}\r\n", "import type * as normal from './index'\nimport type * as rsc from './index-rsc'\n\n// checks to make sure we didn't forgot to replicate any exports\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst _check: typeof normal = {} as typeof rsc\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst _check2: typeof rsc = {} as typeof normal\n\n// -------------------------------------------------------------------------------------\n\nconst throwNotSupportedError = ((\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  ...args: any[]\n): any => {\n  throw new Error(\n    'This function is not supported in React Server Components. Please only use this export in a Client Component.',\n  )\n}) as any\n\nexport {\n  throwNotSupportedError as Provider,\n  throwNotSupportedError as batch,\n  throwNotSupportedError as connect,\n  throwNotSupportedError as createDispatchHook,\n  throwNotSupportedError as createSelectorHook,\n  throwNotSupportedError as createStoreHook,\n  throwNotSupportedError as useDispatch,\n  throwNotSupportedError as useSelector,\n  throwNotSupportedError as useStore,\n}\nexport const ReactReduxContext = {} as any\nexport { default as shallowEqual } from './utils/shallowEqual'\n"], "mappings": ";AAAA,SAAS,GAAG,GAAY,GAAY;AAClC,MAAI,MAAM,GAAG;AACX,WAAO,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EAC7C,OAAO;AACL,WAAO,MAAM,KAAK,MAAM;AAAA,EAC1B;AACF;AAEe,SAAR,aAA8B,MAAW,MAAW;AACzD,MAAI,GAAG,MAAM,IAAI,EAAG,QAAO;AAE3B,MACE,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACA,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAE9B,MAAI,MAAM,WAAW,MAAM,OAAQ,QAAO;AAE1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KACpD,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAClC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACvBA,IAAM,yBAA0B,IAE3B,SACK;AACR,QAAM,IAAI;AAAA,IACR;AAAA,EACF;AACF;AAaO,IAAM,oBAAoB,CAAC;", "names": []}