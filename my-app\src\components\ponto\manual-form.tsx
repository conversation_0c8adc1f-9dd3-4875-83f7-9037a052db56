'use client';

import { useState, useEffect } from 'react';
import { Button, Input } from '@/components/ui';
import { 
  User, 
  Clock, 
  MapPin, 
  Camera, 
  FileText, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface Funcionario {
  id: string;
  nome: string;
  matricula: string;
  cargo: string;
}

interface ManualFormData {
  funcionarioId: string;
  tipo: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim';
  horario: string;
  data: string;
  justificativa: string;
  localizacao?: {
    latitude: number;
    longitude: number;
    endereco: string;
  };
  foto?: string;
}

export function ManualForm() {
  const [funcionarios, setFuncionarios] = useState<Funcionario[]>([]);
  const [formData, setFormData] = useState<ManualFormData>({
    funcionarioId: '',
    tipo: 'entrada',
    horario: '',
    data: '',
    justificativa: '',
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [capturingLocation, setCapturingLocation] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    fetchFuncionarios();
    // Definir horário e data atuais como padrão
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5);
    const currentDate = now.toISOString().split('T')[0];
    setFormData(prev => ({
      ...prev,
      horario: currentTime,
      data: currentDate
    }));
  }, []);

  const fetchFuncionarios = async () => {
    try {
      const response = await fetch('/api/ponto/manual/funcionarios');
      if (response.ok) {
        const data = await response.json();
        setFuncionarios(data.funcionarios || []);
      }
    } catch (error) {
      console.error('Erro ao buscar funcionários:', error);
    }
  };

  const captureLocation = async () => {
    if (!navigator.geolocation) {
      setError('Geolocalização não suportada pelo navegador');
      return;
    }

    setCapturingLocation(true);
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          
          // Buscar endereço usando reverse geocoding (simulado)
          const endereco = `Lat: ${latitude.toFixed(6)}, Lng: ${longitude.toFixed(6)}`;
          
          setFormData(prev => ({
            ...prev,
            localizacao: { latitude, longitude, endereco }
          }));
        } catch (error) {
          console.error('Erro ao obter endereço:', error);
        } finally {
          setCapturingLocation(false);
        }
      },
      (error) => {
        setError('Erro ao obter localização: ' + error.message);
        setCapturingLocation(false);
      },
      { enableHighAccuracy: true, timeout: 10000 }
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.funcionarioId || !formData.justificativa.trim()) {
      setError('Funcionário e justificativa são obrigatórios');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/ponto/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          timestamp: new Date(`${formData.data}T${formData.horario}`).toISOString(),
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSuccess(true);
        // Reset form
        setFormData({
          funcionarioId: '',
          tipo: 'entrada',
          horario: new Date().toTimeString().slice(0, 5),
          data: new Date().toISOString().split('T')[0],
          justificativa: '',
        });
      } else {
        setError(result.error || 'Erro ao registrar ponto');
      }
    } catch (error) {
      setError('Erro de conexão. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const funcionarioSelecionado = funcionarios.find(f => f.id === formData.funcionarioId);

  if (success) {
    return (
      <div className="text-center py-8">
        <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Registro Enviado com Sucesso!
        </h3>
        <p className="text-gray-600 mb-6">
          O registro manual foi enviado para aprovação do supervisor.
        </p>
        <Button onClick={() => setSuccess(false)} variant="primary">
          Fazer Novo Registro
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Seleção de Funcionário */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          <User className="h-4 w-4 inline mr-2" />
          Funcionário *
        </label>
        <select
          value={formData.funcionarioId}
          onChange={(e) => setFormData(prev => ({ ...prev, funcionarioId: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900"
          style={{ color: formData.funcionarioId ? '#111827' : '#6B7280' }}
          required
        >
          <option value="" style={{ color: '#6B7280' }}>Selecione um funcionário</option>
          {funcionarios.map((funcionario) => (
            <option key={funcionario.id} value={funcionario.id}>
              {funcionario.nome} - {funcionario.matricula}
            </option>
          ))}
        </select>
        
        {funcionarioSelecionado && (
          <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
            <strong>Cargo:</strong> {funcionarioSelecionado.cargo}
          </div>
        )}
      </div>

      {/* Data e Horário */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Data *
          </label>
          <input
            type="date"
            value={formData.data}
            onChange={(e) => setFormData(prev => ({ ...prev, data: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900"
            style={{ colorScheme: 'light' }}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Horário *
          </label>
          <input
            type="time"
            value={formData.horario}
            onChange={(e) => setFormData(prev => ({ ...prev, horario: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900"
            style={{ colorScheme: 'light' }}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Tipo de Registro *
          </label>
          <select
            value={formData.tipo}
            onChange={(e) => setFormData(prev => ({ ...prev, tipo: e.target.value as 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim' }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900"
            required
          >
            <option value="entrada">Entrada</option>
            <option value="saida">Saída</option>
            <option value="intervalo_inicio">Início Intervalo</option>
            <option value="intervalo_fim">Fim Intervalo</option>
          </select>
        </div>
      </div>

      {/* Justificativa */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          <FileText className="h-4 w-4 inline mr-2" />
          Justificativa *
        </label>
        <textarea
          value={formData.justificativa}
          onChange={(e) => setFormData(prev => ({ ...prev, justificativa: e.target.value }))}
          placeholder="Descreva o motivo do registro manual (ex: problema na biometria, trabalho externo, etc.)"
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 placeholder:text-gray-600"
          required
        />
      </div>

      {/* Localização */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          <MapPin className="h-4 w-4 inline mr-2" />
          Localização (Opcional)
        </label>
        <div className="flex space-x-2">
          <Button
            type="button"
            onClick={captureLocation}
            disabled={capturingLocation}
            variant="outline"
            className="flex-shrink-0"
          >
            {capturingLocation ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <MapPin className="h-4 w-4 mr-2" />
            )}
            Capturar Localização
          </Button>
          
          {formData.localizacao && (
            <div className="flex-1 text-sm text-gray-600 bg-green-50 p-2 rounded border">
              📍 {formData.localizacao.endereco}
            </div>
          )}
        </div>
      </div>

      {/* Botão de Envio */}
      <div className="flex space-x-4">
        <Button
          type="submit"
          disabled={loading}
          variant="primary"
          className="flex-1"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Enviando...
            </>
          ) : (
            <>
              <Clock className="h-4 w-4 mr-2" />
              Registrar Ponto Manual
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

