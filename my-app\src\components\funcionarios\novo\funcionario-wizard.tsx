'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useWizard } from '@/hooks/use-wizard';
import { ProgressIndicator } from './progress-indicator';
import { StepPessoal } from './step-pessoal';
import { StepProfissional } from './step-profissional';
import { StepConfirmacao } from './step-confirmacao';
import { Button } from '@/components/ui';
import { Loader2, ChevronLeft, ChevronRight, Check } from 'lucide-react';

const WIZARD_STEPS = [
  { id: 'pessoal', title: 'Dados Pessoais', description: 'Informações básicas do funcionário' },
  { id: 'profissional', title: 'Dados Profissionais', description: 'Cargo, setor e horários' },
  { id: 'confirmacao', title: 'Confirmação', description: 'Revisar e confirmar dados' },
];

export interface FuncionarioData {
  // Dados Pessoais
  nomeCompleto: string;
  cpf: string;
  rg?: string;
  email?: string;
  telefone?: string;
  celular?: string;
  
  // Endereço
  cep?: string;
  logradouro?: string;
  numero?: string;
  complemento?: string;
  bairro?: string;
  cidade?: string;
  uf?: string;
  
  // Dados Profissionais
  matricula: string;
  cargo: string;
  setor: string;
  dataAdmissao: string;
  salario?: number;
  cargaHoraria: number;
  horarioEntrada: string;
  horarioSaida: string;
  intervaloInicio?: string;
  intervaloFim?: string;
  observacoes?: string;
}

export function FuncionarioWizard() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  const [funcionarioData, setFuncionarioData] = useState<Partial<FuncionarioData>>({});

  const wizard = useWizard({
    steps: WIZARD_STEPS,
    initialStep: 0,
  });

  const updateFuncionarioData = (data: Partial<FuncionarioData>) => {
    setFuncionarioData(prev => ({ ...prev, ...data }));
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      const response = await fetch('/api/funcionarios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(funcionarioData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao cadastrar funcionário');
      }

      const result = await response.json();
      
      // Redirecionar para a lista de funcionários
      router.push('/funcionarios?success=funcionario-cadastrado');
      
    } catch (error) {
      console.error('Erro ao cadastrar funcionário:', error);
      setSubmitError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (wizard.currentStep.id) {
      case 'pessoal':
        return (
          <StepPessoal
            data={funcionarioData}
            onDataChange={updateFuncionarioData}
            onValidationChange={(isValid) => wizard.setStepValid('pessoal', isValid)}
          />
        );
      case 'profissional':
        return (
          <StepProfissional
            data={funcionarioData}
            onDataChange={updateFuncionarioData}
            onValidationChange={(isValid) => wizard.setStepValid('profissional', isValid)}
          />
        );
      case 'confirmacao':
        return (
          <StepConfirmacao
            data={funcionarioData}
            onValidationChange={(isValid) => wizard.setStepValid('confirmacao', isValid)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <ProgressIndicator
        steps={wizard.steps}
        currentStepIndex={wizard.currentStepIndex}
        completedSteps={wizard.steps.slice(0, wizard.currentStepIndex)}
      />

      {/* Error Alert */}
      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">
            <strong>Erro:</strong> {submitError}
          </div>
        </div>
      )}

      {/* Step Content */}
      <div className="min-h-[400px]">
        {renderCurrentStep()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <div>
          {!wizard.isFirstStep && (
            <Button
              variant="outline"
              onClick={wizard.goToPrevious}
              disabled={isSubmitting}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Anterior
            </Button>
          )}
        </div>

        <div className="flex space-x-3">
          {!wizard.isLastStep ? (
            <Button
              onClick={wizard.goToNext}
              disabled={!wizard.canGoNext || isSubmitting}
              variant="primary"
            >
              Próximo
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={!wizard.canGoNext || isSubmitting}
              variant="primary"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cadastrando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Cadastrar Funcionário
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
