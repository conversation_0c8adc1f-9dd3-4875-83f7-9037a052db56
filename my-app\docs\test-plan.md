# 🧪 Plano de Testes - Sistema RLPONTO

## 📋 Visão Geral

Este documento define a estratégia de testes para o Sistema RLPONTO, incluindo tipos de teste, casos de teste principais, ferramentas utilizadas e critérios de aceitação.

## 🎯 Objetivos dos Testes

### Objetivos Principais
- **Qualidade**: Garantir que o sistema funcione conforme especificado
- **Confiabilidade**: Assegurar estabilidade e disponibilidade
- **Segurança**: Validar controles de acesso e proteção de dados
- **Performance**: Verificar tempos de resposta e escalabilidade
- **Usabilidade**: Confirmar experiência do usuário adequada

### Métricas de Qualidade
- **Cobertura de Código**: <PERSON><PERSON><PERSON> 80%
- **Taxa de Defeitos**: Máximo 2 bugs por 1000 linhas de código
- **Tempo de Execução**: Testes unitários < 30s, integração < 5min
- **Automação**: 90% dos testes automatizados

## 🏗️ Estratégia de Testes

### Pirâmide de Testes
```
        /\
       /E2E\      ← 10% - Testes End-to-End
      /______\
     /        \
    /Integration\ ← 20% - Testes de Integração
   \____________/
   \            /
    \   Unit   /  ← 70% - Testes Unitários
     \________/
```

### Tipos de Teste

#### 1. Testes Unitários (70%)
- **Objetivo**: Testar componentes isolados
- **Escopo**: Funções, classes, componentes React
- **Ferramentas**: Jest, React Testing Library
- **Execução**: A cada commit

#### 2. Testes de Integração (20%)
- **Objetivo**: Testar interação entre módulos
- **Escopo**: APIs, banco de dados, serviços externos
- **Ferramentas**: Jest, Supertest, Test Containers
- **Execução**: A cada push

#### 3. Testes End-to-End (10%)
- **Objetivo**: Testar fluxos completos do usuário
- **Escopo**: Jornadas críticas do sistema
- **Ferramentas**: Playwright, Cypress
- **Execução**: Antes de releases

## 🛠️ Ferramentas de Teste

### Stack de Testes
```typescript
// Configuração principal de testes
{
  "unitTests": {
    "framework": "Jest",
    "testingLibrary": "React Testing Library",
    "coverage": "Istanbul",
    "mocking": "Jest Mocks"
  },
  "integrationTests": {
    "framework": "Jest",
    "httpTesting": "Supertest",
    "database": "Test Containers",
    "fixtures": "Factory Bot"
  },
  "e2eTests": {
    "framework": "Playwright",
    "alternative": "Cypress",
    "visualTesting": "Percy",
    "accessibility": "Axe"
  },
  "performance": {
    "loadTesting": "Artillery",
    "monitoring": "Lighthouse CI",
    "profiling": "Clinic.js"
  }
}
```

### Configuração Jest
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/pages/(.*)$': '<rootDir>/src/pages/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/index.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
}

module.exports = createJestConfig(customJestConfig)
```

## 📝 Casos de Teste Principais

### 1. Autenticação e Autorização

#### TC001: Login Válido
```typescript
describe('Authentication', () => {
  test('should login with valid credentials', async () => {
    // Arrange
    const credentials = {
      username: '<EMAIL>',
      password: 'senha123'
    };

    // Act
    const response = await request(app)
      .post('/api/auth/signin')
      .send(credentials);

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('token');
    expect(response.body.user.email).toBe(credentials.username);
  });

  test('should reject invalid credentials', async () => {
    const credentials = {
      username: '<EMAIL>',
      password: 'senhaerrada'
    };

    const response = await request(app)
      .post('/api/auth/signin')
      .send(credentials);

    expect(response.status).toBe(401);
    expect(response.body.error).toBe('Credenciais inválidas');
  });
});
```

#### TC002: Controle de Acesso
```typescript
describe('Authorization', () => {
  test('should allow admin access to user management', async () => {
    const token = await getAuthToken('admin');
    
    const response = await request(app)
      .get('/api/admin/users')
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
  });

  test('should deny user access to admin endpoints', async () => {
    const token = await getAuthToken('user');
    
    const response = await request(app)
      .get('/api/admin/users')
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(403);
  });
});
```

### 2. Gestão de Funcionários

#### TC003: CRUD de Funcionários
```typescript
describe('Employee Management', () => {
  test('should create new employee', async () => {
    const employeeData = {
      nome: 'João Silva',
      cpf: '123.456.789-00',
      email: '<EMAIL>',
      departamento: 'TI',
      cargo: 'Desenvolvedor',
      dataAdmissao: '2024-01-15'
    };

    const response = await request(app)
      .post('/api/funcionarios')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(employeeData);

    expect(response.status).toBe(201);
    expect(response.body.nome).toBe(employeeData.nome);
    expect(response.body.cpf).toBe(employeeData.cpf);
  });

  test('should validate required fields', async () => {
    const invalidData = {
      nome: '', // Campo obrigatório vazio
      cpf: '123', // CPF inválido
    };

    const response = await request(app)
      .post('/api/funcionarios')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(invalidData);

    expect(response.status).toBe(400);
    expect(response.body.errors).toContain('Nome é obrigatório');
    expect(response.body.errors).toContain('CPF inválido');
  });
});
```

### 3. Registro de Ponto

#### TC004: Registro Biométrico
```typescript
describe('Biometric Time Registration', () => {
  test('should register entry with valid biometric', async () => {
    const biometricData = {
      funcionarioId: 1,
      tipo: 'entrada',
      biometricTemplate: 'mock_biometric_template',
      localizacao: {
        latitude: -23.5505,
        longitude: -46.6333
      }
    };

    const response = await request(app)
      .post('/api/ponto/biometrico')
      .send(biometricData);

    expect(response.status).toBe(201);
    expect(response.body.tipo).toBe('entrada');
    expect(response.body.funcionarioId).toBe(1);
  });

  test('should reject invalid biometric template', async () => {
    const invalidData = {
      funcionarioId: 1,
      tipo: 'entrada',
      biometricTemplate: 'invalid_template'
    };

    const response = await request(app)
      .post('/api/ponto/biometrico')
      .send(invalidData);

    expect(response.status).toBe(400);
    expect(response.body.error).toBe('Template biométrico inválido');
  });
});
```

#### TC005: Registro Manual
```typescript
describe('Manual Time Registration', () => {
  test('should register manual entry with justification', async () => {
    const manualData = {
      funcionarioId: 1,
      tipo: 'entrada',
      timestamp: new Date().toISOString(),
      justificativa: 'Problema com leitor biométrico',
      foto: 'base64_encoded_photo'
    };

    const response = await request(app)
      .post('/api/ponto/manual')
      .set('Authorization', `Bearer ${userToken}`)
      .send(manualData);

    expect(response.status).toBe(201);
    expect(response.body.aprovado).toBe(false);
    expect(response.body.justificativa).toBe(manualData.justificativa);
  });
});
```

### 4. Relatórios

#### TC006: Geração de Relatórios
```typescript
describe('Reports Generation', () => {
  test('should generate employee report', async () => {
    const reportParams = {
      funcionarioId: 1,
      dataInicio: '2024-01-01',
      dataFim: '2024-01-31',
      formato: 'pdf'
    };

    const response = await request(app)
      .post('/api/relatorios/funcionario')
      .set('Authorization', `Bearer ${hrToken}`)
      .send(reportParams);

    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toBe('application/pdf');
  });

  test('should validate date range', async () => {
    const invalidParams = {
      funcionarioId: 1,
      dataInicio: '2024-02-01',
      dataFim: '2024-01-31', // Data fim antes da data início
      formato: 'pdf'
    };

    const response = await request(app)
      .post('/api/relatorios/funcionario')
      .set('Authorization', `Bearer ${hrToken}`)
      .send(invalidParams);

    expect(response.status).toBe(400);
    expect(response.body.error).toBe('Data fim deve ser posterior à data início');
  });
});
```

## 🎭 Testes End-to-End

### Configuração Playwright
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### Testes por Módulo do PRD

#### Módulo 1: Autenticação e Segurança
```typescript
describe('Módulo Autenticação', () => {
  test('deve implementar todos os níveis de acesso do PRD', async () => {
    const roles = ['admin', 'hr', 'manager', 'user', 'readonly'];

    for (const role of roles) {
      const user = await criarUsuarioTeste(role);
      const token = await autenticarUsuario(user);

      expect(token).toBeDefined();
      expect(token.role).toBe(role);
    }
  });

  test('deve bloquear após 3 tentativas incorretas', async () => {
    const credentials = { username: '<EMAIL>', password: 'wrong' };

    // 3 tentativas incorretas
    for (let i = 0; i < 3; i++) {
      const response = await request(app)
        .post('/api/auth/signin')
        .send(credentials);
      expect(response.status).toBe(401);
    }

    // 4ª tentativa deve bloquear
    const response = await request(app)
      .post('/api/auth/signin')
      .send(credentials);
    expect(response.status).toBe(429);
    expect(response.body.error).toContain('bloqueado');
  });
});
```

#### Módulo 2: Gestão de Funcionários
```typescript
describe('Módulo Funcionários', () => {
  test('deve completar cadastro em máximo 5 minutos', async () => {
    const inicio = Date.now();

    const funcionario = await criarFuncionarioCompleto({
      dadosPessoais: { nome: 'Test', cpf: '123.456.789-00' },
      dadosProfissionais: { departamento: 'TI', cargo: 'Dev' },
      documentos: ['rg.pdf', 'comprovante.pdf']
    });

    const duracao = Date.now() - inicio;
    const duracaoMinutos = duracao / (1000 * 60);

    expect(duracaoMinutos).toBeLessThan(5);
    expect(funcionario.id).toBeDefined();
  });

  test('deve validar CPF e dados obrigatórios', async () => {
    const dadosInvalidos = {
      nome: '', // Vazio
      cpf: '123', // Inválido
      email: 'email-inválido' // Formato incorreto
    };

    const response = await request(app)
      .post('/api/funcionarios')
      .send(dadosInvalidos);

    expect(response.status).toBe(400);
    expect(response.body.errors).toContain('Nome é obrigatório');
    expect(response.body.errors).toContain('CPF inválido');
    expect(response.body.errors).toContain('Email inválido');
  });
});
```

#### Módulo 3: Registro de Ponto
```typescript
describe('Módulo Registro de Ponto', () => {
  test('registro biométrico deve ocorrer em menos de 3 segundos', async () => {
    const funcionario = await criarFuncionarioTeste();

    const inicio = Date.now();

    const registro = await registrarPontoBiometrico({
      funcionarioId: funcionario.id,
      biometricTemplate: 'template_teste',
      tipo: 'entrada'
    });

    const duracao = Date.now() - inicio;

    expect(duracao).toBeLessThan(3000);
    expect(registro.tipo).toBe('entrada');
    expect(registro.tipoRegistro).toBe('biometrico');
  });

  test('deve validar jornada de trabalho automaticamente', async () => {
    const funcionario = await criarFuncionarioTeste();

    // Registrar entrada fora do horário
    const registro = await registrarPontoManual({
      funcionarioId: funcionario.id,
      tipo: 'entrada',
      timestamp: new Date('2024-01-01T06:00:00'), // 6h da manhã
      justificativa: 'Hora extra autorizada'
    });

    expect(registro.observacoes).toContain('fora do horário');
    expect(registro.aprovado).toBe(false);
  });
});
```

### Fluxos E2E Críticos

#### E2E001: Fluxo Completo de Login
```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test('complete login flow', async ({ page }) => {
  // Navegar para página de login
  await page.goto('/login');

  // Verificar elementos da página
  await expect(page.locator('h1')).toContainText('Login');
  await expect(page.locator('[data-testid="username"]')).toBeVisible();
  await expect(page.locator('[data-testid="password"]')).toBeVisible();

  // Preencher credenciais
  await page.fill('[data-testid="username"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'senha123');

  // Submeter formulário
  await page.click('[data-testid="login-button"]');

  // Verificar redirecionamento
  await expect(page).toHaveURL('/dashboard');
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
});
```

#### E2E002: Cadastro de Funcionário
```typescript
// e2e/employee-registration.spec.ts
test('employee registration flow', async ({ page }) => {
  // Login como admin
  await loginAsAdmin(page);

  // Navegar para cadastro
  await page.goto('/funcionarios/novo');

  // Preencher dados pessoais
  await page.fill('[data-testid="nome"]', 'Maria Santos');
  await page.fill('[data-testid="cpf"]', '987.654.321-00');
  await page.fill('[data-testid="email"]', '<EMAIL>');

  // Avançar para próximo step
  await page.click('[data-testid="next-step"]');

  // Preencher dados profissionais
  await page.selectOption('[data-testid="departamento"]', 'RH');
  await page.selectOption('[data-testid="cargo"]', 'Analista');
  await page.fill('[data-testid="data-admissao"]', '2024-01-15');

  // Submeter formulário
  await page.click('[data-testid="submit-button"]');

  // Verificar sucesso
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  await expect(page).toHaveURL('/funcionarios');
});
```

#### E2E003: Registro de Ponto
```typescript
// e2e/time-registration.spec.ts
test('biometric time registration', async ({ page }) => {
  // Login como funcionário
  await loginAsEmployee(page);

  // Navegar para registro de ponto
  await page.goto('/ponto-biometrico');

  // Simular captura biométrica
  await page.click('[data-testid="capture-biometric"]');
  
  // Aguardar processamento
  await expect(page.locator('[data-testid="processing"]')).toBeVisible();
  await expect(page.locator('[data-testid="processing"]')).toBeHidden();

  // Verificar sucesso
  await expect(page.locator('[data-testid="success-message"]')).toContainText('Ponto registrado com sucesso');
  
  // Verificar histórico atualizado
  await expect(page.locator('[data-testid="last-record"]')).toContainText('Entrada');
});
```

## 🚀 Testes de Performance

### Testes Específicos do PRD
```typescript
// Testes para métricas específicas do PRD
describe('PRD Performance Requirements', () => {
  test('classificação deve ser 95% automática', async () => {
    // Criar 1000 registros de teste
    const registros = await criarRegistrosTeste(1000);

    // Processar classificação automática
    const resultado = await classificarHorasAutomaticamente(registros);

    // Verificar percentual automático
    const automaticos = resultado.filter(r => r.processadoAutomaticamente).length;
    const percentualAutomatico = (automaticos / registros.length) * 100;

    expect(percentualAutomatico).toBeGreaterThanOrEqual(95);
  });

  test('fechamento deve processar 1000 funcionários em menos de 10 minutos', async () => {
    // Criar dados de teste para 1000 funcionários
    const funcionarios = await criarFuncionariosTeste(1000);
    const registros = await criarRegistrosMensaisTeste(funcionarios);

    const inicio = Date.now();

    // Processar fechamento mensal
    await processarFechamentoMensal({
      funcionarios,
      registros,
      ano: 2024,
      mes: 1
    });

    const duracao = Date.now() - inicio;
    const duracaoMinutos = duracao / (1000 * 60);

    expect(duracaoMinutos).toBeLessThan(10);
  });

  test('relatórios devem processar 10.000 registros em menos de 30 segundos', async () => {
    // Criar 10.000 registros de teste
    const registros = await criarRegistrosTeste(10000);

    const inicio = Date.now();

    // Gerar relatório personalizado
    const relatorio = await gerarRelatorioPersonalizado({
      registros,
      formato: 'pdf',
      incluirGraficos: true,
      incluirEstatisticas: true
    });

    const duracao = Date.now() - inicio;
    const duracaoSegundos = duracao / 1000;

    expect(duracaoSegundos).toBeLessThan(30);
    expect(relatorio).toBeDefined();
    expect(relatorio.totalRegistros).toBe(10000);
  });

  test('dashboard deve carregar métricas em menos de 5 segundos', async () => {
    const inicio = Date.now();

    // Carregar todas as métricas do dashboard
    const metricas = await carregarMetricasDashboard({
      periodo: 'ultimo_mes',
      incluirGraficos: true,
      incluirKPIs: true
    });

    const duracao = Date.now() - inicio;
    const duracaoSegundos = duracao / 1000;

    expect(duracaoSegundos).toBeLessThan(5);
    expect(metricas.funcionariosAtivos).toBeDefined();
    expect(metricas.registrosHoje).toBeDefined();
    expect(metricas.horasExtras).toBeDefined();
  });

  test('sistema deve suportar 500 usuários simultâneos', async () => {
    const usuariosSimultaneos = 500;
    const promises = [];

    // Simular 500 usuários fazendo login simultaneamente
    for (let i = 0; i < usuariosSimultaneos; i++) {
      promises.push(
        request(app)
          .post('/api/auth/signin')
          .send({
            username: `user${i}@test.com`,
            password: 'senha123'
          })
      );
    }

    const inicio = Date.now();
    const resultados = await Promise.all(promises);
    const duracao = Date.now() - inicio;

    // Verificar que todos os logins foram bem-sucedidos
    const sucessos = resultados.filter(r => r.status === 200).length;
    const taxaSucesso = (sucessos / usuariosSimultaneos) * 100;

    expect(taxaSucesso).toBeGreaterThanOrEqual(95);
    expect(duracao).toBeLessThan(30000); // 30 segundos
  });
});
```

### Configuração Artillery
```yaml
# artillery.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Load test"
    - duration: 60
      arrivalRate: 100
      name: "Stress test"

scenarios:
  - name: "Login and dashboard"
    flow:
      - post:
          url: "/api/auth/signin"
          json:
            username: "<EMAIL>"
            password: "senha123"
          capture:
            - json: "$.token"
              as: "token"
      - get:
          url: "/api/dashboard"
          headers:
            Authorization: "Bearer {{ token }}"
```

### Métricas de Performance
```typescript
// performance.test.ts
describe('Performance Tests', () => {
  test('API response time should be under 500ms', async () => {
    const start = Date.now();
    
    const response = await request(app)
      .get('/api/funcionarios')
      .set('Authorization', `Bearer ${token}`);
    
    const duration = Date.now() - start;
    
    expect(response.status).toBe(200);
    expect(duration).toBeLessThan(500);
  });

  test('Database query should be optimized', async () => {
    const start = Date.now();
    
    const employees = await prisma.funcionario.findMany({
      include: {
        registrosPonto: {
          take: 10,
          orderBy: { data: 'desc' }
        }
      }
    });
    
    const duration = Date.now() - start;
    
    expect(employees.length).toBeGreaterThan(0);
    expect(duration).toBeLessThan(200);
  });
});
```

## 🔒 Testes de Segurança

### Testes de Autenticação
```typescript
describe('Security Tests', () => {
  test('should prevent SQL injection', async () => {
    const maliciousInput = "'; DROP TABLE funcionarios; --";
    
    const response = await request(app)
      .get(`/api/funcionarios?search=${maliciousInput}`)
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.status).toBe(200);
    // Verificar que a tabela ainda existe
    const count = await prisma.funcionario.count();
    expect(count).toBeGreaterThan(0);
  });

  test('should prevent XSS attacks', async () => {
    const xssPayload = '<script>alert("xss")</script>';
    
    const response = await request(app)
      .post('/api/funcionarios')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        nome: xssPayload,
        cpf: '123.456.789-00',
        email: '<EMAIL>'
      });
    
    expect(response.status).toBe(400);
    expect(response.body.error).toContain('Caracteres inválidos detectados');
  });
});
```

## 📊 Critérios de Aceitação

### Critérios de Qualidade
- **Cobertura de Código**: ≥ 80%
- **Testes Unitários**: ≥ 95% de sucesso
- **Testes de Integração**: 100% de sucesso
- **Testes E2E**: 100% de sucesso nos fluxos críticos
- **Performance**: APIs < 500ms (95th percentile)
- **Segurança**: Zero vulnerabilidades críticas

### Critérios de Release
```typescript
// release-criteria.test.ts
describe('Release Criteria', () => {
  test('all critical user journeys should pass', async () => {
    const criticalTests = [
      'login-flow',
      'employee-registration',
      'time-registration',
      'report-generation'
    ];

    for (const testSuite of criticalTests) {
      const result = await runTestSuite(testSuite);
      expect(result.success).toBe(true);
      expect(result.failureCount).toBe(0);
    }
  });

  test('performance benchmarks should be met', async () => {
    const benchmarks = await runPerformanceTests();
    
    expect(benchmarks.apiResponseTime.p95).toBeLessThan(500);
    expect(benchmarks.pageLoadTime.p95).toBeLessThan(3000);
    expect(benchmarks.databaseQueryTime.p95).toBeLessThan(200);
  });

  test('security scans should pass', async () => {
    const securityScan = await runSecurityScan();
    
    expect(securityScan.criticalVulnerabilities).toBe(0);
    expect(securityScan.highVulnerabilities).toBeLessThanOrEqual(2);
  });
});
```

## 📋 Execução dos Testes

### Scripts de Teste
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "playwright test",
    "test:performance": "artillery run artillery.yml",
    "test:security": "npm audit && snyk test",
    "test:all": "npm run test && npm run test:integration && npm run test:e2e"
  }
}
```

### Pipeline de CI/CD
```yaml
# .github/workflows/test.yml
name: Test Pipeline

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:coverage
      - uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: rlponto_test
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e
```

---

**Documento criado em**: [Data]  
**Última atualização**: [Data]  
**Versão**: 1.0  
**Responsável**: [Nome do QA Lead]
