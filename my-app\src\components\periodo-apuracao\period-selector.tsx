'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui';
import { Calendar, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';

interface PeriodSelectorProps {
  currentPeriod: {
    ano: number;
    mes: number;
  };
}

export function PeriodSelector({ currentPeriod }: PeriodSelectorProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isOpen, setIsOpen] = useState(false);

  const meses = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', '<PERSON><PERSON>', '<PERSON>ho',
    'Julho', 'Agosto', 'Set<PERSON><PERSON>', 'Out<PERSON>ro', 'Novembro', 'Dezembro'
  ];

  const anos = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i);

  const handlePeriodChange = (ano: number, mes: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('ano', ano.toString());
    params.set('mes', mes.toString());
    router.push(`/periodo-apuracao?${params.toString()}`);
    setIsOpen(false);
  };

  const navigatePeriod = (direction: 'prev' | 'next') => {
    let novoAno = currentPeriod.ano;
    let novoMes = currentPeriod.mes;

    if (direction === 'prev') {
      novoMes--;
      if (novoMes < 1) {
        novoMes = 12;
        novoAno--;
      }
    } else {
      novoMes++;
      if (novoMes > 12) {
        novoMes = 1;
        novoAno++;
      }
    }

    handlePeriodChange(novoAno, novoMes);
  };

  const isCurrentMonth = () => {
    const hoje = new Date();
    return currentPeriod.ano === hoje.getFullYear() && 
           currentPeriod.mes === hoje.getMonth() + 1;
  };

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Navegação Rápida */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigatePeriod('prev')}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Seletor Principal */}
        <div className="relative">
          <Button
            variant="outline"
            onClick={() => setIsOpen(!isOpen)}
            className="min-w-[180px] justify-between"
          >
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>
                {meses[currentPeriod.mes - 1]} {currentPeriod.ano}
              </span>
              {isCurrentMonth() && (
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Atual
                </span>
              )}
            </div>
            <ChevronDown className="h-4 w-4" />
          </Button>

          {/* Dropdown */}
          {isOpen && (
            <div className="absolute top-full mt-2 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[300px]">
              <div className="p-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Seleção de Ano */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Ano
                    </label>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {anos.map((ano) => (
                        <button
                          key={ano}
                          onClick={() => handlePeriodChange(ano, currentPeriod.mes)}
                          className={`w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                            ano === currentPeriod.ano
                              ? 'bg-blue-100 text-blue-900 font-medium'
                              : 'hover:bg-gray-100'
                          }`}
                        >
                          {ano}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Seleção de Mês */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mês
                    </label>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {meses.map((mes, index) => {
                        const mesNumero = index + 1;
                        const isAtual = new Date().getFullYear() === currentPeriod.ano && 
                                       new Date().getMonth() + 1 === mesNumero;
                        
                        return (
                          <button
                            key={mesNumero}
                            onClick={() => handlePeriodChange(currentPeriod.ano, mesNumero)}
                            className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center justify-between ${
                              mesNumero === currentPeriod.mes
                                ? 'bg-blue-100 text-blue-900 font-medium'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <span>{mes}</span>
                            {isAtual && (
                              <span className="text-xs bg-green-100 text-green-800 px-1 rounded">
                                Atual
                              </span>
                            )}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Ações Rápidas */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const hoje = new Date();
                        handlePeriodChange(hoje.getFullYear(), hoje.getMonth() + 1);
                      }}
                      className="flex-1"
                    >
                      Mês Atual
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const mesPassado = new Date();
                        mesPassado.setMonth(mesPassado.getMonth() - 1);
                        handlePeriodChange(mesPassado.getFullYear(), mesPassado.getMonth() + 1);
                      }}
                      className="flex-1"
                    >
                      Mês Anterior
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => navigatePeriod('next')}
          disabled={isCurrentMonth()}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Overlay para fechar dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
