# 👥 MÓDULO FUNCIONÁRIOS - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável pela gestão completa de funcionários no sistema RLPONTO-WEB.

## 🎯 Funcionalidades
- CRUD completo de funcionários
- Upload e gestão de fotos
- Validação avançada (CPF, email, telefone)
- Cadastro de biometria
- Histórico de alterações
- Gestão de EPIs
- Relatórios individuais

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── funcionarios/
│           ├── page.tsx                    # Lista de funcionários
│           ├── novo/
│           │   └── page.tsx                # Cadastro de funcionário
│           ├── [id]/
│           │   ├── page.tsx                # Detalhes do funcionário
│           │   ├── editar/
│           │   │   └── page.tsx            # Edição de funcionário
│           │   └── biometria/
│           │       └── page.tsx            # Cadastro de biometria
│           └── desligados/
│               └── page.tsx                # Funcionários desligados
├── components/
│   └── funcionarios/
│       ├── funcionarios-list.tsx          # Lista de funcionários
│       ├── funcionario-form.tsx           # Formulário de funcionário
│       ├── funcionario-card.tsx           # Card de funcionário
│       ├── funcionarios-filters.tsx       # Filtros de busca
│       ├── foto-upload.tsx                # Upload de foto
│       └── biometria-cadastro.tsx         # Cadastro de biometria
└── api/
    └── funcionarios/
        ├── route.ts                        # GET/POST funcionários
        ├── [id]/
        │   ├── route.ts                    # GET/PUT/DELETE funcionário
        │   ├── foto/
        │   │   └── route.ts                # Upload/GET foto
        │   └── biometria/
        │       └── route.ts                # Cadastro biometria
        └── search/
            └── route.ts                    # Busca de funcionários
```

## 🔧 Implementação Técnica

### 📋 Lista de Funcionários (page.tsx)
```typescript
// app/(dashboard)/funcionarios/page.tsx
import { Suspense } from 'react';
import { Metadata } from 'next/metadata';
import { FuncionariosList } from '@/components/funcionarios/funcionarios-list';
import { FuncionariosFilters } from '@/components/funcionarios/funcionarios-filters';
import { Button } from '@/components/ui/button';
import { Plus, Users } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Funcionários - RLPONTO',
  description: 'Gestão de funcionários do sistema',
};

interface FuncionariosPageProps {
  searchParams: {
    search?: string;
    setor?: string;
    status?: string;
    page?: string;
  };
}

export default function FuncionariosPage({ searchParams }: FuncionariosPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Funcionários</h1>
            <p className="text-gray-600">Gerencie os funcionários da empresa</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href="/funcionarios/desligados">
              Desligados
            </Link>
          </Button>
          <Button asChild>
            <Link href="/funcionarios/novo">
              <Plus className="mr-2 h-4 w-4" />
              Novo Funcionário
            </Link>
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <FuncionariosFilters />

      {/* Lista */}
      <Suspense fallback={<FuncionariosListSkeleton />}>
        <FuncionariosList searchParams={searchParams} />
      </Suspense>
    </div>
  );
}

function FuncionariosListSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-20 bg-gray-200 rounded-lg animate-pulse" />
      ))}
    </div>
  );
}
```

### 👤 Formulário de Funcionário (funcionario-form.tsx)
```typescript
// components/funcionarios/funcionario-form.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FotoUpload } from './foto-upload';
import { cpfValidator, emailValidator, telefoneValidator } from '@/lib/validators';

const funcionarioSchema = z.object({
  nomeCompleto: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  cpf: z.string().refine(cpfValidator, 'CPF inválido'),
  matriculaEmpresa: z.string().optional(),
  setor: z.string().min(1, 'Setor é obrigatório'),
  cargo: z.string().min(1, 'Cargo é obrigatório'),
  empresa: z.string().min(1, 'Empresa é obrigatória'),
  dataNascimento: z.string().refine((date) => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 16;
  }, 'Funcionário deve ter pelo menos 16 anos'),
  telefone: z.string().refine(telefoneValidator, 'Telefone inválido').optional(),
  email: z.string().refine(emailValidator, 'Email inválido').optional(),
  enderecoCompleto: z.string().optional(),
});

type FuncionarioFormData = z.infer<typeof funcionarioSchema>;

interface FuncionarioFormProps {
  funcionario?: Partial<FuncionarioFormData>;
  isEditing?: boolean;
}

export function FuncionarioForm({ funcionario, isEditing = false }: FuncionarioFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [fotoUrl, setFotoUrl] = useState<string | null>(funcionario?.fotoUrl || null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FuncionarioFormData>({
    resolver: zodResolver(funcionarioSchema),
    defaultValues: funcionario,
  });

  const onSubmit = async (data: FuncionarioFormData) => {
    setIsLoading(true);

    try {
      const payload = {
        ...data,
        fotoUrl,
      };

      const url = isEditing 
        ? `/api/funcionarios/${funcionario?.id}`
        : '/api/funcionarios';
      
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar funcionário');
      }

      router.push('/funcionarios');
      router.refresh();
    } catch (error) {
      console.error('Erro:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Foto */}
      <Card>
        <CardHeader>
          <CardTitle>Foto do Funcionário</CardTitle>
        </CardHeader>
        <CardContent>
          <FotoUpload
            currentFoto={fotoUrl}
            onFotoChange={setFotoUrl}
          />
        </CardContent>
      </Card>

      {/* Dados Pessoais */}
      <Card>
        <CardHeader>
          <CardTitle>Dados Pessoais</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <Label htmlFor="nomeCompleto">Nome Completo *</Label>
            <Input
              id="nomeCompleto"
              {...register('nomeCompleto')}
              placeholder="Digite o nome completo"
            />
            {errors.nomeCompleto && (
              <p className="text-sm text-red-600 mt-1">{errors.nomeCompleto.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="cpf">CPF *</Label>
            <Input
              id="cpf"
              {...register('cpf')}
              placeholder="000.000.000-00"
              maxLength={14}
            />
            {errors.cpf && (
              <p className="text-sm text-red-600 mt-1">{errors.cpf.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="dataNascimento">Data de Nascimento *</Label>
            <Input
              id="dataNascimento"
              type="date"
              {...register('dataNascimento')}
            />
            {errors.dataNascimento && (
              <p className="text-sm text-red-600 mt-1">{errors.dataNascimento.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="telefone">Telefone</Label>
            <Input
              id="telefone"
              {...register('telefone')}
              placeholder="(11) 99999-9999"
            />
            {errors.telefone && (
              <p className="text-sm text-red-600 mt-1">{errors.telefone.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="enderecoCompleto">Endereço Completo</Label>
            <Input
              id="enderecoCompleto"
              {...register('enderecoCompleto')}
              placeholder="Rua, número, bairro, cidade - UF"
            />
          </div>
        </CardContent>
      </Card>

      {/* Dados Profissionais */}
      <Card>
        <CardHeader>
          <CardTitle>Dados Profissionais</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="matriculaEmpresa">Matrícula</Label>
            <Input
              id="matriculaEmpresa"
              {...register('matriculaEmpresa')}
              placeholder="Matrícula da empresa"
            />
          </div>

          <div>
            <Label htmlFor="empresa">Empresa *</Label>
            <Select onValueChange={(value) => setValue('empresa', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione a empresa" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="empresa1">Empresa Principal</SelectItem>
                <SelectItem value="empresa2">Empresa Cliente A</SelectItem>
                <SelectItem value="empresa3">Empresa Cliente B</SelectItem>
              </SelectContent>
            </Select>
            {errors.empresa && (
              <p className="text-sm text-red-600 mt-1">{errors.empresa.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="setor">Setor *</Label>
            <Select onValueChange={(value) => setValue('setor', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o setor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ti">Tecnologia da Informação</SelectItem>
                <SelectItem value="rh">Recursos Humanos</SelectItem>
                <SelectItem value="financeiro">Financeiro</SelectItem>
                <SelectItem value="operacional">Operacional</SelectItem>
                <SelectItem value="administrativo">Administrativo</SelectItem>
              </SelectContent>
            </Select>
            {errors.setor && (
              <p className="text-sm text-red-600 mt-1">{errors.setor.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="cargo">Cargo *</Label>
            <Input
              id="cargo"
              {...register('cargo')}
              placeholder="Digite o cargo"
            />
            {errors.cargo && (
              <p className="text-sm text-red-600 mt-1">{errors.cargo.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Botões */}
      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Salvando...' : isEditing ? 'Atualizar' : 'Cadastrar'}
        </Button>
      </div>
    </form>
  );
}
```

## 📊 API Routes

### 🔌 API de Funcionários (route.ts)
```typescript
// app/api/funcionarios/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { funcionarioSchema } from '@/lib/validations/funcionario';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const setor = searchParams.get('setor');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const where = {
      ativo: status !== 'desligados',
      ...(search && {
        OR: [
          { nomeCompleto: { contains: search } },
          { cpf: { contains: search } },
          { matriculaEmpresa: { contains: search } },
        ],
      }),
      ...(setor && { setor }),
    };

    const [funcionarios, total] = await Promise.all([
      prisma.funcionario.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { nomeCompleto: 'asc' },
        include: {
          _count: {
            select: {
              registrosPonto: true,
              biometrias: true,
            },
          },
        },
      }),
      prisma.funcionario.count({ where }),
    ]);

    return NextResponse.json({
      funcionarios,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Erro ao buscar funcionários:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role === 'readonly') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = funcionarioSchema.parse(body);

    // Verificar se CPF já existe
    const existingFuncionario = await prisma.funcionario.findUnique({
      where: { cpf: validatedData.cpf },
    });

    if (existingFuncionario) {
      return NextResponse.json(
        { error: 'CPF já cadastrado' },
        { status: 400 }
      );
    }

    const funcionario = await prisma.funcionario.create({
      data: validatedData,
    });

    return NextResponse.json(funcionario, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar funcionário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Schema do Banco de Dados

### 📋 Modelo Prisma
```prisma
model Funcionario {
  id              Int      @id @default(autoincrement())
  nomeCompleto    String   @map("nome_completo")
  cpf             String   @unique
  matriculaEmpresa String? @map("matricula_empresa")
  setor           String
  cargo           String
  empresa         String
  dataNascimento  DateTime @map("data_nascimento")
  telefone        String?
  email           String?
  enderecoCompleto String? @map("endereco_completo")
  fotoUrl         String?  @map("foto_url")
  ativo           Boolean  @default(true)
  dataCadastro    DateTime @default(now()) @map("data_cadastro")
  dataAtualizacao DateTime @updatedAt @map("data_atualizacao")

  // Relacionamentos
  registrosPonto  RegistroPonto[]
  biometrias      BiometriaFuncionario[]
  alocacoes       FuncionarioAlocacao[]

  @@map("funcionarios")
  @@index([cpf])
  @@index([nomeCompleto])
  @@index([setor])
  @@index([ativo])
}
```

## 🧪 Testes

### ✅ Teste do Formulário
```typescript
// __tests__/funcionarios/funcionario-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FuncionarioForm } from '@/components/funcionarios/funcionario-form';

describe('FuncionarioForm', () => {
  it('should validate required fields', async () => {
    render(<FuncionarioForm />);

    fireEvent.click(screen.getByRole('button', { name: /cadastrar/i }));

    await waitFor(() => {
      expect(screen.getByText(/nome deve ter pelo menos 2 caracteres/i)).toBeInTheDocument();
      expect(screen.getByText(/cpf inválido/i)).toBeInTheDocument();
      expect(screen.getByText(/setor é obrigatório/i)).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const mockFetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ id: 1 }),
    });
    global.fetch = mockFetch;

    render(<FuncionarioForm />);

    fireEvent.change(screen.getByLabelText(/nome completo/i), {
      target: { value: 'João Silva' },
    });
    fireEvent.change(screen.getByLabelText(/cpf/i), {
      target: { value: '123.456.789-09' },
    });

    fireEvent.click(screen.getByRole('button', { name: /cadastrar/i }));

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/funcionarios', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: expect.stringContaining('João Silva'),
      });
    });
  });
});
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Lista de funcionários com paginação
- [ ] Formulário de cadastro com validação
- [ ] Edição de funcionários
- [ ] Upload de fotos
- [ ] Busca e filtros avançados
- [ ] Exclusão lógica (desligamento)
- [ ] Cadastro de biometria
- [ ] Histórico de alterações
- [ ] Exportação de dados
- [ ] Relatórios individuais

### 🔧 Validações
- [ ] Validação de CPF
- [ ] Validação de email
- [ ] Validação de telefone
- [ ] Verificação de idade mínima
- [ ] Unicidade de CPF
- [ ] Validação de campos obrigatórios

## 🚀 Próximos Passos
1. **Novo Funcionário** - Página de cadastro detalhada
2. **Módulo Ponto** - Integração com registro de ponto
3. **Módulo Relatórios** - Relatórios específicos de funcionários
