'use client';

import { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Eye, 
  Clock, 
  CheckCircle,
  AlertCircle,
  MoreVertical,
  Calendar,
  User
} from 'lucide-react';
import { Button } from '@/components/ui';

interface RecentReport {
  id: string;
  title: string;
  type: string;
  status: 'completed' | 'processing' | 'failed';
  createdAt: Date;
  createdBy: string;
  fileSize?: string;
  downloadUrl?: string;
  parameters: {
    periodo?: string;
    funcionario?: string;
    formato: 'PDF' | 'Excel' | 'CSV';
  };
}

export function RecentReports() {
  const [reports, setReports] = useState<RecentReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    fetchRecentReports();
  }, []);

  const fetchRecentReports = async () => {
    try {
      setLoading(true);

      // Usar datas fixas para evitar problemas de hidratação
      const baseDate = new Date('2024-01-27T10:00:00');

      // Simulação de dados
      const mockReports: RecentReport[] = [
        {
          id: '1',
          title: 'Relatório Individual - João Silva',
          type: 'Individual',
          status: 'completed',
          createdAt: new Date(baseDate.getTime() - 1000 * 60 * 30), // 30 min atrás
          createdBy: 'Admin',
          fileSize: '2.3 MB',
          downloadUrl: '/downloads/relatorio-joao-silva.pdf',
          parameters: {
            funcionario: 'João Silva Santos',
            periodo: 'Janeiro 2024',
            formato: 'PDF'
          }
        },
        {
          id: '2',
          title: 'Relatório Mensal - Janeiro 2024',
          type: 'Período',
          status: 'completed',
          createdAt: new Date(baseDate.getTime() - 1000 * 60 * 60 * 2), // 2h atrás
          createdBy: 'RH Manager',
          fileSize: '5.7 MB',
          downloadUrl: '/downloads/relatorio-mensal-jan2024.xlsx',
          parameters: {
            periodo: 'Janeiro 2024',
            formato: 'Excel'
          }
        },
        {
          id: '3',
          title: 'Análise de Frequência',
          type: 'Analítico',
          status: 'processing',
          createdAt: new Date(baseDate.getTime() - 1000 * 60 * 10), // 10 min atrás
          createdBy: 'Admin',
          parameters: {
            periodo: 'Janeiro 2024',
            formato: 'PDF'
          }
        },
        {
          id: '4',
          title: 'Relatório de Horas Extras',
          type: 'Analítico',
          status: 'completed',
          createdAt: new Date(baseDate.getTime() - 1000 * 60 * 60 * 6), // 6h atrás
          createdBy: 'Supervisor',
          fileSize: '1.8 MB',
          downloadUrl: '/downloads/horas-extras-jan2024.pdf',
          parameters: {
            periodo: 'Janeiro 2024',
            formato: 'PDF'
          }
        },
        {
          id: '5',
          title: 'Relatório Individual - Maria Costa',
          type: 'Individual',
          status: 'failed',
          createdAt: new Date(baseDate.getTime() - 1000 * 60 * 60 * 12), // 12h atrás
          createdBy: 'Admin',
          parameters: {
            funcionario: 'Maria Oliveira Costa',
            periodo: 'Janeiro 2024',
            formato: 'Excel'
          }
        }
      ];

      await new Promise(resolve => setTimeout(resolve, 800));
      setReports(mockReports);
    } catch (error) {
      console.error('Erro ao buscar relatórios recentes:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-600 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'processing':
        return 'Processando';
      case 'failed':
        return 'Falhou';
      default:
        return 'Desconhecido';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimeAgo = (date: Date) => {
    if (!mounted) return '';
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m atrás`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h atrás`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d atrás`;
    }
  };

  const handleDownload = (report: RecentReport) => {
    if (report.downloadUrl) {
      // Simular download
      console.log(`Downloading: ${report.downloadUrl}`);
      // window.open(report.downloadUrl, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3 border rounded animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded" />
            <div className="flex-1">
              <div className="w-3/4 h-4 bg-gray-200 rounded mb-2" />
              <div className="w-1/2 h-3 bg-gray-200 rounded" />
            </div>
            <div className="w-16 h-6 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {reports.length === 0 ? (
        <div className="text-center py-8">
          <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Nenhum relatório recente</p>
        </div>
      ) : (
        reports.map((report) => (
          <div 
            key={report.id} 
            className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-start space-x-3">
              {/* Ícone do tipo */}
              <div className="flex-shrink-0 mt-1">
                <FileText className="h-5 w-5 text-gray-600" />
              </div>

              {/* Conteúdo principal */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {report.title}
                  </h4>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(report.status)}
                  </div>
                </div>

                <div className="flex items-center space-x-2 mb-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(report.status)}`}>
                    {getStatusLabel(report.status)}
                  </span>
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    {report.type}
                  </span>
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">
                    {report.parameters.formato}
                  </span>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      <User className="h-3 w-3" />
                      <span>{report.createdBy}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatTimeAgo(report.createdAt)}</span>
                    </div>
                    {report.fileSize && (
                      <span>{report.fileSize}</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Ações */}
              <div className="flex items-center space-x-1">
                {report.status === 'completed' && report.downloadUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(report)}
                    className="h-8 w-8 p-0"
                  >
                    <Download className="h-3 w-3" />
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <Eye className="h-3 w-3" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        ))
      )}

      {/* Link para ver mais */}
      {reports.length > 0 && (
        <div className="text-center pt-3 border-t border-gray-200">
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            Ver todos os relatórios
          </button>
        </div>
      )}
    </div>
  );
}

