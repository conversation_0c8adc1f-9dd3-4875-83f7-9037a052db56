'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { clsx } from 'clsx';
import {
  Users,
  Clock,
  Calendar,
  BarChart3,
  Settings,
  Home,
  UserPlus,
  FileText,
  Hand,
  TrendingUp
} from 'lucide-react';

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    description: 'Visão geral do sistema'
  },
  {
    name: 'Funcionários',
    href: '/funcionarios',
    icon: Users,
    description: 'Gestão de funcionários',
    children: [
      {
        name: 'Lista de Funcionários',
        href: '/funcionarios',
        icon: Users
      },
      {
        name: 'Novo Funcionário',
        href: '/funcionarios/novo',
        icon: UserPlus
      }
    ]
  },
  {
    name: '<PERSON><PERSON>',
    href: '/ponto/biometrico',
    icon: Clock,
    description: 'Registro de ponto',
    children: [
      {
        name: '<PERSON><PERSON> Biométrico',
        href: '/ponto/biometrico',
        icon: Clock
      },
      {
        name: '<PERSON><PERSON>',
        href: '/ponto/manual',
        icon: Hand
      }
    ]
  },
  {
    name: 'Per<PERSON><PERSON> de Apuração',
    href: '/periodo-apuracao',
    icon: BarChart3,
    description: 'Análise mensal'
  },
  {
    name: 'Relatórios',
    href: '/relatorios',
    icon: FileText,
    description: 'Relatórios e análises',
    children: [
      {
        name: 'Relatório Individual',
        href: '/relatorios/funcionario',
        icon: FileText
      },
      {
        name: 'Relatório por Período',
        href: '/relatorios/periodo',
        icon: FileText
      },
      {
        name: 'Relatórios Analíticos',
        href: '/relatorios/analiticos',
        icon: FileText
      }
    ]
  },
  {
    name: 'Estatísticas',
    href: '/estatisticas',
    icon: TrendingUp,
    description: 'Análises e KPIs',
    children: [
      {
        name: 'Produtividade',
        href: '/estatisticas/produtividade',
        icon: TrendingUp
      },
      {
        name: 'Absenteísmo',
        href: '/estatisticas/absenteismo',
        icon: TrendingUp
      },
      {
        name: 'Tendências',
        href: '/estatisticas/tendencias',
        icon: TrendingUp
      },
      {
        name: 'Comparativos',
        href: '/estatisticas/comparativos',
        icon: TrendingUp
      }
    ]
  },
  {
    name: 'Administração',
    href: '/administracao',
    icon: Settings,
    description: 'Configurações do sistema'
  }
];

export function DashboardNav() {
  const pathname = usePathname();

  return (
    <nav className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
      <div className="p-6">
        <div className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
            
            return (
              <div key={item.name}>
                <Link
                  href={item.href}
                  className={clsx(
                    'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  )}
                >
                  <item.icon
                    className={clsx(
                      'mr-3 h-5 w-5',
                      isActive ? 'text-blue-700' : 'text-gray-400'
                    )}
                  />
                  {item.name}
                </Link>
                
                {/* Submenu */}
                {item.children && isActive && (
                  <div className="ml-8 mt-1 space-y-1">
                    {item.children.map((child) => {
                      const isChildActive = pathname === child.href;
                      
                      return (
                        <Link
                          key={child.name}
                          href={child.href}
                          className={clsx(
                            'flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors',
                            isChildActive
                              ? 'bg-blue-100 text-blue-800'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                          )}
                        >
                          <child.icon className="mr-2 h-3 w-3" />
                          {child.name}
                        </Link>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </nav>
  );
}

