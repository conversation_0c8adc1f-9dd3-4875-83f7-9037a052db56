'use client';

import { useState, useCallback } from 'react';

export interface WizardStep {
  id: string;
  title: string;
  description: string;
  isValid?: boolean;
  isCompleted?: boolean;
}

export interface UseWizardProps {
  steps: WizardStep[];
  initialStep?: number;
}

export function useWizard({ steps, initialStep = 0 }: UseWizardProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(initialStep);
  const [stepValidation, setStepValidation] = useState<Record<string, boolean>>({});
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const currentStep = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  const goToNext = useCallback(() => {
    if (!isLastStep) {
      setCurrentStepIndex(prev => prev + 1);
    }
  }, [isLastStep]);

  const goToPrevious = useCallback(() => {
    if (!isFirstStep) {
      setCurrentStepIndex(prev => prev - 1);
    }
  }, [isFirstStep]);

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStepIndex(stepIndex);
    }
  }, [steps.length]);

  const setStepValid = useCallback((stepId: string, isValid: boolean) => {
    setStepValidation(prev => ({
      ...prev,
      [stepId]: isValid
    }));
  }, []);

  const markStepCompleted = useCallback((stepId: string) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));
  }, []);

  const isStepValid = useCallback((stepId: string) => {
    return stepValidation[stepId] ?? false;
  }, [stepValidation]);

  const isStepCompleted = useCallback((stepId: string) => {
    return completedSteps.has(stepId);
  }, [completedSteps]);

  const canGoNext = isStepValid(currentStep?.id);
  const canGoPrevious = !isFirstStep;

  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  return {
    currentStep,
    currentStepIndex,
    steps,
    isFirstStep,
    isLastStep,
    canGoNext,
    canGoPrevious,
    progress,
    goToNext,
    goToPrevious,
    goToStep,
    setStepValid,
    markStepCompleted,
    isStepValid,
    isStepCompleted,
  };
}
