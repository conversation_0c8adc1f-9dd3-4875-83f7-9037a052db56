# ⏰ MÓDULO CLASSIFICAR HORAS - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável pela classificação e categorização das horas trabalhadas pelos funcionários, incluindo horas normais, extras, faltas, atrasos e justificativas.

## 🎯 Funcionalidades
- Classificação automática de horas
- Categorização de registros (normal, extra, falta, atraso)
- Cálculo de horas extras (50%, 100%)
- Gestão de justificativas e abonos
- Aprovação de supervisores
- Relatórios de classificação
- Exportação de dados
- Histórico de alterações

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── classificar-horas/
│           ├── page.tsx                    # Dashboard de classificação
│           ├── funcionario/
│           │   └── [id]/
│           │       └── page.tsx            # Classificação por funcionário
│           ├── periodo/
│           │   └── [periodo]/
│           │       └── page.tsx            # Classificação por período
│           └── components/
│               ├── classification-table.tsx # Tabela de classificação
│               ├── hour-calculator.tsx     # Calculadora de horas
│               ├── justification-modal.tsx # Modal de justificativas
│               └── approval-panel.tsx      # Painel de aprovação
├── components/
│   └── classificacao/
│       ├── hour-type-badge.tsx            # Badge tipo de hora
│       ├── classification-form.tsx        # Formulário classificação
│       ├── bulk-actions.tsx               # Ações em lote
│       └── export-options.tsx             # Opções de exportação
└── api/
    └── classificar-horas/
        ├── route.ts                       # API principal
        ├── funcionario/
        │   └── [id]/
        │       └── route.ts               # Por funcionário
        ├── periodo/
        │   └── [periodo]/
        │       └── route.ts               # Por período
        ├── aprovar/
        │   └── route.ts                   # Aprovação
        └── export/
            └── route.ts                   # Exportação
```

## 🔧 Implementação Técnica

### ⏰ Dashboard de Classificação (page.tsx)
```typescript
// app/(dashboard)/classificar-horas/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { ClassificationTable } from './components/classification-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, Filter, Download, CheckCircle, AlertCircle, Calendar } from 'lucide-react';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';

export const metadata: Metadata = {
  title: 'Classificar Horas - RLPONTO',
  description: 'Classificação e categorização de horas trabalhadas',
};

interface ClassificarHorasPageProps {
  searchParams: {
    funcionario?: string;
    periodo?: string;
    status?: string;
    tipo?: string;
    page?: string;
  };
}

export default function ClassificarHorasPage({ searchParams }: ClassificarHorasPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Clock className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Classificar Horas</h1>
            <p className="text-gray-600">Classifique e categorize as horas trabalhadas</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button>
            <CheckCircle className="h-4 w-4 mr-2" />
            Aprovar Selecionados
          </Button>
        </div>
      </div>

      {/* Métricas Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">127</div>
            <p className="text-xs text-muted-foreground">registros para classificar</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aprovados</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">1,234</div>
            <p className="text-xs text-muted-foreground">registros aprovados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Horas Extras</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">89.5h</div>
            <p className="text-xs text-muted-foreground">no período atual</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faltas</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">12</div>
            <p className="text-xs text-muted-foreground">faltas registradas</p>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Funcionário</label>
              <Input placeholder="Buscar funcionário..." />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Período</label>
              <DatePickerWithRange />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select defaultValue={searchParams.status}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pendente">Pendente</SelectItem>
                  <SelectItem value="aprovado">Aprovado</SelectItem>
                  <SelectItem value="rejeitado">Rejeitado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Tipo</label>
              <Select defaultValue={searchParams.tipo}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="extra_50">Extra 50%</SelectItem>
                  <SelectItem value="extra_100">Extra 100%</SelectItem>
                  <SelectItem value="falta">Falta</SelectItem>
                  <SelectItem value="atraso">Atraso</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button className="w-full">
                <Filter className="h-4 w-4 mr-2" />
                Filtrar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Classificação */}
      <Card>
        <CardContent className="p-0">
          <Suspense fallback={<ClassificationTableSkeleton />}>
            <ClassificationTable searchParams={searchParams} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function ClassificationTableSkeleton() {
  return (
    <div className="p-6 space-y-4">
      {[...Array(10)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}
```

### 📊 Tabela de Classificação (classification-table.tsx)
```typescript
// app/(dashboard)/classificar-horas/components/classification-table.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Check, X, Edit, MessageSquare, Clock } from 'lucide-react';
import { formatDuration, format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { JustificationModal } from './justification-modal';

interface RegistroClassificacao {
  id: number;
  funcionario: {
    id: number;
    nome: string;
    matricula: string;
  };
  data: string;
  entrada: string;
  saida: string;
  horasTrabalhadas: number;
  tipoHora: 'normal' | 'extra_50' | 'extra_100' | 'falta' | 'atraso';
  status: 'pendente' | 'aprovado' | 'rejeitado';
  justificativa?: string;
  aprovadoPor?: string;
  criadoEm: string;
}

interface ClassificationTableProps {
  searchParams: {
    funcionario?: string;
    periodo?: string;
    status?: string;
    tipo?: string;
    page?: string;
  };
}

export function ClassificationTable({ searchParams }: ClassificationTableProps) {
  const [registros, setRegistros] = useState<RegistroClassificacao[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showJustificationModal, setShowJustificationModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<RegistroClassificacao | null>(null);

  useEffect(() => {
    fetchRegistros();
  }, [searchParams]);

  const fetchRegistros = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await fetch(`/api/classificar-horas?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setRegistros(data.registros);
      }
    } catch (error) {
      console.error('Erro ao buscar registros:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getHourTypeBadge = (tipo: string) => {
    const typeConfig = {
      normal: { label: 'Normal', variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
      extra_50: { label: 'Extra 50%', variant: 'secondary' as const, color: 'bg-orange-100 text-orange-800' },
      extra_100: { label: 'Extra 100%', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      falta: { label: 'Falta', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      atraso: { label: 'Atraso', variant: 'outline' as const, color: 'bg-yellow-100 text-yellow-800' },
    };

    const config = typeConfig[tipo as keyof typeof typeConfig] || typeConfig.normal;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pendente: { label: 'Pendente', color: 'bg-yellow-100 text-yellow-800' },
      aprovado: { label: 'Aprovado', color: 'bg-green-100 text-green-800' },
      rejeitado: { label: 'Rejeitado', color: 'bg-red-100 text-red-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pendente;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(registros.map(r => r.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectRecord = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedIds([...selectedIds, id]);
    } else {
      setSelectedIds(selectedIds.filter(selectedId => selectedId !== id));
    }
  };

  const handleApprove = async (ids: number[]) => {
    try {
      const response = await fetch('/api/classificar-horas/aprovar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids, acao: 'aprovar' }),
      });

      if (response.ok) {
        fetchRegistros();
        setSelectedIds([]);
      }
    } catch (error) {
      console.error('Erro ao aprovar registros:', error);
    }
  };

  const handleReject = async (ids: number[]) => {
    try {
      const response = await fetch('/api/classificar-horas/aprovar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids, acao: 'rejeitar' }),
      });

      if (response.ok) {
        fetchRegistros();
        setSelectedIds([]);
      }
    } catch (error) {
      console.error('Erro ao rejeitar registros:', error);
    }
  };

  const formatHours = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h${mins.toString().padStart(2, '0')}m`;
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="overflow-x-auto">
        {/* Ações em Lote */}
        {selectedIds.length > 0 && (
          <div className="bg-blue-50 border-b p-4 flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedIds.length} registro(s) selecionado(s)
            </span>
            <div className="flex space-x-2">
              <Button size="sm" onClick={() => handleApprove(selectedIds)}>
                <Check className="h-4 w-4 mr-1" />
                Aprovar
              </Button>
              <Button size="sm" variant="destructive" onClick={() => handleReject(selectedIds)}>
                <X className="h-4 w-4 mr-1" />
                Rejeitar
              </Button>
            </div>
          </div>
        )}

        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="px-6 py-3 text-left">
                <Checkbox
                  checked={selectedIds.length === registros.length && registros.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Funcionário
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Data
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Horários
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Horas
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Tipo
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Ações
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {registros.map((registro) => (
              <tr key={registro.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <Checkbox
                    checked={selectedIds.includes(registro.id)}
                    onCheckedChange={(checked) => handleSelectRecord(registro.id, checked as boolean)}
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={`/avatars/${registro.funcionario.id}.jpg`} />
                      <AvatarFallback>
                        {registro.funcionario.nome.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {registro.funcionario.nome}
                      </div>
                      <div className="text-sm text-gray-500">
                        Mat: {registro.funcionario.matricula}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {format(new Date(registro.data), 'dd/MM/yyyy', { locale: ptBR })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span>{registro.entrada} - {registro.saida}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatHours(registro.horasTrabalhadas)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getHourTypeBadge(registro.tipoHora)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(registro.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleApprove([registro.id])}>
                        <Check className="mr-2 h-4 w-4" />
                        Aprovar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleReject([registro.id])}>
                        <X className="mr-2 h-4 w-4" />
                        Rejeitar
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => {
                          setSelectedRecord(registro);
                          setShowJustificationModal(true);
                        }}
                      >
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Justificar
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {registros.length === 0 && (
          <div className="text-center py-12">
            <Clock className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum registro encontrado</h3>
            <p className="mt-1 text-sm text-gray-500">
              Não há registros para classificar no período selecionado.
            </p>
          </div>
        )}
      </div>

      {/* Modal de Justificativa */}
      <JustificationModal
        isOpen={showJustificationModal}
        onClose={() => {
          setShowJustificationModal(false);
          setSelectedRecord(null);
        }}
        record={selectedRecord}
        onSave={fetchRegistros}
      />
    </>
  );
}
```

## 🔌 API Routes

### ⏰ API Principal de Classificação (route.ts)
```typescript
// app/api/classificar-horas/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { startOfDay, endOfDay, parseISO } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const funcionario = searchParams.get('funcionario');
    const periodo = searchParams.get('periodo');
    const status = searchParams.get('status');
    const tipo = searchParams.get('tipo');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 50;

    const where: any = {};

    if (funcionario) {
      where.funcionario = {
        OR: [
          { nome: { contains: funcionario, mode: 'insensitive' } },
          { matricula: { contains: funcionario, mode: 'insensitive' } },
        ],
      };
    }

    if (periodo) {
      const [startDate, endDate] = periodo.split(',');
      where.data = {
        gte: startOfDay(parseISO(startDate)),
        lte: endOfDay(parseISO(endDate)),
      };
    }

    if (status) {
      where.status = status;
    }

    if (tipo) {
      where.tipoHora = tipo;
    }

    const [registros, total] = await Promise.all([
      prisma.registroClassificacao.findMany({
        where,
        include: {
          funcionario: {
            select: {
              id: true,
              nome: true,
              matricula: true,
            },
          },
          aprovadoPor: {
            select: {
              nome: true,
            },
          },
        },
        orderBy: [
          { data: 'desc' },
          { funcionario: { nome: 'asc' } },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.registroClassificacao.count({ where }),
    ]);

    return NextResponse.json({
      registros,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error('Erro ao buscar registros de classificação:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Schema do Banco de Dados

### ⏰ Modelo Prisma
```prisma
model RegistroClassificacao {
  id                Int       @id @default(autoincrement())
  funcionarioId     Int       @map("funcionario_id")
  data              DateTime
  entrada           String
  saida             String
  horasTrabalhadas  Int       // em minutos
  tipoHora          String    @map("tipo_hora") // 'normal', 'extra_50', 'extra_100', 'falta', 'atraso'
  status            String    @default("pendente") // 'pendente', 'aprovado', 'rejeitado'
  justificativa     String?
  observacoes       String?
  aprovadoEm        DateTime? @map("aprovado_em")
  aprovadoPorId     Int?      @map("aprovado_por_id")
  criadoEm          DateTime  @default(now()) @map("criado_em")
  atualizadoEm      DateTime  @updatedAt @map("atualizado_em")

  // Relacionamentos
  funcionario       Funcionario @relation(fields: [funcionarioId], references: [id])
  aprovadoPor       Usuario?    @relation(fields: [aprovadoPorId], references: [id])

  @@map("registros_classificacao")
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Dashboard de classificação
- [ ] Tabela de registros
- [ ] Filtros avançados
- [ ] Classificação automática
- [ ] Aprovação em lote
- [ ] Justificativas
- [ ] Exportação de dados
- [ ] Histórico de alterações

### 🔧 Validações
- [ ] Cálculo correto de horas
- [ ] Validação de períodos
- [ ] Controle de permissões
- [ ] Logs de auditoria
- [ ] Backup de dados

## 🚀 Próximos Passos
1. **Fechamento** - Processo de fechamento mensal
2. **Relatórios** - Relatórios detalhados
3. **Estatísticas** - Análises avançadas
