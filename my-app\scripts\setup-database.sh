#!/bin/bash

# Script para configurar o banco de dados do RLPONTO
# Executa migrações do Prisma e cria usuário inicial

set -e

echo "🗄️ Configurando banco de dados RLPONTO..."

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do projeto"
    exit 1
fi

# Verificar se o Prisma está instalado
if ! command -v npx &> /dev/null; then
    echo "❌ Erro: Node.js/npm não encontrado"
    exit 1
fi

echo "1. Gerando cliente Prisma..."
npx prisma generate

echo "2. Executando migrações do banco..."
npx prisma db push

echo "3. Criando usuário administrador inicial..."
npx prisma db seed

echo "✅ Banco de dados configurado com sucesso!"
echo ""
echo "📋 Credenciais do usuário administrador:"
echo "   Usuário: admin"
echo "   Senha: 200381"
echo ""
echo "🌐 Acesse o sistema em: http://************"
