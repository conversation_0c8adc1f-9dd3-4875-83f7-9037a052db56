import { useState, useCallback } from 'react';
import { signIn, signOut, useSession } from 'next-auth/react';
import { LoginCredentials, LoginResponse } from '@/types';

interface UseAuthReturn {
  user: any;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  clearError: () => void;
  isAuthenticated: boolean;
}

export const useAuth = (): UseAuthReturn => {
  const { data: session, status } = useSession();
  const [error, setError] = useState<string | null>(null);

  const login = useCallback(async (credentials: LoginCredentials): Promise<LoginResponse> => {
    setError(null);

    try {
      const result = await signIn('credentials', {
        usuario: credentials.usuario,
        senha: credentials.senha,
        redirect: false,
      });

      if (result?.error) {
        const errorMessage = 'Usu<PERSON><PERSON> ou senha inválid<PERSON>';
        setError(errorMessage);
        return {
          success: false,
          error: errorMessage,
        };
      }

      if (result?.ok) {
        return {
          success: true,
          user: session?.user,
        };
      }

      return {
        success: false,
        error: 'Erro desconhecido no login',
      };
    } catch (err) {
      const errorMessage = 'Erro de conexão. Tente novamente.';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, [session]);

  const logout = useCallback(async (): Promise<void> => {
    try {
      await signOut({ redirect: false });
      window.location.href = '/login';
    } catch (err) {
      console.error('Erro ao fazer logout:', err);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    user: session?.user || null,
    isLoading: status === 'loading',
    error,
    login,
    logout,
    clearError,
    isAuthenticated: !!session?.user,
  };
};
