# 🚀 Scripts de Deploy - Sistema RLPONTO

## 📋 Visão Geral

Este diretório contém todos os scripts necessários para configurar e fazer deploy do Sistema RLPONTO no ambiente de produção.

## 🖥️ Servidor de Produção

**Configurações do Container LXC:**
- **IP**: ************
- **SO**: Ubuntu 22.04
- **Usuário**: root
- **Senha**: @Ric6109
- **Rede**: Local (HTTP apenas)

## 📁 Scripts Disponíveis

### 1. 🔧 `setup-complete.sh` - **SCRIPT PRINCIPAL**
**Executa todo o processo de configuração automaticamente**

```bash
chmod +x scripts/setup-complete.sh
./scripts/setup-complete.sh
```

**O que faz:**
- Configura SSH sem senha
- Instala e configura todos os serviços
- Prepara ambiente para deploy
- Configura monitoramento e backup automático

### 2. 🔑 `setup-production.sh`
**Configura conexão SSH sem senha**

```bash
./scripts/setup-production.sh
```

**Funcionalidades:**
- Gera chave SSH `rl-ponto-next`
- Configura `~/.ssh/config` com alias `rlponto-prod`
- Copia chave pública para o servidor
- Testa conexão SSH

### 3. 🖥️ `configure-server.sh`
**Configura o servidor Ubuntu 22.04**

```bash
# Executado automaticamente pelo setup-complete.sh
# Ou manualmente no servidor:
ssh rlponto-prod
chmod +x /tmp/configure-server.sh
./tmp/configure-server.sh
```

**Instala e configura:**
- Node.js 18
- MySQL 8.0
- Nginx
- PM2
- UFW Firewall
- Fail2Ban
- Usuário `rlponto`
- Estrutura de diretórios

### 4. 🚀 `deploy.sh`
**Deploy automatizado da aplicação**

```bash
./scripts/deploy.sh
```

**Processo de deploy:**
- Executa testes locais
- Gera build de produção
- Envia para servidor
- Instala dependências
- Executa migrações
- Reinicia aplicação com PM2
- Verifica saúde da aplicação

### 5. 📊 `backup-and-monitor.sh`
**Backup e monitoramento do sistema**

```bash
# No servidor (executado automaticamente via cron):
/opt/rlponto/backup-and-monitor.sh backup   # Backup completo
/opt/rlponto/backup-and-monitor.sh monitor  # Verificar serviços
/opt/rlponto/backup-and-monitor.sh report   # Gerar relatório
/opt/rlponto/backup-and-monitor.sh full     # Todas as operações
```

## 🔄 Fluxo de Configuração

### Primeira Configuração (Uma vez apenas):

```bash
# 1. Executar setup completo
./scripts/setup-complete.sh

# 2. Fazer primeiro deploy
./scripts/deploy.sh

# 3. Acessar sistema
# http://************
```

### Deploy Regular:

```bash
# Para novos deploys
./scripts/deploy.sh
```

## 🔑 Configuração SSH

Após executar o setup, você terá:

**Arquivo `~/.ssh/config`:**
```
Host rlponto-prod
    HostName ************
    User root
    IdentityFile ~/.ssh/rl-ponto-next
    IdentitiesOnly yes
    StrictHostKeyChecking no
    ServerAliveInterval 60
```

**Comandos SSH:**
```bash
ssh rlponto-prod                    # Conectar ao servidor
ssh rlponto-prod 'pm2 status'       # Executar comando remoto
scp arquivo.txt rlponto-prod:/tmp/   # Copiar arquivo
```

## 🏗️ Estrutura no Servidor

```
/opt/rlponto/
├── current/                 # Symlink para release atual
├── releases/               # Releases anteriores
│   ├── 20240115_143022/   # Release por timestamp
│   └── 20240115_150145/
├── shared/                # Arquivos compartilhados
│   ├── .env.production    # Variáveis de ambiente
│   ├── logs/             # Logs da aplicação
│   └── uploads/          # Uploads de usuários
├── backups/              # Backups automáticos
│   ├── database/         # Backups do MySQL
│   └── uploads/          # Backups dos uploads
└── backup-and-monitor.sh # Script de monitoramento
```

## 📊 Monitoramento Automático

### Cron Jobs Configurados:

```bash
# Backup diário às 2h da manhã
0 2 * * * /opt/rlponto/backup-and-monitor.sh backup

# Monitoramento a cada 15 minutos
*/15 * * * * /opt/rlponto/backup-and-monitor.sh monitor

# Relatório semanal aos domingos às 6h
0 6 * * 0 /opt/rlponto/backup-and-monitor.sh report
```

### Verificar Monitoramento:

```bash
# Ver cron jobs
ssh rlponto-prod 'crontab -u rlponto -l'

# Executar monitoramento manual
ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh monitor'

# Ver logs de backup
ssh rlponto-prod 'ls -la /opt/rlponto/backups/'
```

## 🔧 Comandos Úteis

### Gerenciamento da Aplicação:
```bash
ssh rlponto-prod 'pm2 status'           # Status
ssh rlponto-prod 'pm2 logs rlponto'     # Logs
ssh rlponto-prod 'pm2 restart rlponto'  # Reiniciar
ssh rlponto-prod 'pm2 stop rlponto'     # Parar
ssh rlponto-prod 'pm2 start rlponto'    # Iniciar
```

### Gerenciamento de Serviços:
```bash
ssh rlponto-prod 'systemctl status nginx'   # Status Nginx
ssh rlponto-prod 'systemctl status mysql'   # Status MySQL
ssh rlponto-prod 'systemctl restart nginx'  # Reiniciar Nginx
ssh rlponto-prod 'systemctl restart mysql'  # Reiniciar MySQL
```

### Logs do Sistema:
```bash
ssh rlponto-prod 'tail -f /var/log/rlponto/combined.log'  # Logs da app
ssh rlponto-prod 'tail -f /var/log/nginx/rlponto_access.log'  # Nginx access
ssh rlponto-prod 'tail -f /var/log/nginx/rlponto_error.log'   # Nginx error
```

### Banco de Dados:
```bash
ssh rlponto-prod 'mysql -u rlponto_user -p rlponto'  # Conectar ao MySQL
ssh rlponto-prod 'mysqldump -u rlponto_user -p rlponto > backup.sql'  # Backup manual
```

## 🚨 Troubleshooting

### Problemas Comuns:

#### 1. SSH não conecta:
```bash
# Verificar chave SSH
ls -la ~/.ssh/rl-ponto-next*

# Testar conexão manual
ssh -i ~/.ssh/rl-ponto-next root@************
```

#### 2. Aplicação não inicia:
```bash
# Ver logs detalhados
ssh rlponto-prod 'pm2 logs rlponto --lines 50'

# Verificar variáveis de ambiente
ssh rlponto-prod 'cat /opt/rlponto/shared/.env.production'

# Testar manualmente
ssh rlponto-prod 'cd /opt/rlponto/current && npm start'
```

#### 3. Nginx não responde:
```bash
# Verificar configuração
ssh rlponto-prod 'nginx -t'

# Ver logs de erro
ssh rlponto-prod 'tail -f /var/log/nginx/error.log'

# Reiniciar serviço
ssh rlponto-prod 'systemctl restart nginx'
```

#### 4. MySQL não conecta:
```bash
# Verificar status
ssh rlponto-prod 'systemctl status mysql'

# Testar conexão
ssh rlponto-prod 'mysql -u rlponto_user -p -e "SHOW DATABASES;"'
```

## 📞 Suporte

Para problemas ou dúvidas:

1. **Verificar logs** primeiro
2. **Executar monitoramento**: `./backup-and-monitor.sh monitor`
3. **Consultar documentação** do projeto
4. **Contatar equipe técnica**

## 🔄 Atualizações

Para atualizar os scripts:

1. Modificar scripts localmente
2. Testar em ambiente de desenvolvimento
3. Fazer commit das mudanças
4. Executar novo deploy

---

**Criado por**: Equipe RLPONTO  
**Última atualização**: Janeiro 2024  
**Versão**: 1.0
