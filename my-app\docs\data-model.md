# 🗄️ Modelo de Dados - Sistema RLPONTO

## 📋 Visão Geral

Este documento define o modelo de dados do Sistema RLPONTO, incluindo entidades principais, relacionamentos, padrões de nomenclatura e estrutura do banco de dados.

## 🎯 Princípios do Modelo

### Princípios de Design
- **Normalização**: Evitar redundância de dados
- **Integridade**: Garantir consistência referencial
- **Performance**: Otimi<PERSON> para consultas frequentes
- **Escalabilidade**: Suportar crescimento de dados
- **Auditoria**: Rastrear todas as mudanças
- **Flexibilidade**: Permitir extensões futuras

### Padrões de Nomenclatura
- **Tabelas**: snake_case no plural (ex: `funcionarios`)
- **Colunas**: snake_case (ex: `data_admissao`)
- **Cha<PERSON>**: `id` (auto-increment)
- **Chaves <PERSON>**: `{tabela}_id` (ex: `funcionario_id`)
- **Timestamps**: `criado_em`, `atualizado_em`
- **Soft Delete**: `deletado_em`

## 🏗️ Arquitetura do Banco

### Tecnologia
- **SGBD**: MySQL 8.0+
- **ORM**: Prisma 5.0+
- **Charset**: utf8mb4
- **Collation**: utf8mb4_unicode_ci
- **Engine**: InnoDB

### Estrutura Geral
```
┌─────────────────────────────────────────────────────────────┐
│                    SISTEMA RLPONTO                         │
├─────────────────────────────────────────────────────────────┤
│  Core Entities                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Usuarios   │ │Funcionarios │ │   Empresa   │          │
│  │             │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Time Tracking                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Registros   │ │Classificacao│ │ Fechamento  │          │
│  │   Ponto     │ │   Horas     │ │   Mensal    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Organization                                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │Departamentos│ │   Cargos    │ │  Jornadas   │          │
│  │             │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Audit & Logs                                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Logs      │ │  Auditoria  │ │ Notificacoes│          │
│  │  Sistema    │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📊 Entidades Principais

### 🏢 Empresa
```sql
CREATE TABLE empresas (
  id INT PRIMARY KEY AUTO_INCREMENT,
  razao_social VARCHAR(255) NOT NULL,
  nome_fantasia VARCHAR(255) NOT NULL,
  cnpj VARCHAR(18) UNIQUE NOT NULL,
  inscricao_estadual VARCHAR(50),
  inscricao_municipal VARCHAR(50),
  endereco JSON,
  contato JSON,
  configuracoes JSON,
  ativo BOOLEAN DEFAULT TRUE,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  criado_por INT,
  atualizado_por INT,
  
  INDEX idx_cnpj (cnpj),
  INDEX idx_ativo (ativo)
);
```

### 👥 Usuários
```sql
CREATE TABLE usuarios (
  id INT PRIMARY KEY AUTO_INCREMENT,
  usuario VARCHAR(100) UNIQUE NOT NULL,
  senha_hash VARCHAR(255) NOT NULL,
  nome VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  nivel_acesso ENUM('admin', 'hr', 'manager', 'user', 'readonly') NOT NULL,
  funcionario_id INT,
  ultimo_login TIMESTAMP NULL,
  tentativas_login INT DEFAULT 0,
  ativo BOOLEAN DEFAULT TRUE,
  bloqueado BOOLEAN DEFAULT FALSE,
  forcar_troca_senha BOOLEAN DEFAULT TRUE,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_usuario (usuario),
  INDEX idx_email (email),
  INDEX idx_nivel_acesso (nivel_acesso),
  INDEX idx_ativo (ativo),
  FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id)
);
```

### 👨‍💼 Funcionários
```sql
CREATE TABLE funcionarios (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nome VARCHAR(255) NOT NULL,
  cpf VARCHAR(14) UNIQUE NOT NULL,
  rg VARCHAR(20),
  matricula VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE,
  telefone VARCHAR(20),
  data_nascimento DATE,
  endereco JSON,
  departamento_id INT,
  cargo_id INT,
  data_admissao DATE NOT NULL,
  data_desligamento DATE NULL,
  salario DECIMAL(10,2),
  jornada_id INT,
  foto_url VARCHAR(500),
  documentos JSON,
  ativo BOOLEAN DEFAULT TRUE,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  criado_por INT,
  atualizado_por INT,
  
  INDEX idx_cpf (cpf),
  INDEX idx_matricula (matricula),
  INDEX idx_email (email),
  INDEX idx_departamento (departamento_id),
  INDEX idx_cargo (cargo_id),
  INDEX idx_ativo (ativo),
  INDEX idx_data_admissao (data_admissao),
  FOREIGN KEY (departamento_id) REFERENCES departamentos(id),
  FOREIGN KEY (cargo_id) REFERENCES cargos(id),
  FOREIGN KEY (jornada_id) REFERENCES jornadas(id)
);
```

### 🏢 Departamentos
```sql
CREATE TABLE departamentos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  codigo VARCHAR(20) UNIQUE,
  empresa_id INT NOT NULL,
  parent_id INT NULL,
  gestor_id INT NULL,
  ativo BOOLEAN DEFAULT TRUE,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_empresa (empresa_id),
  INDEX idx_parent (parent_id),
  INDEX idx_gestor (gestor_id),
  INDEX idx_ativo (ativo),
  FOREIGN KEY (empresa_id) REFERENCES empresas(id),
  FOREIGN KEY (parent_id) REFERENCES departamentos(id),
  FOREIGN KEY (gestor_id) REFERENCES funcionarios(id)
);
```

### 💼 Cargos
```sql
CREATE TABLE cargos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  nivel ENUM('estagiario', 'junior', 'pleno', 'senior', 'coordenador', 'gerente', 'diretor'),
  empresa_id INT NOT NULL,
  departamento_id INT,
  salario_base DECIMAL(10,2),
  ativo BOOLEAN DEFAULT TRUE,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_empresa (empresa_id),
  INDEX idx_departamento (departamento_id),
  INDEX idx_nivel (nivel),
  INDEX idx_ativo (ativo),
  FOREIGN KEY (empresa_id) REFERENCES empresas(id),
  FOREIGN KEY (departamento_id) REFERENCES departamentos(id)
);
```

### ⏰ Jornadas de Trabalho
```sql
CREATE TABLE jornadas (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  empresa_id INT NOT NULL,
  horas_semanais INT NOT NULL,
  dias_semana JSON NOT NULL, -- [1,2,3,4,5] para seg-sex
  horarios JSON NOT NULL, -- {"entrada": "08:00", "saida": "17:00", "intervalo": {"inicio": "12:00", "fim": "13:00"}}
  tolerancia_atraso INT DEFAULT 10, -- minutos
  tolerancia_saida INT DEFAULT 10, -- minutos
  ativo BOOLEAN DEFAULT TRUE,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_empresa (empresa_id),
  INDEX idx_ativo (ativo),
  FOREIGN KEY (empresa_id) REFERENCES empresas(id)
);
```

### 📅 Registros de Ponto
```sql
CREATE TABLE registros_ponto (
  id INT PRIMARY KEY AUTO_INCREMENT,
  funcionario_id INT NOT NULL,
  data DATE NOT NULL,
  entrada TIMESTAMP NULL,
  saida TIMESTAMP NULL,
  intervalo_inicio TIMESTAMP NULL,
  intervalo_fim TIMESTAMP NULL,
  tipo_registro ENUM('biometrico', 'manual', 'automatico') NOT NULL,
  dispositivo_id VARCHAR(100),
  localizacao JSON, -- {"latitude": -23.5505, "longitude": -46.6333, "precisao": 10}
  foto_url VARCHAR(500),
  observacoes TEXT,
  justificativa TEXT,
  aprovado BOOLEAN DEFAULT FALSE,
  aprovado_por_id INT NULL,
  aprovado_em TIMESTAMP NULL,
  criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_funcionario_data (funcionario_id, data),
  INDEX idx_data (data),
  INDEX idx_tipo_registro (tipo_registro),
  INDEX idx_aprovado (aprovado),
  UNIQUE KEY uk_funcionario_data (funcionario_id, data),
  FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
  FOREIGN KEY (aprovado_por_id) REFERENCES usuarios(id)
);
```

### 📊 Classificação de Horas
```sql
CREATE TABLE classificacao_horas (
  id INT PRIMARY KEY AUTO_INCREMENT,
  registro_ponto_id INT NOT NULL,
  funcionario_id INT NOT NULL,
  data DATE NOT NULL,
  horas_normais DECIMAL(4,2) DEFAULT 0,
  horas_extras_50 DECIMAL(4,2) DEFAULT 0,
  horas_extras_100 DECIMAL(4,2) DEFAULT 0,
  horas_noturnas DECIMAL(4,2) DEFAULT 0,
  horas_falta DECIMAL(4,2) DEFAULT 0,
  minutos_atraso INT DEFAULT 0,
  minutos_saida_antecipada INT DEFAULT 0,
  observacoes TEXT,
  processado_automaticamente BOOLEAN DEFAULT TRUE,
  processado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processado_por_id INT,
  
  INDEX idx_funcionario_data (funcionario_id, data),
  INDEX idx_data (data),
  INDEX idx_registro_ponto (registro_ponto_id),
  UNIQUE KEY uk_registro_ponto (registro_ponto_id),
  FOREIGN KEY (registro_ponto_id) REFERENCES registros_ponto(id),
  FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
  FOREIGN KEY (processado_por_id) REFERENCES usuarios(id)
);
```

### 📋 Fechamento Mensal
```sql
CREATE TABLE fechamentos_mensais (
  id INT PRIMARY KEY AUTO_INCREMENT,
  empresa_id INT NOT NULL,
  ano INT NOT NULL,
  mes INT NOT NULL,
  data_inicio DATE NOT NULL,
  data_fim DATE NOT NULL,
  total_funcionarios INT NOT NULL,
  total_registros INT NOT NULL,
  total_horas_normais DECIMAL(10,2) DEFAULT 0,
  total_horas_extras DECIMAL(10,2) DEFAULT 0,
  status ENUM('aberto', 'processando', 'fechado', 'reaberto') DEFAULT 'aberto',
  processado_por_id INT,
  processado_em TIMESTAMP NULL,
  fechado_por_id INT,
  fechado_em TIMESTAMP NULL,
  observacoes TEXT,
  
  INDEX idx_empresa_periodo (empresa_id, ano, mes),
  INDEX idx_status (status),
  UNIQUE KEY uk_empresa_periodo (empresa_id, ano, mes),
  FOREIGN KEY (empresa_id) REFERENCES empresas(id),
  FOREIGN KEY (processado_por_id) REFERENCES usuarios(id),
  FOREIGN KEY (fechado_por_id) REFERENCES usuarios(id)
);
```

## 🔗 Relacionamentos

### Diagrama ER Simplificado
```
Empresa (1) ──────── (N) Funcionarios
   │                      │
   │                      │
   └── (N) Departamentos ──┘
   │                      │
   └── (N) Cargos ────────┘
   │                      │
   └── (N) Jornadas ──────┘
                          │
                          │
Usuarios (N) ──── (1) Funcionarios (1) ──── (N) RegistrosPonto
                          │                      │
                          │                      │
                          └──── (N) ClassificacaoHoras
                                      │
                                      │
                          FechamentosMensais ───┘
```

### Relacionamentos Detalhados

#### 1:N (Um para Muitos)
- **Empresa → Funcionários**: Uma empresa tem muitos funcionários
- **Empresa → Departamentos**: Uma empresa tem muitos departamentos
- **Empresa → Cargos**: Uma empresa tem muitos cargos
- **Funcionário → Registros de Ponto**: Um funcionário tem muitos registros
- **Departamento → Funcionários**: Um departamento tem muitos funcionários

#### 1:1 (Um para Um)
- **Usuário → Funcionário**: Um usuário pode estar associado a um funcionário
- **Registro de Ponto → Classificação de Horas**: Cada registro tem uma classificação

#### N:N (Muitos para Muitos)
- **Funcionários ↔ Projetos**: Através de tabela de associação (futura implementação)

## 📋 Padrões de Dados

### Tipos de Dados Padronizados
```typescript
// Tipos TypeScript correspondentes
interface Endereco {
  logradouro: string;
  numero: string;
  complemento?: string;
  bairro: string;
  cidade: string;
  estado: string;
  cep: string;
  pais: string;
}

interface Contato {
  telefone: string;
  email: string;
  site?: string;
  whatsapp?: string;
}

interface Localizacao {
  latitude: number;
  longitude: number;
  precisao: number;
  endereco?: string;
}

interface Horario {
  entrada: string; // "HH:mm"
  saida: string;   // "HH:mm"
  intervalo?: {
    inicio: string; // "HH:mm"
    fim: string;    // "HH:mm"
  };
}
```

### Validações de Dados
```sql
-- Constraints de validação
ALTER TABLE funcionarios 
ADD CONSTRAINT chk_cpf_format 
CHECK (cpf REGEXP '^[0-9]{3}\.[0-9]{3}\.[0-9]{3}-[0-9]{2}$');

ALTER TABLE empresas 
ADD CONSTRAINT chk_cnpj_format 
CHECK (cnpj REGEXP '^[0-9]{2}\.[0-9]{3}\.[0-9]{3}/[0-9]{4}-[0-9]{2}$');

ALTER TABLE registros_ponto 
ADD CONSTRAINT chk_entrada_saida 
CHECK (saida IS NULL OR entrada IS NULL OR saida >= entrada);

ALTER TABLE classificacao_horas 
ADD CONSTRAINT chk_horas_positivas 
CHECK (horas_normais >= 0 AND horas_extras_50 >= 0 AND horas_extras_100 >= 0);
```

## 🔍 Índices e Performance

### Índices Principais
```sql
-- Índices compostos para consultas frequentes
CREATE INDEX idx_funcionario_periodo ON registros_ponto (funcionario_id, data);
CREATE INDEX idx_empresa_ativo ON funcionarios (empresa_id, ativo);
CREATE INDEX idx_departamento_ativo ON funcionarios (departamento_id, ativo);

-- Índices para relatórios
CREATE INDEX idx_data_tipo ON registros_ponto (data, tipo_registro);
CREATE INDEX idx_aprovacao ON registros_ponto (aprovado, aprovado_em);

-- Índices para auditoria
CREATE INDEX idx_criado_em ON funcionarios (criado_em);
CREATE INDEX idx_atualizado_em ON funcionarios (atualizado_em);
```

### Particionamento (Futuro)
```sql
-- Particionamento por data para registros_ponto
ALTER TABLE registros_ponto 
PARTITION BY RANGE (YEAR(data)) (
  PARTITION p2023 VALUES LESS THAN (2024),
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🔒 Segurança de Dados

### Criptografia
- **Senhas**: bcrypt hash
- **Dados Sensíveis**: AES-256 encryption
- **PII**: Campos específicos criptografados

### Auditoria
```sql
CREATE TABLE logs_auditoria (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tabela VARCHAR(100) NOT NULL,
  registro_id INT NOT NULL,
  acao ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
  valores_antigos JSON,
  valores_novos JSON,
  usuario_id INT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_tabela_registro (tabela, registro_id),
  INDEX idx_usuario (usuario_id),
  INDEX idx_timestamp (timestamp)
);
```

## 📊 Métricas do Banco

### Estimativas de Volume
- **Funcionários**: 1,000 por empresa
- **Registros de Ponto**: 250,000 por mês (1,000 funcionários × 22 dias úteis)
- **Classificações**: 250,000 por mês
- **Logs de Auditoria**: 500,000 por mês
- **Crescimento**: 20% ao ano

### Tamanho Estimado
- **Ano 1**: ~5GB
- **Ano 3**: ~15GB
- **Ano 5**: ~30GB

---

**Documento criado em**: [Data]  
**Última atualização**: [Data]  
**Versão**: 1.0  
**Responsável**: [Nome do Database Architect]
