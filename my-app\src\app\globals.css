@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Melhorar contraste dos placeholders em todo o sistema */
input::placeholder,
textarea::placeholder,
select::placeholder {
  color: #6b7280 !important; /* text-gray-500 */
  opacity: 1;
}

/* Garantir que ícones tenham contraste adequado */
.text-gray-400 {
  color: #6b7280 !important; /* Mudar de gray-400 para gray-500 */
}

/* Melhorar contraste de textos secundários */
.text-gray-400 {
  color: #6b7280 !important;
}

/* Melhorar contraste de selects */
select {
  color: #111827 !important; /* text-gray-900 */
}

select option {
  color: #111827 !important;
}

select option[value=""] {
  color: #6b7280 !important; /* Placeholder option */
}

/* Melhorar contraste de inputs de data e hora */
input[type="date"],
input[type="time"],
input[type="datetime-local"] {
  color: #111827 !important;
  color-scheme: light;
}

/* Melhorar contraste de botões outline */
.border-gray-300 {
  border-color: #9ca3af !important; /* gray-400 em vez de gray-300 */
}

