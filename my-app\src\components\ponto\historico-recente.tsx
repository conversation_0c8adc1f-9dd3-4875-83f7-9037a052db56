'use client';

import { useState, useEffect } from 'react';
import { 
  Clock, 
  LogIn, 
  LogOut, 
  Coffee, 
  Fingerprint, 
  Camera,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface RegistroPonto {
  id: string;
  funcionario: {
    nome: string;
    matricula: string;
  };
  tipo: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim';
  metodo: 'fingerprint' | 'facial' | 'manual';
  horario: string;
  data: string;
  status: 'sucesso' | 'erro';
}

export function HistoricoRecente() {
  const [registros, setRegistros] = useState<RegistroPonto[]>([]);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    fetchHistorico();
  }, []);

  const fetchHistorico = async () => {
    try {
      setLoading(true);

      // Usar data base fixa para evitar problemas de hidratação
      const baseDate = new Date('2024-01-27');

      // Simulação de dados (substituir por API real)
      const mockData: RegistroPonto[] = [
        {
          id: '1',
          funcionario: {
            nome: 'João Silva Santos',
            matricula: 'EMP001'
          },
          tipo: 'entrada',
          metodo: 'fingerprint',
          horario: '08:00:15',
          data: baseDate.toLocaleDateString('pt-BR'),
          status: 'sucesso'
        },
        {
          id: '2',
          funcionario: {
            nome: 'Maria Oliveira Costa',
            matricula: 'EMP002'
          },
          tipo: 'intervalo_inicio',
          metodo: 'facial',
          horario: '12:00:32',
          data: baseDate.toLocaleDateString('pt-BR'),
          status: 'sucesso'
        },
        {
          id: '3',
          funcionario: {
            nome: 'Carlos Roberto Lima',
            matricula: 'EMP003'
          },
          tipo: 'saida',
          metodo: 'fingerprint',
          horario: '17:30:45',
          data: new Date(baseDate.getTime() - 86400000).toLocaleDateString('pt-BR'),
          status: 'sucesso'
        },
        {
          id: '4',
          funcionario: {
            nome: 'Ana Paula Silva',
            matricula: 'EMP004'
          },
          tipo: 'entrada',
          metodo: 'facial',
          horario: '07:45:12',
          data: new Date(baseDate.getTime() - 86400000).toLocaleDateString('pt-BR'),
          status: 'erro'
        }
      ];

      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setRegistros(mockData);
    } catch (error) {
      console.error('Erro ao buscar histórico:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'entrada':
        return <LogIn className="h-5 w-5 text-green-600" />;
      case 'saida':
        return <LogOut className="h-5 w-5 text-red-600" />;
      case 'intervalo_inicio':
      case 'intervalo_fim':
        return <Coffee className="h-5 w-5 text-yellow-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getMetodoIcon = (metodo: string) => {
    switch (metodo) {
      case 'fingerprint':
        return <Fingerprint className="h-4 w-4 text-blue-600" />;
      case 'facial':
        return <Camera className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sucesso':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'erro':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTipoLabel = (tipo: string) => {
    switch (tipo) {
      case 'entrada':
        return 'Entrada';
      case 'saida':
        return 'Saída';
      case 'intervalo_inicio':
        return 'Início Intervalo';
      case 'intervalo_fim':
        return 'Fim Intervalo';
      default:
        return tipo;
    }
  };

  const getMetodoLabel = (metodo: string) => {
    switch (metodo) {
      case 'fingerprint':
        return 'Biometria Digital';
      case 'facial':
        return 'Reconhecimento Facial';
      case 'manual':
        return 'Manual';
      default:
        return metodo;
    }
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg animate-pulse">
            <div className="w-12 h-12 bg-gray-200 rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4" />
              <div className="h-3 bg-gray-200 rounded w-1/3" />
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    );
  }

  if (registros.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="h-12 w-12 text-gray-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum registro encontrado</h3>
        <p className="text-gray-600">Os registros de ponto aparecerão aqui</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {registros.map((registro) => (
        <div 
          key={registro.id} 
          className={`flex items-center space-x-4 p-4 border rounded-lg transition-colors ${
            registro.status === 'sucesso' 
              ? 'border-gray-200 hover:bg-gray-50' 
              : 'border-red-200 bg-red-50'
          }`}
        >
          {/* Ícone do tipo */}
          <div className="flex-shrink-0">
            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
              {getTipoIcon(registro.tipo)}
            </div>
          </div>

          {/* Informações principais */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {registro.funcionario.nome}
              </h4>
              <span className="text-xs text-gray-500">
                ({registro.funcionario.matricula})
              </span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <span>{getTipoLabel(registro.tipo)}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                {getMetodoIcon(registro.metodo)}
                <span>{getMetodoLabel(registro.metodo)}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                {getStatusIcon(registro.status)}
                <span className={registro.status === 'sucesso' ? 'text-green-600' : 'text-red-600'}>
                  {registro.status === 'sucesso' ? 'Sucesso' : 'Erro'}
                </span>
              </div>
            </div>
          </div>

          {/* Horário e data */}
          <div className="flex-shrink-0 text-right">
            <div className="text-sm font-medium text-gray-900">
              {registro.horario}
            </div>
            <div className="text-xs text-gray-500">
              {registro.data}
            </div>
          </div>
        </div>
      ))}

      {/* Link para ver mais */}
      <div className="text-center pt-4">
        <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
          Ver histórico completo
        </button>
      </div>
    </div>
  );
}

