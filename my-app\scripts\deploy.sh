#!/bin/bash

# 🚀 Script de Deploy - Sistema RLPONTO
# Deploy automatizado para servidor de produção

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
SERVER_ALIAS="rlponto-prod"
APP_USER="rlponto"
APP_DIR="/opt/rlponto"
REPO_URL="https://github.com/seu-usuario/rlponto-system.git"  # Ajustar conforme necessário
BRANCH="main"

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

echo -e "${BLUE}🚀 Deploy Sistema RLPONTO${NC}"
echo -e "${BLUE}=========================${NC}"

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    error "Execute este script a partir do diretório raiz do projeto (onde está o package.json)"
fi

# Verificar conexão SSH
log "1. Verificando conexão SSH..."
if ! ssh -o ConnectTimeout=10 ${SERVER_ALIAS} "echo 'Conexão OK'" >/dev/null 2>&1; then
    error "Não foi possível conectar ao servidor. Verifique a configuração SSH."
fi

# Gerar timestamp para release
RELEASE_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RELEASE_DIR="${APP_DIR}/releases/${RELEASE_TIMESTAMP}"

log "2. Preparando build local..."

# Instalar dependências
npm ci

# Executar testes
log "Executando testes..."
npm test

# Gerar build de produção
log "Gerando build de produção..."
npm run build

# Criar arquivo de release
log "3. Criando pacote de release..."
RELEASE_FILE="rlponto-${RELEASE_TIMESTAMP}.tar.gz"

# Criar arquivo tar com os arquivos necessários
tar -czf ${RELEASE_FILE} \
    --exclude=node_modules \
    --exclude=.git \
    --exclude=.next/cache \
    --exclude=coverage \
    --exclude=*.log \
    --exclude=.env.local \
    --exclude=.env.development \
    .

log "4. Enviando release para servidor..."

# Enviar arquivo para servidor
scp ${RELEASE_FILE} ${SERVER_ALIAS}:/tmp/

# Executar deploy no servidor
ssh ${SERVER_ALIAS} << EOF
set -e

log() {
    echo -e "\033[0;32m[\$(date +'%Y-%m-%d %H:%M:%S')] \$1\033[0m"
}

log "Iniciando deploy no servidor..."

# Criar diretório de release
sudo -u ${APP_USER} mkdir -p ${RELEASE_DIR}

# Extrair arquivos
log "Extraindo arquivos..."
cd ${RELEASE_DIR}
sudo -u ${APP_USER} tar -xzf /tmp/${RELEASE_FILE}

# Copiar variáveis de ambiente
log "Configurando variáveis de ambiente..."
sudo -u ${APP_USER} cp ${APP_DIR}/shared/.env.production ${RELEASE_DIR}/.env.production

# Instalar dependências de produção
log "Instalando dependências..."
sudo -u ${APP_USER} npm ci --only=production

# Executar migrações do banco (se necessário)
log "Executando migrações do banco..."
sudo -u ${APP_USER} npx prisma generate
sudo -u ${APP_USER} npx prisma migrate deploy

# Parar aplicação atual (se estiver rodando)
log "Parando aplicação atual..."
sudo -u ${APP_USER} pm2 stop rlponto || true

# Atualizar symlink para nova release
log "Atualizando symlink..."
sudo -u ${APP_USER} ln -sfn ${RELEASE_DIR} ${APP_DIR}/current

# Configurar PM2
log "Configurando PM2..."
sudo -u ${APP_USER} cat > ${APP_DIR}/current/ecosystem.config.js << 'EOL'
module.exports = {
  apps: [{
    name: 'rlponto',
    script: 'npm',
    args: 'start',
    cwd: '${APP_DIR}/current',
    instances: 1,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/rlponto/error.log',
    out_file: '/var/log/rlponto/out.log',
    log_file: '/var/log/rlponto/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
}
EOL

# Iniciar aplicação
log "Iniciando aplicação..."
cd ${APP_DIR}/current
sudo -u ${APP_USER} pm2 start ecosystem.config.js
sudo -u ${APP_USER} pm2 save

# Aguardar aplicação inicializar
log "Aguardando aplicação inicializar..."
sleep 10

# Verificar se aplicação está rodando
if curl -f http://localhost:3000/health >/dev/null 2>&1; then
    log "✅ Aplicação iniciada com sucesso!"
else
    log "❌ Falha ao iniciar aplicação. Verificando logs..."
    sudo -u ${APP_USER} pm2 logs rlponto --lines 20
    exit 1
fi

# Recarregar Nginx
log "Recarregando Nginx..."
systemctl reload nginx

# Limpar releases antigas (manter apenas as 5 mais recentes)
log "Limpando releases antigas..."
cd ${APP_DIR}/releases
sudo -u ${APP_USER} ls -t | tail -n +6 | xargs -r rm -rf

# Limpar arquivo temporário
rm -f /tmp/${RELEASE_FILE}

log "✅ Deploy concluído com sucesso!"
EOF

# Limpar arquivo local
rm -f ${RELEASE_FILE}

# Verificar deploy
log "5. Verificando deploy..."
if ssh ${SERVER_ALIAS} "curl -f http://localhost:3000/health" >/dev/null 2>&1; then
    log "✅ Deploy verificado com sucesso!"
    
    echo -e "${BLUE}"
    echo "================================================="
    echo "🎉 DEPLOY CONCLUÍDO COM SUCESSO!"
    echo "================================================="
    echo "Release: ${RELEASE_TIMESTAMP}"
    echo "Servidor: http://************"
    echo "Status: ✅ Online"
    echo ""
    echo "Comandos úteis:"
    echo "  ssh ${SERVER_ALIAS}                    # Conectar ao servidor"
    echo "  ssh ${SERVER_ALIAS} 'pm2 status'      # Status da aplicação"
    echo "  ssh ${SERVER_ALIAS} 'pm2 logs rlponto' # Ver logs"
    echo "  ssh ${SERVER_ALIAS} 'pm2 restart rlponto' # Reiniciar app"
    echo -e "${NC}"
else
    error "❌ Deploy falhou. Verifique os logs no servidor."
fi

log "Deploy finalizado!"
