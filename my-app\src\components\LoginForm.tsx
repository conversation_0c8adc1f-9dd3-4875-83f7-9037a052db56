'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Lock, User, LogIn } from 'lucide-react';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Alert } from '@/components/ui/Alert';
import { useAuth } from '@/hooks';
import { LoginFormData } from '@/types';

// Schema de validação
const loginSchema = z.object({
  usuario: z
    .string()
    .min(1, 'Usuário é obrigatório')
    .min(3, 'Usuário deve ter pelo menos 3 caracteres'),
  senha: z
    .string()
    .min(1, 'Senha é obrigatória')
    .min(6, 'Senha deve ter pelo menos 6 caracteres'),
  lembrarMe: z.boolean().optional(),
});

interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  redirectTo = '/dashboard',
}) => {
  const [showPassword, setShowPassword] = useState(false);

  // Debug detalhado: Verificar cada componente individualmente
  console.log('=== DEBUG LOGINFORM ===');
  console.log('Card:', Card);
  console.log('CardHeader:', CardHeader);
  console.log('CardContent:', CardContent);
  console.log('Button:', Button);
  console.log('Input:', Input);
  console.log('Alert:', Alert);
  console.log('Eye:', Eye);
  console.log('EyeOff:', EyeOff);
  console.log('Lock:', Lock);
  console.log('User:', User);
  console.log('LogIn:', LogIn);

  // Verificar se algum é undefined
  const componentes = { Card, CardHeader, CardContent, Button, Input, Alert, Eye, EyeOff, Lock, User, LogIn };
  Object.entries(componentes).forEach(([nome, comp]) => {
    if (comp === undefined) {
      console.error(`COMPONENTE UNDEFINED: ${nome}`);
    }
  });

  const { login, isLoading, error, clearError } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      usuario: '',
      senha: '',
      lembrarMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    clearError();

    try {
      const result = await login({
        usuario: data.usuario,
        senha: data.senha,
      });

      if (result.success) {
        if (result.requiresPasswordChange) {
          // Redirecionar para página de troca de senha
          window.location.href = '/auth/change-password';
        } else {
          // Login bem-sucedido
          if (onSuccess) {
            onSuccess();
          } else {
            window.location.href = redirectTo;
          }
        }
      } else {
        // Tratar erros específicos
        if (result.error?.includes('usuário')) {
          setError('usuario', { message: result.error });
        } else if (result.error?.includes('senha')) {
          setError('senha', { message: result.error });
        }
      }
    } catch (err) {
      console.error('Erro no login:', err);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card variant="elevated" className="shadow-xl">
          <CardHeader className="text-center">
            <div className="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
              <Lock className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">RLPONTO</h1>
            <p className="text-gray-600 mt-2">
              Sistema de Controle de Ponto
            </p>
          </CardHeader>

          <CardContent>
            {error && (
              <Alert
                variant="error"
                title="Erro no Login"
                description={error}
                onClose={clearError}
                className="mb-6"
              />
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <label htmlFor="usuario" className="block text-sm font-medium text-gray-700 mb-2">
                  Usuário
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-500" />
                  </div>
                  <Input
                    id="usuario"
                    type="text"
                    placeholder="Digite seu usuário"
                    className="pl-10"
                    {...register('usuario')}
                    error={errors.usuario?.message}
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="senha" className="block text-sm font-medium text-gray-700 mb-2">
                  Senha
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-500" />
                  </div>
                  <Input
                    id="senha"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Digite sua senha"
                    className="pl-10 pr-10"
                    {...register('senha')}
                    error={errors.senha?.message}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={togglePasswordVisibility}
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-500 hover:text-gray-700" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-500 hover:text-gray-700" />
                    )}
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="lembrarMe"
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    {...register('lembrarMe')}
                    disabled={isLoading}
                  />
                  <label htmlFor="lembrarMe" className="ml-2 block text-sm text-gray-700">
                    Lembrar-me
                  </label>
                </div>

                <div className="text-sm">
                  <a
                    href="/auth/forgot-password"
                    className="font-medium text-blue-600 hover:text-blue-500"
                  >
                    Esqueceu a senha?
                  </a>
                </div>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Entrando...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <LogIn className="h-5 w-5 mr-2" />
                    Entrar
                  </div>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            © 2025 RLPONTO. Todos os direitos reservados.
          </p>
        </div>
      </div>
    </div>
  );
};

