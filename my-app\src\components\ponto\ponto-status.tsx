'use client';

import { useState, useEffect } from 'react';
import { Clock, Calendar, User, CheckCircle, AlertCircle } from 'lucide-react';

interface PontoStatusData {
  funcionario: {
    nome: string;
    matricula: string;
    cargo: string;
  };
  statusAtual: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim' | 'finalizado';
  ultimoRegistro?: {
    tipo: string;
    horario: string;
    data: string;
  };
  proximoRegistro: string;
  horasTrabalhadas: string;
  saldoHoras: string;
}

export function PontoStatus() {
  const [status, setStatus] = useState<PontoStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Atualizar horário a cada segundo
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    fetchPontoStatus();
  }, []);

  const fetchPontoStatus = async () => {
    try {
      setLoading(true);
      
      // Simulação de dados (substituir por API real)
      const mockData: PontoStatusData = {
        funcionario: {
          nome: 'João Silva Santos',
          matricula: 'EMP001',
          cargo: 'Analista de Sistemas'
        },
        statusAtual: 'entrada',
        ultimoRegistro: {
          tipo: 'Entrada',
          horario: '08:00',
          data: new Date().toLocaleDateString('pt-BR')
        },
        proximoRegistro: 'Saída para Intervalo',
        horasTrabalhadas: '04:15',
        saldoHoras: '+00:15'
      };

      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStatus(mockData);
    } catch (error) {
      console.error('Erro ao buscar status do ponto:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (statusAtual: string) => {
    switch (statusAtual) {
      case 'entrada':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'saida':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'intervalo_inicio':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'intervalo_fim':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'finalizado':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (statusAtual: string) => {
    switch (statusAtual) {
      case 'entrada':
      case 'saida':
        return <CheckCircle className="h-5 w-5" />;
      case 'intervalo_inicio':
      case 'intervalo_fim':
        return <Clock className="h-5 w-5" />;
      default:
        return <AlertCircle className="h-5 w-5" />;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4" />
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2" />
            <div className="h-4 bg-gray-200 rounded w-1/3" />
            <div className="h-4 bg-gray-200 rounded w-1/4" />
          </div>
        </div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center text-gray-500">
          <AlertCircle className="h-12 w-12 mx-auto mb-4" />
          <p>Erro ao carregar status do ponto</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        {/* Header com horário atual */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <User className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Status do Ponto</h2>
              <p className="text-sm text-gray-600">{formatDate(currentTime)}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-gray-900">{formatTime(currentTime)}</div>
            <div className="text-sm text-gray-600">Horário atual</div>
          </div>
        </div>

        {/* Informações do funcionário */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Funcionário</div>
            <div className="text-lg font-semibold text-gray-900">{status.funcionario.nome}</div>
            <div className="text-sm text-gray-600">
              {status.funcionario.matricula} • {status.funcionario.cargo}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Horas Trabalhadas</div>
            <div className="text-lg font-semibold text-gray-900">{status.horasTrabalhadas}</div>
            <div className="text-sm text-gray-600">Hoje</div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Saldo de Horas</div>
            <div className={`text-lg font-semibold ${
              status.saldoHoras.startsWith('+') ? 'text-green-600' : 'text-red-600'
            }`}>
              {status.saldoHoras}
            </div>
            <div className="text-sm text-gray-600">Acumulado</div>
          </div>
        </div>

        {/* Status atual e próximo registro */}
        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${getStatusColor(status.statusAtual)}`}>
              {getStatusIcon(status.statusAtual)}
              <span className="text-sm font-medium">
                Próximo: {status.proximoRegistro}
              </span>
            </div>
          </div>
          
          {status.ultimoRegistro && (
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                Último: {status.ultimoRegistro.tipo}
              </div>
              <div className="text-sm text-gray-600">
                {status.ultimoRegistro.horario} - {status.ultimoRegistro.data}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
