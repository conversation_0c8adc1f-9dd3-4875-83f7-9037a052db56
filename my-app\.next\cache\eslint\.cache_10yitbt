[{"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Button.test.tsx": "3", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Button.tsx": "4", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\index.ts": "5", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Input.tsx": "6", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\constants\\index.ts": "7", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\index.ts": "8", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useDebounce.ts": "9", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useFetch.ts": "10", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useLocalStorage.ts": "11", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useToggle.ts": "12", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\types\\index.ts": "13", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\utils\\index.ts": "14", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\login\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\logout\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\dashboard\\page.tsx": "17", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\login\\page.tsx": "18", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\LoginForm.tsx": "19", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Alert.tsx": "20", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Card.tsx": "21", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useAuth.ts": "22", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\novo\\page.tsx": "23", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\page.tsx": "24", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx": "25", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\periodo-apuracao\\page.tsx": "26", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\biometrico\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\manual\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\analiticos\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\funcionario\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\periodo\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\funcionarios\\route.ts": "34", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\biometrico\\route.ts": "35", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\manual\\funcionarios\\route.ts": "36", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\manual\\route.ts": "37", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx": "38", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx": "39", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-header.tsx": "40", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-nav.tsx": "41", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionarios-filters.tsx": "42", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionarios-list.tsx": "43", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\funcionario-wizard.tsx": "44", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\progress-indicator.tsx": "45", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\step-confirmacao.tsx": "46", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\step-pessoal.tsx": "47", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\step-profissional.tsx": "48", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\alerts-panel.tsx": "49", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\employee-summary.tsx": "50", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\frequency-chart.tsx": "51", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\metrics-overview.tsx": "52", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\period-selector.tsx": "53", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\biometric-scanner.tsx": "54", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\historico-recente.tsx": "55", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\manual-form.tsx": "56", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\ponto-status.tsx": "57", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\analytics-preview.tsx": "58", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\analytics-templates.tsx": "59", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\quick-stats.tsx": "60", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\recent-reports.tsx": "61", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-form.tsx": "62", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-templates.tsx": "63", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\use-wizard.ts": "64", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\administracao\\page.tsx": "65", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\absenteismo\\page.tsx": "66", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\comparativos\\page.tsx": "67", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\page.tsx": "68", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\produtividade\\page.tsx": "69", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\tendencias\\page.tsx": "70", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\agendamentos\\page.tsx": "71", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\construtor\\page.tsx": "72", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\insights\\page.tsx": "73", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\callback\\credentials\\route.ts": "74", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "75", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\auth\\route-guard.tsx": "76", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\insights-engine.tsx": "77", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-builder.tsx": "78", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\schedule-manager.tsx": "79", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\SearchIcon.tsx": "80", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\lib\\auth.ts": "81", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\lib\\db.ts": "82", "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\providers\\session-provider.tsx": "83"}, {"size": 1437, "mtime": 1753652740531, "results": "84", "hashOfConfig": "85"}, {"size": 146, "mtime": 1753635218407, "results": "86", "hashOfConfig": "85"}, {"size": 1326, "mtime": 1753635216572, "results": "87", "hashOfConfig": "85"}, {"size": 2219, "mtime": 1753635216348, "results": "88", "hashOfConfig": "85"}, {"size": 236, "mtime": 1753635216208, "results": "89", "hashOfConfig": "85"}, {"size": 2197, "mtime": 1753635216528, "results": "90", "hashOfConfig": "85"}, {"size": 1418, "mtime": 1753635222308, "results": "91", "hashOfConfig": "85"}, {"size": 259, "mtime": 1753635214553, "results": "92", "hashOfConfig": "85"}, {"size": 408, "mtime": 1753635214413, "results": "93", "hashOfConfig": "85"}, {"size": 1701, "mtime": 1753635214734, "results": "94", "hashOfConfig": "85"}, {"size": 1259, "mtime": 1753635214465, "results": "95", "hashOfConfig": "85"}, {"size": 437, "mtime": 1753635214794, "results": "96", "hashOfConfig": "85"}, {"size": 3419, "mtime": 1753635218251, "results": "97", "hashOfConfig": "85"}, {"size": 3872, "mtime": 1753635218111, "results": "98", "hashOfConfig": "85"}, {"size": 1640, "mtime": 1753659004467, "results": "99", "hashOfConfig": "85"}, {"size": 1044, "mtime": 1753635218943, "results": "100", "hashOfConfig": "85"}, {"size": 200, "mtime": 1753635219260, "results": "101", "hashOfConfig": "85"}, {"size": 5412, "mtime": 1753662058148, "results": "102", "hashOfConfig": "85"}, {"size": 8285, "mtime": 1753656366087, "results": "103", "hashOfConfig": "85"}, {"size": 2731, "mtime": 1753635216152, "results": "104", "hashOfConfig": "85"}, {"size": 1875, "mtime": 1753635216400, "results": "105", "hashOfConfig": "85"}, {"size": 2152, "mtime": 1753635214665, "results": "106", "hashOfConfig": "85"}, {"size": 1635, "mtime": 1753635221761, "results": "107", "hashOfConfig": "85"}, {"size": 2941, "mtime": 1753635221816, "results": "108", "hashOfConfig": "85"}, {"size": 806, "mtime": 1753635219726, "results": "109", "hashOfConfig": "85"}, {"size": 5839, "mtime": 1753635219383, "results": "110", "hashOfConfig": "85"}, {"size": 4026, "mtime": 1753635219677, "results": "111", "hashOfConfig": "85"}, {"size": 5117, "mtime": 1753635219591, "results": "112", "hashOfConfig": "85"}, {"size": 175, "mtime": 1753635219465, "results": "113", "hashOfConfig": "85"}, {"size": 10281, "mtime": 1753635220955, "results": "114", "hashOfConfig": "85"}, {"size": 5716, "mtime": 1753635220525, "results": "115", "hashOfConfig": "85"}, {"size": 9796, "mtime": 1753635220165, "results": "116", "hashOfConfig": "85"}, {"size": 10454, "mtime": 1753635220850, "results": "117", "hashOfConfig": "85"}, {"size": 7632, "mtime": 1753635219148, "results": "118", "hashOfConfig": "85"}, {"size": 7049, "mtime": 1753635218811, "results": "119", "hashOfConfig": "85"}, {"size": 4271, "mtime": 1753635218731, "results": "120", "hashOfConfig": "85"}, {"size": 9462, "mtime": 1753663231546, "results": "121", "hashOfConfig": "85"}, {"size": 6109, "mtime": 1753635221938, "results": "122", "hashOfConfig": "85"}, {"size": 5677, "mtime": 1753635218335, "results": "123", "hashOfConfig": "85"}, {"size": 3847, "mtime": 1753635215960, "results": "124", "hashOfConfig": "85"}, {"size": 4755, "mtime": 1753635216026, "results": "125", "hashOfConfig": "85"}, {"size": 4460, "mtime": 1753635218014, "results": "126", "hashOfConfig": "85"}, {"size": 6411, "mtime": 1753635217935, "results": "127", "hashOfConfig": "85"}, {"size": 5665, "mtime": 1753635217832, "results": "128", "hashOfConfig": "85"}, {"size": 3797, "mtime": 1753635217705, "results": "129", "hashOfConfig": "85"}, {"size": 8754, "mtime": 1753635217656, "results": "130", "hashOfConfig": "85"}, {"size": 9678, "mtime": 1753635217791, "results": "131", "hashOfConfig": "85"}, {"size": 10930, "mtime": 1753635217880, "results": "132", "hashOfConfig": "85"}, {"size": 9467, "mtime": 1753635215421, "results": "133", "hashOfConfig": "85"}, {"size": 12838, "mtime": 1753635215250, "results": "134", "hashOfConfig": "85"}, {"size": 9471, "mtime": 1753635215181, "results": "135", "hashOfConfig": "85"}, {"size": 8746, "mtime": 1753635215362, "results": "136", "hashOfConfig": "85"}, {"size": 7208, "mtime": 1753635215301, "results": "137", "hashOfConfig": "85"}, {"size": 9229, "mtime": 1753635215708, "results": "138", "hashOfConfig": "85"}, {"size": 7777, "mtime": 1753635215616, "results": "139", "hashOfConfig": "85"}, {"size": 10508, "mtime": 1753635215866, "results": "140", "hashOfConfig": "85"}, {"size": 6978, "mtime": 1753635215527, "results": "141", "hashOfConfig": "85"}, {"size": 7367, "mtime": 1753635216739, "results": "142", "hashOfConfig": "85"}, {"size": 10519, "mtime": 1753635217442, "results": "143", "hashOfConfig": "85"}, {"size": 8410, "mtime": 1753635216659, "results": "144", "hashOfConfig": "85"}, {"size": 9741, "mtime": 1753635217118, "results": "145", "hashOfConfig": "85"}, {"size": 11032, "mtime": 1753635216928, "results": "146", "hashOfConfig": "85"}, {"size": 9699, "mtime": 1753635217307, "results": "147", "hashOfConfig": "85"}, {"size": 2174, "mtime": 1753635214608, "results": "148", "hashOfConfig": "85"}, {"size": 13350, "mtime": 1753635220028, "results": "149", "hashOfConfig": "85"}, {"size": 14100, "mtime": 1753635221534, "results": "150", "hashOfConfig": "85"}, {"size": 17946, "mtime": 1753635221325, "results": "151", "hashOfConfig": "85"}, {"size": 14779, "mtime": 1753635221094, "results": "152", "hashOfConfig": "85"}, {"size": 12084, "mtime": 1753635221641, "results": "153", "hashOfConfig": "85"}, {"size": 14481, "mtime": 1753635221193, "results": "154", "hashOfConfig": "85"}, {"size": 12490, "mtime": 1753635220739, "results": "155", "hashOfConfig": "85"}, {"size": 10170, "mtime": 1753635220430, "results": "156", "hashOfConfig": "85"}, {"size": 14533, "mtime": 1753635220291, "results": "157", "hashOfConfig": "85"}, {"size": 376, "mtime": 1753659736702, "results": "158", "hashOfConfig": "85"}, {"size": 161, "mtime": 1753662878948, "results": "159", "hashOfConfig": "85"}, {"size": 1299, "mtime": 1753644713628, "results": "160", "hashOfConfig": "85"}, {"size": 11923, "mtime": 1753635217026, "results": "161", "hashOfConfig": "85"}, {"size": 12459, "mtime": 1753635216827, "results": "162", "hashOfConfig": "85"}, {"size": 14221, "mtime": 1753635217497, "results": "163", "hashOfConfig": "85"}, {"size": 321, "mtime": 1753635216476, "results": "164", "hashOfConfig": "85"}, {"size": 2106, "mtime": 1753662866285, "results": "165", "hashOfConfig": "85"}, {"size": 284, "mtime": 1753662850967, "results": "166", "hashOfConfig": "85"}, {"size": 2569, "mtime": 1753652775114, "results": "167", "hashOfConfig": "85"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10b9bsu", {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Button.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useDebounce.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useFetch.ts", ["417"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useLocalStorage.ts", ["418"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useToggle.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\logout\\route.ts", ["419"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\useAuth.ts", ["420"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\novo\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\periodo-apuracao\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\biometrico\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\manual\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\analiticos\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\funcionario\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\periodo\\page.tsx", ["421"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\funcionarios\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\biometrico\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\manual\\funcionarios\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\ponto\\manual\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-header.tsx", ["422"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-nav.tsx", ["423"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionarios-filters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionarios-list.tsx", ["424", "425", "426", "427"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\funcionario-wizard.tsx", ["428"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\progress-indicator.tsx", ["429"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\step-confirmacao.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\step-pessoal.tsx", ["430", "431", "432", "433"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\step-profissional.tsx", ["434", "435", "436", "437", "438"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\alerts-panel.tsx", ["439"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\employee-summary.tsx", ["440"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\frequency-chart.tsx", ["441"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\metrics-overview.tsx", ["442"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\period-selector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\biometric-scanner.tsx", ["443", "444"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\historico-recente.tsx", ["445"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\manual-form.tsx", ["446", "447", "448", "449"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\ponto-status.tsx", ["450"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\analytics-preview.tsx", ["451", "452"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\analytics-templates.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\quick-stats.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\recent-reports.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-templates.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\hooks\\use-wizard.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\administracao\\page.tsx", ["453", "454"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\absenteismo\\page.tsx", ["455"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\comparativos\\page.tsx", ["456", "457"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\page.tsx", ["458"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\produtividade\\page.tsx", ["459"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\tendencias\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\agendamentos\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\construtor\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\insights\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\callback\\credentials\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\auth\\route-guard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\insights-engine.tsx", ["460"], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-builder.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\schedule-manager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\SearchIcon.tsx", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\providers\\session-provider.tsx", [], [], {"ruleId": "461", "severity": 1, "message": "462", "line": 62, "column": 6, "nodeType": "463", "endLine": 62, "endColumn": 11, "suggestions": "464"}, {"ruleId": "465", "severity": 1, "message": "466", "line": 1, "column": 20, "nodeType": null, "messageId": "467", "endLine": 1, "endColumn": 29}, {"ruleId": "465", "severity": 1, "message": "468", "line": 3, "column": 28, "nodeType": null, "messageId": "467", "endLine": 3, "endColumn": 36}, {"ruleId": "465", "severity": 1, "message": "469", "line": 44, "column": 14, "nodeType": null, "messageId": "467", "endLine": 44, "endColumn": 18}, {"ruleId": "465", "severity": 1, "message": "470", "line": 11, "column": 9, "nodeType": null, "messageId": "467", "endLine": 11, "endColumn": 21}, {"ruleId": "465", "severity": 1, "message": "471", "line": 4, "column": 10, "nodeType": null, "messageId": "467", "endLine": 4, "endColumn": 16}, {"ruleId": "465", "severity": 1, "message": "472", "line": 9, "column": 3, "nodeType": null, "messageId": "467", "endLine": 9, "endColumn": 11}, {"ruleId": "465", "severity": 1, "message": "473", "line": 11, "column": 3, "nodeType": null, "messageId": "467", "endLine": 11, "endColumn": 7}, {"ruleId": "465", "severity": 1, "message": "474", "line": 12, "column": 3, "nodeType": null, "messageId": "467", "endLine": 12, "endColumn": 9}, {"ruleId": "465", "severity": 1, "message": "475", "line": 13, "column": 3, "nodeType": null, "messageId": "467", "endLine": 13, "endColumn": 15}, {"ruleId": "461", "severity": 1, "message": "476", "line": 34, "column": 6, "nodeType": "463", "endLine": 34, "endColumn": 20, "suggestions": "477"}, {"ruleId": "465", "severity": 1, "message": "478", "line": 85, "column": 13, "nodeType": null, "messageId": "467", "endLine": 85, "endColumn": 19}, {"ruleId": "465", "severity": 1, "message": "479", "line": 45, "column": 17, "nodeType": null, "messageId": "467", "endLine": 45, "endColumn": 27}, {"ruleId": "465", "severity": 1, "message": "480", "line": 5, "column": 16, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 20}, {"ruleId": "465", "severity": 1, "message": "481", "line": 5, "column": 22, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 27}, {"ruleId": "465", "severity": 1, "message": "482", "line": 5, "column": 29, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 35}, {"ruleId": "461", "severity": 1, "message": "483", "line": 112, "column": 6, "nodeType": "463", "endLine": 112, "endColumn": 16, "suggestions": "484"}, {"ruleId": "465", "severity": 1, "message": "485", "line": 5, "column": 21, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 29}, {"ruleId": "465", "severity": 1, "message": "486", "line": 5, "column": 31, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 36}, {"ruleId": "465", "severity": 1, "message": "487", "line": 5, "column": 38, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 48}, {"ruleId": "465", "severity": 1, "message": "488", "line": 81, "column": 9, "nodeType": null, "messageId": "467", "endLine": 81, "endColumn": 23}, {"ruleId": "461", "severity": 1, "message": "483", "line": 92, "column": 6, "nodeType": "463", "endLine": 92, "endColumn": 16, "suggestions": "489"}, {"ruleId": "465", "severity": 1, "message": "490", "line": 144, "column": 9, "nodeType": null, "messageId": "467", "endLine": 144, "endColumn": 20}, {"ruleId": "465", "severity": 1, "message": "491", "line": 10, "column": 3, "nodeType": null, "messageId": "467", "endLine": 10, "endColumn": 14}, {"ruleId": "461", "severity": 1, "message": "492", "line": 29, "column": 6, "nodeType": "463", "endLine": 29, "endColumn": 14, "suggestions": "493"}, {"ruleId": "465", "severity": 1, "message": "494", "line": 7, "column": 3, "nodeType": null, "messageId": "467", "endLine": 7, "endColumn": 13}, {"ruleId": "465", "severity": 1, "message": "466", "line": 3, "column": 20, "nodeType": null, "messageId": "467", "endLine": 3, "endColumn": 29}, {"ruleId": "465", "severity": 1, "message": "495", "line": 102, "column": 14, "nodeType": null, "messageId": "467", "endLine": 102, "endColumn": 19}, {"ruleId": "465", "severity": 1, "message": "496", "line": 31, "column": 10, "nodeType": null, "messageId": "467", "endLine": 31, "endColumn": 17}, {"ruleId": "465", "severity": 1, "message": "497", "line": 4, "column": 18, "nodeType": null, "messageId": "467", "endLine": 4, "endColumn": 23}, {"ruleId": "465", "severity": 1, "message": "498", "line": 9, "column": 3, "nodeType": null, "messageId": "467", "endLine": 9, "endColumn": 9}, {"ruleId": "465", "severity": 1, "message": "496", "line": 50, "column": 10, "nodeType": null, "messageId": "467", "endLine": 50, "endColumn": 17}, {"ruleId": "465", "severity": 1, "message": "495", "line": 150, "column": 14, "nodeType": null, "messageId": "467", "endLine": 150, "endColumn": 19}, {"ruleId": "465", "severity": 1, "message": "472", "line": 4, "column": 17, "nodeType": null, "messageId": "467", "endLine": 4, "endColumn": 25}, {"ruleId": "465", "severity": 1, "message": "499", "line": 6, "column": 3, "nodeType": null, "messageId": "467", "endLine": 6, "endColumn": 11}, {"ruleId": "465", "severity": 1, "message": "500", "line": 8, "column": 3, "nodeType": null, "messageId": "467", "endLine": 8, "endColumn": 8}, {"ruleId": "465", "severity": 1, "message": "486", "line": 11, "column": 3, "nodeType": null, "messageId": "467", "endLine": 11, "endColumn": 8}, {"ruleId": "465", "severity": 1, "message": "485", "line": 12, "column": 3, "nodeType": null, "messageId": "467", "endLine": 12, "endColumn": 11}, {"ruleId": "465", "severity": 1, "message": "500", "line": 5, "column": 3, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 8}, {"ruleId": "465", "severity": 1, "message": "500", "line": 5, "column": 3, "nodeType": null, "messageId": "467", "endLine": 5, "endColumn": 8}, {"ruleId": "465", "severity": 1, "message": "472", "line": 7, "column": 3, "nodeType": null, "messageId": "467", "endLine": 7, "endColumn": 11}, {"ruleId": "465", "severity": 1, "message": "501", "line": 2, "column": 10, "nodeType": null, "messageId": "467", "endLine": 2, "endColumn": 18}, {"ruleId": "465", "severity": 1, "message": "472", "line": 9, "column": 3, "nodeType": null, "messageId": "467", "endLine": 9, "endColumn": 11}, {"ruleId": "465", "severity": 1, "message": "502", "line": 8, "column": 3, "nodeType": null, "messageId": "467", "endLine": 8, "endColumn": 11}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["503"], "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "unusedVar", "'_request' is defined but never used.", "'_err' is defined but never used.", "'reportConfig' is assigned a value but never used.", "'Button' is defined but never used.", "'Calendar' is defined but never used.", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'MoreVertical' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFuncionarios'. Either include it or remove the dependency array.", ["504"], "'result' is assigned a value but never used.", "'isUpcoming' is assigned a value but never used.", "'Mail' is defined but never used.", "'Phone' is defined but never used.", "'MapPin' is defined but never used.", "React Hook useEffect has a missing dependency: 'validateForm'. Either include it or remove the dependency array.", ["505"], "'Building' is defined but never used.", "'Clock' is defined but never used.", "'DollarSign' is defined but never used.", "'formatCurrency' is assigned a value but never used.", ["506"], "'getTipoIcon' is assigned a value but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFrequencyData'. Either include it or remove the dependency array.", ["507"], "'TrendingUp' is defined but never used.", "'error' is defined but never used.", "'mounted' is assigned a value but never used.", "'Input' is defined but never used.", "'Camera' is defined but never used.", "'PieChart' is defined but never used.", "'Users' is defined but never used.", "'Suspense' is defined but never used.", "'Settings' is defined but never used.", {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, {"desc": "512", "fix": "513"}, {"desc": "512", "fix": "514"}, {"desc": "515", "fix": "516"}, "Update the dependencies array to be: [fetchData, url]", {"range": "517", "text": "518"}, "Update the dependencies array to be: [fetchFuncionarios, searchParams]", {"range": "519", "text": "520"}, "Update the dependencies array to be: [formData, validateForm]", {"range": "521", "text": "522"}, {"range": "523", "text": "522"}, "Update the dependencies array to be: [fetchFrequencyData, period]", {"range": "524", "text": "525"}, [1563, 1568], "[fetchData, url]", [783, 797], "[fetchFuncionarios, searchParams]", [3390, 3400], "[formData, validateForm]", [2721, 2731], [666, 674], "[fetchFrequencyData, period]"]