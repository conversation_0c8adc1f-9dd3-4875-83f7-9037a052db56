'use client';

import { useState } from 'react';
import { Button, Input } from '@/components/ui';
import { 
  FileText, 
  Download, 
  Loader2, 
  <PERSON><PERSON>ircle,
  Setting<PERSON>,
  Eye,
  Calendar
} from 'lucide-react';

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'select' | 'checkbox' | 'radio' | 'daterange';
  required?: boolean;
  placeholder?: string;
  description?: string;
  defaultValue?: unknown;
  options?: { value: string; label: string; description?: string }[];
}

interface ReportSection {
  title: string;
  description: string;
  included: boolean;
  optional?: boolean;
}

interface ReportConfig {
  type: string;
  title: string;
  description: string;
  color: string;
  fields: FormField[];
  sections: ReportSection[];
}

interface ReportFormProps {
  config: ReportConfig;
}

export function ReportForm({ config }: ReportFormProps) {
  const [formData, setFormData] = useState<Record<string, unknown>>(() => {
    const initial: Record<string, unknown> = {};
    config.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        initial[field.name] = field.defaultValue;
      }
    });
    return initial;
  });

  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (name: string, value: unknown) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    config.fields.forEach(field => {
      if (field.required && !formData[field.name]) {
        newErrors[field.name] = `${field.label} é obrigatório`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (action: 'generate' | 'preview') => {
    if (!validateForm()) return;

    setLoading(true);
    
    try {
      // Simular geração do relatório
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      if (action === 'preview') {
        setPreview(true);
      } else {
        // Simular download
        console.log('Gerando relatório:', { config: config.type, data: formData });
        alert('Relatório gerado com sucesso! O download iniciará em breve.');
      }
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderField = (field: FormField) => {
    switch (field.type) {
      case 'select':
        return (
          <select
            value={formData[field.name] || ''}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors[field.name] ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">{field.placeholder}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <label className="flex items-start space-x-3">
            <input
              type="checkbox"
              checked={formData[field.name] || false}
              onChange={(e) => handleInputChange(field.name, e.target.checked)}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div>
              <span className="text-sm font-medium text-gray-900">{field.label}</span>
              {field.description && (
                <p className="text-sm text-gray-600">{field.description}</p>
              )}
            </div>
          </label>
        );

      case 'radio':
        return (
          <div className="space-y-3">
            {field.options?.map(option => (
              <label key={option.value} className="flex items-start space-x-3">
                <input
                  type="radio"
                  name={field.name}
                  value={option.value}
                  checked={formData[field.name] === option.value}
                  onChange={(e) => handleInputChange(field.name, e.target.value)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div>
                  <span className="text-sm font-medium text-gray-900">{option.label}</span>
                  {option.description && (
                    <p className="text-sm text-gray-600">{option.description}</p>
                  )}
                </div>
              </label>
            ))}
          </div>
        );

      case 'daterange':
        return (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-600 mb-1">Data Inicial</label>
              <input
                type="date"
                value={formData[field.name]?.start?.toISOString().split('T')[0] || ''}
                onChange={(e) => handleInputChange(field.name, {
                  ...formData[field.name],
                  start: new Date(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">Data Final</label>
              <input
                type="date"
                value={formData[field.name]?.end?.toISOString().split('T')[0] || ''}
                onChange={(e) => handleInputChange(field.name, {
                  ...formData[field.name],
                  end: new Date(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        );

      default:
        return (
          <Input
            type={field.type}
            placeholder={field.placeholder}
            value={formData[field.name] || ''}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            error={errors[field.name]}
          />
        );
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-xl font-semibold text-gray-900">{config.title}</h2>
        <p className="text-gray-600">{config.description}</p>
      </div>

      {/* Formulário */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Campos do formulário */}
        <div className="lg:col-span-2 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {config.fields.map(field => (
              <div key={field.name} className={field.type === 'radio' ? 'md:col-span-2' : ''}>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderField(field)}
                {errors[field.name] && (
                  <p className="mt-1 text-sm text-red-600">{errors[field.name]}</p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Seções do relatório */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Seções do Relatório</h3>
          </div>
          
          <div className="space-y-3">
            {config.sections.map((section, index) => (
              <div key={index} className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{section.title}</h4>
                    <p className="text-xs text-gray-600 mt-1">{section.description}</p>
                  </div>
                  <div className="ml-3">
                    {section.included ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <div className="h-4 w-4 border border-gray-300 rounded" />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Ações */}
      <div className="border-t border-gray-200 pt-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <Calendar className="h-4 w-4 inline mr-1" />
            Tempo estimado: 2-3 minutos
          </div>
          
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => handleSubmit('preview')}
              disabled={loading}
            >
              <Eye className="h-4 w-4 mr-2" />
              Visualizar
            </Button>
            
            <Button
              variant="primary"
              onClick={() => handleSubmit('generate')}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Gerando...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Gerar Relatório
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Preview */}
      {preview && (
        <div className="border-t border-gray-200 pt-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <h4 className="font-medium text-blue-900">Preview do Relatório</h4>
            </div>
            <p className="text-sm text-blue-700">
              O relatório será gerado com os parâmetros selecionados. 
              Clique em &quot;Gerar Relatório&quot; para fazer o download.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

