'use client';

import { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Clock, 
  TrendingUp,
  Calendar,
  Users,
  BarChart3
} from 'lucide-react';

interface QuickStatsData {
  totalRelatorios: number;
  relatoriosHoje: number;
  downloadsMes: number;
  tempoMedioGeracao: number;
  relatoriosAgendados: number;
  funcionariosAtivos: number;
}

export function QuickStats() {
  const [stats, setStats] = useState<QuickStatsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchQuickStats();
  }, []);

  const fetchQuickStats = async () => {
    try {
      setLoading(true);
      
      // Simulação de dados (substituir por API real)
      const mockData: QuickStatsData = {
        totalRelatorios: 1247,
        relatoriosHoje: 23,
        downloadsMes: 156,
        tempoMedioGeracao: 3.2,
        relatoriosAgendados: 8,
        funcionariosAtivos: 8
      };

      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 800));
      
      setStats(mockData);
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded" />
              <div className="w-16 h-4 bg-gray-200 rounded" />
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded mb-2" />
            <div className="w-24 h-4 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: 'Relatórios Gerados',
      value: stats.totalRelatorios.toLocaleString(),
      subtitle: 'Total histórico',
      icon: FileText,
      color: 'blue',
      trend: '+12%',
      trendUp: true
    },
    {
      title: 'Gerados Hoje',
      value: stats.relatoriosHoje,
      subtitle: 'Relatórios hoje',
      icon: Clock,
      color: 'green',
      trend: '+5',
      trendUp: true
    },
    {
      title: 'Downloads do Mês',
      value: stats.downloadsMes,
      subtitle: 'Arquivos baixados',
      icon: Download,
      color: 'purple',
      trend: '+8%',
      trendUp: true
    },
    {
      title: 'Tempo Médio',
      value: `${stats.tempoMedioGeracao}s`,
      subtitle: 'Geração de relatório',
      icon: TrendingUp,
      color: 'orange',
      trend: '-0.5s',
      trendUp: true
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-600',
      green: 'bg-green-100 text-green-600',
      purple: 'bg-purple-100 text-purple-600',
      orange: 'bg-orange-100 text-orange-600',
      red: 'bg-red-100 text-red-600'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getTrendColor = (trendUp: boolean) => {
    return trendUp ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Cards Principais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 rounded-lg ${getColorClasses(card.color)}`}>
                <card.icon className="h-6 w-6" />
              </div>
              <span className={`text-sm font-medium ${getTrendColor(card.trendUp)}`}>
                {card.trend}
              </span>
            </div>
            
            <div className="space-y-1">
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-sm text-gray-600">{card.subtitle}</p>
              <p className="text-xs font-medium text-gray-900">{card.title}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Métricas Adicionais */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-indigo-100 text-indigo-600 rounded-lg">
              <Calendar className="h-5 w-5" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Relatórios Agendados</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Ativos</span>
              <span className="text-lg font-bold text-gray-900">{stats.relatoriosAgendados}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Próxima execução</span>
              <span className="text-sm text-gray-900">Amanhã 08:00</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-indigo-600 h-2 rounded-full" 
                style={{ width: `${(stats.relatoriosAgendados / 10) * 100}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-teal-100 text-teal-600 rounded-lg">
              <Users className="h-5 w-5" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Cobertura de Funcionários</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Funcionários ativos</span>
              <span className="text-lg font-bold text-gray-900">{stats.funcionariosAtivos}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Com relatórios</span>
              <span className="text-sm text-gray-900">{stats.funcionariosAtivos}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-teal-600 h-2 rounded-full" 
                style={{ width: '100%' }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Resumo de Performance */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 text-blue-600 rounded-lg">
              <BarChart3 className="h-6 w-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Performance do Sistema</h3>
              <p className="text-sm text-gray-600">Métricas de eficiência dos relatórios</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-green-600">98.5%</div>
            <div className="text-sm text-gray-600">Taxa de sucesso</div>
          </div>
        </div>
        
        <div className="mt-4 grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              {stats.tempoMedioGeracao}s
            </div>
            <div className="text-xs text-gray-600">Tempo médio</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              {((stats.downloadsMes / stats.totalRelatorios) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-gray-600">Taxa de download</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              {(stats.relatoriosHoje / stats.funcionariosAtivos).toFixed(1)}
            </div>
            <div className="text-xs text-gray-600">Relatórios/funcionário</div>
          </div>
        </div>
      </div>
    </div>
  );
}
