(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[392],{262:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.bind(a,7616)),Promise.resolve().then(a.bind(a,8427)),Promise.resolve().then(a.bind(a,5669)),Promise.resolve().then(a.bind(a,948)),Promise.resolve().then(a.bind(a,4035)),Promise.resolve().then(a.bind(a,9829))},948:(e,s,a)=>{"use strict";a.d(s,{MetricsOverview:()=>m});var t=a(5155),r=a(2115),l=a(7580),n=a(646),i=a(4186),c=a(1243);let d=(0,a(9946).A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var o=a(9074),x=a(4861);function m(e){let{period:s}=e,[a,m]=(0,r.useState)(null),[u,h]=(0,r.useState)(!0);(0,r.useEffect)(()=>{g()},[s]);let g=async()=>{try{h(!0),await new Promise(e=>setTimeout(e,1e3)),m({totalFuncionarios:8,funcionariosAtivos:7,horasTrabalhadasTotal:1344,horasExtrasTotal:45,frequenciaMedia:96.5,alertasTotal:3,registrosPendentes:2,diasUteis:22,diasTrabalhados:21,absenteismo:3.5})}catch(e){console.error("Erro ao buscar m\xe9tricas:",e)}finally{h(!1)}};if(u||!a)return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 animate-pulse",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"w-16 h-4 bg-gray-200 rounded"})]}),(0,t.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded mb-2"}),(0,t.jsx)("div",{className:"w-24 h-4 bg-gray-200 rounded"})]},s))});let p=[{title:"Funcion\xe1rios Ativos",value:a.funcionariosAtivos,total:a.totalFuncionarios,icon:l.A,color:"blue",trend:"+2.5%",description:"funcion\xe1rios trabalhando"},{title:"Frequ\xeancia M\xe9dia",value:"".concat(a.frequenciaMedia,"%"),icon:n.A,color:"green",trend:"+1.2%",description:"de presen\xe7a no per\xedodo"},{title:"Horas Trabalhadas",value:"".concat(a.horasTrabalhadasTotal,"h"),icon:i.A,color:"purple",trend:"+5.8%",description:"total no per\xedodo"},{title:"Alertas Pendentes",value:a.alertasTotal,icon:c.A,color:"red",trend:"-12%",description:"inconsist\xeancias detectadas"}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,s)=>{var a;return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg ".concat((e=>{let s={blue:"bg-blue-100 text-blue-600",green:"bg-green-100 text-green-600",purple:"bg-purple-100 text-purple-600",red:"bg-red-100 text-red-600",yellow:"bg-yellow-100 text-yellow-600"};return s[e]||s.blue})(e.color)),children:(0,t.jsx)(e.icon,{className:"h-6 w-6"})}),(0,t.jsx)("span",{className:"text-sm font-medium ".concat((a=e.trend).startsWith("+")?"text-green-600":a.startsWith("-")?"text-red-600":"text-gray-600"),children:e.trend})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:e.value}),e.total&&(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["/ ",e.total]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsx)("p",{className:"text-xs font-medium text-gray-900",children:e.title})]})]},s)})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 text-blue-600 rounded-lg",children:(0,t.jsx)(d,{className:"h-5 w-5"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Horas Extras"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Total do per\xedodo"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[a.horasExtrasTotal,"h"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"M\xe9dia por funcion\xe1rio"}),(0,t.jsxs)("span",{className:"text-sm text-gray-900",children:[(a.horasExtrasTotal/a.funcionariosAtivos).toFixed(1),"h"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(Math.min(a.horasExtrasTotal/100*100,100),"%")}})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 text-green-600 rounded-lg",children:(0,t.jsx)(o.A,{className:"h-5 w-5"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dias Trabalhados"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Dias \xfateis"}),(0,t.jsx)("span",{className:"text-lg font-bold text-gray-900",children:a.diasUteis})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Trabalhados"}),(0,t.jsx)("span",{className:"text-sm text-gray-900",children:a.diasTrabalhados})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(a.diasTrabalhados/a.diasUteis*100,"%")}})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 text-yellow-600 rounded-lg",children:(0,t.jsx)(x.A,{className:"h-5 w-5"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Absente\xedsmo"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Taxa do per\xedodo"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[a.absenteismo,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Registros pendentes"}),(0,t.jsx)("span",{className:"text-sm text-gray-900",children:a.registrosPendentes})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-yellow-600 h-2 rounded-full",style:{width:"".concat(Math.min(10*a.absenteismo,100),"%")}})})]})]})]})]})}},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1243:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1788:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2355:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2713:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3052:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3109:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3769:(e,s,a)=>{"use strict";a.d(s,{$n:()=>l,pd:()=>n,WI:()=>x.SearchIcon});var t=a(5155);a(2115);var r=a(4001);let l=e=>{let{children:s,className:a,variant:l="primary",size:n="md",disabled:i=!1,loading:c=!1,type:d="button",onClick:o,...x}=e;return(0,t.jsxs)("button",{type:d,className:(0,r.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[l],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[n],a),disabled:i||c,onClick:o,...x,children:[c&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s]})},n=e=>{let{className:s,type:a="text",placeholder:l,value:n,defaultValue:i,disabled:c=!1,required:d=!1,error:o,label:x,id:m,name:u,onChange:h,onBlur:g,onFocus:p,...j}=e,v=m||u;return(0,t.jsxs)("div",{className:"w-full",children:[x&&(0,t.jsxs)("label",{htmlFor:v,className:"block text-sm font-medium text-gray-700 mb-1",children:[x,d&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("input",{type:a,id:v,name:u,style:{color:"#000000",backgroundColor:"#ffffff",fontSize:"16px",fontWeight:"600"},className:(0,r.cn)("flex h-12 w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-base font-semibold placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",o&&"border-red-500 focus:ring-red-500 focus:border-red-500",s),placeholder:l,value:n,defaultValue:i,disabled:c,required:d,onChange:h,onBlur:g,onFocus:p,...j}),o&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]})};var i=a(646),c=a(4861),d=a(5339),o=a(1284);i.A,c.A,d.A,o.A;var x=a(9829)},4001:(e,s,a)=>{"use strict";a.d(s,{cn:()=>l});var t=a(2596),r=a(9688);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}},4035:(e,s,a)=>{"use strict";a.d(s,{PeriodSelector:()=>x});var t=a(5155),r=a(2115),l=a(5695),n=a(3769),i=a(2355),c=a(9074),d=a(6474),o=a(3052);function x(e){let{currentPeriod:s}=e,a=(0,l.useRouter)(),x=(0,l.useSearchParams)(),[m,u]=(0,r.useState)(!1),h=["Janeiro","Fevereiro","Mar\xe7o","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],g=Array.from({length:5},(e,s)=>new Date().getFullYear()-2+s),p=(e,s)=>{let t=new URLSearchParams(x);t.set("ano",e.toString()),t.set("mes",s.toString()),a.push("/periodo-apuracao?".concat(t.toString())),u(!1)},j=e=>{let a=s.ano,t=s.mes;"prev"===e?--t<1&&(t=12,a--):++t>12&&(t=1,a++),p(a,t)},v=()=>{let e=new Date;return s.ano===e.getFullYear()&&s.mes===e.getMonth()+1};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$n,{variant:"outline",size:"sm",onClick:()=>j("prev"),children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(n.$n,{variant:"outline",onClick:()=>u(!m),className:"min-w-[180px] justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[h[s.mes-1]," ",s.ano]}),v()&&(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:"Atual"})]}),(0,t.jsx)(d.A,{className:"h-4 w-4"})]}),m&&(0,t.jsx)("div",{className:"absolute top-full mt-2 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[300px]",children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ano"}),(0,t.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:g.map(e=>(0,t.jsx)("button",{onClick:()=>p(e,s.mes),className:"w-full text-left px-3 py-2 rounded text-sm transition-colors ".concat(e===s.ano?"bg-blue-100 text-blue-900 font-medium":"hover:bg-gray-100"),children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xeas"}),(0,t.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:h.map((e,a)=>{let r=a+1,l=new Date().getFullYear()===s.ano&&new Date().getMonth()+1===r;return(0,t.jsxs)("button",{onClick:()=>p(s.ano,r),className:"w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center justify-between ".concat(r===s.mes?"bg-blue-100 text-blue-900 font-medium":"hover:bg-gray-100"),children:[(0,t.jsx)("span",{children:e}),l&&(0,t.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-1 rounded",children:"Atual"})]},r)})})]})]}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(n.$n,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;p(e.getFullYear(),e.getMonth()+1)},className:"flex-1",children:"M\xeas Atual"}),(0,t.jsx)(n.$n,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setMonth(e.getMonth()-1),p(e.getFullYear(),e.getMonth()+1)},className:"flex-1",children:"M\xeas Anterior"})]})})]})})]}),(0,t.jsx)(n.$n,{variant:"outline",size:"sm",onClick:()=>j("next"),disabled:v(),children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})]}),m&&(0,t.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>u(!1)})]})}},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5669:(e,s,a)=>{"use strict";a.d(s,{FrequencyChart:()=>c});var t=a(5155),r=a(2115),l=a(2713),n=a(3109),i=a(8500);function c(e){let{period:s}=e,[a,c]=(0,r.useState)([]),[d,o]=(0,r.useState)(!0),[x,m]=(0,r.useState)("presenca");(0,r.useEffect)(()=>{u()},[s]);let u=async()=>{try{o(!0);let e=new Date(s.ano,s.mes,0).getDate(),a=[],t=["Dom","Seg","Ter","Qua","Qui","Sex","S\xe1b"];for(let r=1;r<=e;r++){let e=new Date(s.ano,s.mes-1,r),l=t[e.getDay()];if(0===e.getDay()||6===e.getDay())a.push({dia:r,presentes:0,ausentes:0,atrasados:0,horasExtras:0,diaSemana:l});else{let e=r%7;a.push({dia:r,presentes:8-e%2,ausentes:e%2,atrasados:e%3,horasExtras:e%10+2,diaSemana:l})}}await new Promise(e=>setTimeout(e,1e3)),c(a)}catch(e){console.error("Erro ao buscar dados de frequ\xeancia:",e)}finally{o(!1)}};if(d)return(0,t.jsx)("div",{className:"h-80 bg-gray-200 rounded animate-pulse flex items-center justify-center",children:(0,t.jsx)(l.A,{className:"h-8 w-8 text-gray-400"})});let h="presenca"===x?Math.max(...a.map(e=>Math.max(e.presentes,e.ausentes,e.atrasados))):Math.max(...a.map(e=>e.horasExtras)),g=e=>h>0?e/h*100:0,p=a.reduce((e,s)=>e+s.presentes,0),j=a.reduce((e,s)=>e+s.ausentes,0),v=a.reduce((e,s)=>e+s.atrasados,0),b=a.reduce((e,s)=>e+s.horasExtras,0),f=p+j>0?p/(p+j)*100:0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>m("presenca"),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat("presenca"===x?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"),children:"Presen\xe7a"}),(0,t.jsx)("button",{onClick:()=>m("horas"),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat("horas"===x?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"),children:"Horas Extras"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4 text-sm",children:"presenca"===x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,t.jsx)("span",{children:"Presentes"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded"}),(0,t.jsx)("span",{children:"Ausentes"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded"}),(0,t.jsx)("span",{children:"Atrasados"})]})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,t.jsx)("span",{children:"Horas Extras"})]})})]}),(0,t.jsx)("div",{className:"relative h-64 border-b border-l border-gray-200",children:(0,t.jsx)("div",{className:"absolute inset-0 flex items-end justify-between px-2",children:a.map((e,s)=>(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-1 flex-1 max-w-[30px]",children:[(0,t.jsx)("div",{className:"relative w-full flex flex-col items-center justify-end h-56",children:"presenca"===x?(0,t.jsxs)("div",{className:"w-full flex flex-col items-center justify-end space-y-1",children:[e.presentes>0&&(0,t.jsx)("div",{className:"w-full bg-green-500 rounded-t",style:{height:"".concat(g(e.presentes),"%")},title:"".concat(e.presentes," presentes")}),e.atrasados>0&&(0,t.jsx)("div",{className:"w-full bg-yellow-500",style:{height:"".concat(g(e.atrasados),"%")},title:"".concat(e.atrasados," atrasados")}),e.ausentes>0&&(0,t.jsx)("div",{className:"w-full bg-red-500 rounded-b",style:{height:"".concat(g(e.ausentes),"%")},title:"".concat(e.ausentes," ausentes")})]}):(0,t.jsx)("div",{className:"w-full bg-blue-500 rounded-t",style:{height:"".concat(g(e.horasExtras),"%")},title:"".concat(e.horasExtras,"h extras")})}),(0,t.jsxs)("div",{className:"text-xs text-center",children:[(0,t.jsx)("div",{className:"font-medium",children:e.dia}),(0,t.jsx)("div",{className:"text-gray-500",children:e.diaSemana})]})]},s))})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-700",children:p}),(0,t.jsx)("div",{className:"text-sm text-green-600",children:"Total Presentes"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-700",children:j}),(0,t.jsx)("div",{className:"text-sm text-red-600",children:"Total Ausentes"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-700",children:v}),(0,t.jsx)("div",{className:"text-sm text-yellow-600",children:"Total Atrasados"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-700",children:[b,"h"]}),(0,t.jsx)("div",{className:"text-sm text-blue-600",children:"Horas Extras"})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Frequ\xeancia Geral do Per\xedodo"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[f>=95?(0,t.jsx)(n.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(i.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(f>=95?"text-green-600":"text-red-600"),children:[f.toFixed(1),"%"]})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(f>=95?"bg-green-600":"bg-red-600"),style:{width:"".concat(f,"%")}})})]})]})}},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},6474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7616:(e,s,a)=>{"use strict";a.d(s,{AlertsPanel:()=>x});var t=a(5155),r=a(2115),l=a(4861),n=a(1243),i=a(1284);a(4186);var c=a(1007),d=a(646),o=a(9074);function x(e){let{period:s}=e,[a,x]=(0,r.useState)([]),[m,u]=(0,r.useState)(!0),[h,g]=(0,r.useState)("todos");(0,r.useEffect)(()=>{p()},[s]);let p=async()=>{try{u(!0);let e=[{id:"1",tipo:"inconsistencia",severidade:"alta",funcionario:{nome:"Jo\xe3o Silva Santos",matricula:"EMP001"},titulo:"Registro de sa\xedda sem entrada",descricao:"Funcion\xe1rio registrou sa\xedda \xe0s 17:30 sem ter registrado entrada no dia 15/01",data:"15/01/2024",status:"pendente",criadoEm:new Date},{id:"2",tipo:"horas_extras",severidade:"media",funcionario:{nome:"Maria Oliveira Costa",matricula:"EMP002"},titulo:"Excesso de horas extras",descricao:"Funcion\xe1rio acumulou 25h extras no per\xedodo, acima do limite de 20h",data:"20/01/2024",status:"pendente",criadoEm:new Date},{id:"3",tipo:"aprovacao",severidade:"baixa",funcionario:{nome:"Carlos Roberto Lima",matricula:"EMP003"},titulo:"Ponto manual pendente",descricao:"Registro manual de entrada aguardando aprova\xe7\xe3o h\xe1 2 dias",data:"18/01/2024",status:"pendente",criadoEm:new Date},{id:"4",tipo:"atraso",severidade:"baixa",funcionario:{nome:"Ana Paula Silva",matricula:"EMP004"},titulo:"Atrasos recorrentes",descricao:"Funcion\xe1rio apresentou 5 atrasos nos \xfaltimos 10 dias",data:"22/01/2024",status:"resolvido",criadoEm:new Date}];await new Promise(e=>setTimeout(e,800)),x(e)}catch(e){console.error("Erro ao buscar alertas:",e)}finally{u(!1)}},j=a.filter(e=>"todos"===h||e.status===h),v={total:a.length,pendente:a.filter(e=>"pendente"===e.status).length,resolvido:a.filter(e=>"resolvido"===e.status).length,alta:a.filter(e=>"alta"===e.severidade).length};return m?(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded animate-pulse",children:[(0,t.jsx)("div",{className:"w-5 h-5 bg-gray-200 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"w-3/4 h-4 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"w-1/2 h-3 bg-gray-200 rounded"})]})]},s))}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-red-700",children:v.pendente}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"Pendentes"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-green-700",children:v.resolvido}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"Resolvidos"})]})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:["todos","pendente","resolvido"].map(e=>(0,t.jsx)("button",{onClick:()=>g(e),className:"px-3 py-1 text-xs font-medium rounded-full transition-colors ".concat(h===e?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"),children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),(0,t.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:0===j.length?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-sm",children:"Nenhum alerta encontrado"})]}):j.map(e=>(0,t.jsx)("div",{className:"p-3 border rounded-lg transition-colors ".concat("pendente"===e.status?"border-red-200 bg-red-50":"resolvido"===e.status?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"p-1 rounded ".concat((e=>{switch(e){case"alta":return"text-red-600 bg-red-100";case"media":return"text-yellow-600 bg-yellow-100";case"baixa":return"text-blue-600 bg-blue-100";default:return"text-gray-600 bg-gray-100"}})(e.severidade)),children:(e=>{switch(e){case"alta":return(0,t.jsx)(l.A,{className:"h-4 w-4"});case"media":return(0,t.jsx)(n.A,{className:"h-4 w-4"});default:return(0,t.jsx)(i.A,{className:"h-4 w-4"})}})(e.severidade)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.titulo}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("pendente"===e.status?"bg-red-100 text-red-800":"resolvido"===e.status?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:"pendente"===e.status?"Pendente":"resolvido"===e.status?"Resolvido":"Ignorado"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.descricao}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.funcionario.nome}),(0,t.jsxs)("span",{children:["(",e.funcionario.matricula,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.data})]})]})]})]})},e.id))}),v.pendente>0&&(0,t.jsx)("div",{className:"pt-3 border-t border-gray-200",children:(0,t.jsxs)("button",{className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:["Ver todos os alertas (",v.total,")"]})})]})}},8427:(e,s,a)=>{"use strict";a.d(s,{EmployeeSummary:()=>m});var t=a(5155),r=a(2115),l=a(1788),n=a(3109),i=a(8500),c=a(1007),d=a(4186),o=a(1243),x=a(3769);function m(e){let{period:s}=e,[a,m]=(0,r.useState)([]),[u,h]=(0,r.useState)(!0),[g,p]=(0,r.useState)(""),[j,v]=(0,r.useState)("nome"),[b,f]=(0,r.useState)("asc");(0,r.useEffect)(()=>{y()},[s]);let y=async()=>{try{h(!0),await new Promise(e=>setTimeout(e,1e3)),m([{funcionario:{id:"EMP001",nome:"Jo\xe3o Silva Santos",matricula:"EMP001",cargo:"Analista de Sistemas",setor:"TI"},horasTrabalhadas:168,horasExtras:12,diasPresentes:21,diasAusentes:1,atrasos:2,frequencia:95.5,saldoHoras:4,alertas:1,status:"regular"},{funcionario:{id:"EMP002",nome:"Maria Oliveira Costa",matricula:"EMP002",cargo:"Gerente de Vendas",setor:"Vendas"},horasTrabalhadas:176,horasExtras:25,diasPresentes:22,diasAusentes:0,atrasos:0,frequencia:100,saldoHoras:8,alertas:1,status:"atencao"},{funcionario:{id:"EMP003",nome:"Carlos Roberto Lima",matricula:"EMP003",cargo:"Operador de Produ\xe7\xe3o",setor:"Produ\xe7\xe3o"},horasTrabalhadas:160,horasExtras:5,diasPresentes:20,diasAusentes:2,atrasos:5,frequencia:90.9,saldoHoras:-4,alertas:2,status:"atencao"},{funcionario:{id:"EMP004",nome:"Ana Paula Silva",matricula:"EMP004",cargo:"Analista de RH",setor:"RH"},horasTrabalhadas:172,horasExtras:8,diasPresentes:21,diasAusentes:1,atrasos:1,frequencia:95.5,saldoHoras:2,alertas:0,status:"regular"},{funcionario:{id:"EMP005",nome:"Pedro Henrique Souza",matricula:"EMP005",cargo:"Assistente Administrativo",setor:"Administra\xe7\xe3o"},horasTrabalhadas:155,horasExtras:2,diasPresentes:19,diasAusentes:3,atrasos:8,frequencia:86.4,saldoHoras:-8,alertas:3,status:"critico"}])}catch(e){console.error("Erro ao buscar resumo dos funcion\xe1rios:",e)}finally{h(!1)}},N=a.filter(e=>e.funcionario.nome.toLowerCase().includes(g.toLowerCase())||e.funcionario.matricula.toLowerCase().includes(g.toLowerCase())||e.funcionario.cargo.toLowerCase().includes(g.toLowerCase())).sort((e,s)=>{let a,t;switch(j){case"nome":default:a=e.funcionario.nome,t=s.funcionario.nome;break;case"frequencia":a=e.frequencia,t=s.frequencia;break;case"horas":a=e.horasTrabalhadas,t=s.horasTrabalhadas}return"string"==typeof a&&"string"==typeof t?"asc"===b?a.localeCompare(t):t.localeCompare(a):"asc"===b?a-t:t-a}),w=e=>{j===e?f("asc"===b?"desc":"asc"):(v(e),f("asc"))};return u?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-6 gap-4 pb-2 border-b",children:[...Array(6)].map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"},s))}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)("div",{className:"grid grid-cols-6 gap-4 py-3",children:[...Array(6)].map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"},s))},s))]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(x.WI,{className:"absolute left-3 top-1/2 -translate-y-1/2"}),(0,t.jsx)(x.pd,{placeholder:"Buscar funcion\xe1rio...",value:g,onChange:e=>p(e.target.value),className:"pl-10 w-64"})]})}),(0,t.jsxs)(x.$n,{variant:"outline",size:"sm",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Exportar"]})]}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900",onClick:()=>w("nome"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Funcion\xe1rio"}),"nome"===j&&("asc"===b?(0,t.jsx)(n.A,{className:"h-4 w-4"}):(0,t.jsx)(i.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900",onClick:()=>w("frequencia"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Frequ\xeancia"}),"frequencia"===j&&("asc"===b?(0,t.jsx)(n.A,{className:"h-4 w-4"}):(0,t.jsx)(i.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900",onClick:()=>w("horas"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Horas"}),"horas"===j&&("asc"===b?(0,t.jsx)(n.A,{className:"h-4 w-4"}):(0,t.jsx)(i.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Extras"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Saldo"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Status"})]})}),(0,t.jsx)("tbody",{children:N.map(e=>(0,t.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-full",children:(0,t.jsx)(c.A,{className:"h-4 w-4 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.funcionario.nome}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e.funcionario.matricula," • ",e.funcionario.cargo]})]})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"font-medium ".concat(e.frequencia>=95?"text-green-600":e.frequencia>=90?"text-yellow-600":"text-red-600"),children:[e.frequencia.toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["(",e.diasPresentes,"/",e.diasPresentes+e.diasAusentes,")"]})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.horasTrabalhadas,"h"]})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("span",{className:"font-medium ".concat(e.horasExtras>20?"text-red-600":e.horasExtras>10?"text-yellow-600":"text-gray-900"),children:[e.horasExtras,"h"]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("span",{className:"font-medium ".concat(e.saldoHoras>0?"text-green-600":e.saldoHoras<0?"text-red-600":"text-gray-900"),children:[e.saldoHoras>0?"+":"",e.saldoHoras,"h"]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat((e=>{switch(e){case"regular":return"bg-green-100 text-green-800";case"atencao":return"bg-yellow-100 text-yellow-800";case"critico":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status)),children:(e=>{switch(e){case"regular":return"Regular";case"atencao":return"Aten\xe7\xe3o";case"critico":return"Cr\xedtico";default:return"N/A"}})(e.status)}),e.alertas>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-red-500"}),(0,t.jsx)("span",{className:"text-xs text-red-600",children:e.alertas})]})]})})]},e.funcionario.id))})]})}),0===N.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-sm",children:"Nenhum funcion\xe1rio encontrado"})]})]})}},8500:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9829:(e,s,a)=>{"use strict";a.d(s,{SearchIcon:()=>n});var t=a(5155),r=a(7924),l=a(4001);function n(e){let{className:s}=e;return(0,t.jsx)(r.A,{className:(0,l.cn)("h-4 w-4 text-gray-500",s)})}}},e=>{e.O(0,[874,596,441,964,358],()=>e(e.s=262)),_N_E=e.O()}]);