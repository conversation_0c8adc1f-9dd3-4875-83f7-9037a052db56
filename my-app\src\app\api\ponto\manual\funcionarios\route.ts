import { NextRequest, NextResponse } from 'next/server';

interface Funcionario {
  id: string;
  nome: string;
  matricula: string;
  cargo: string;
  setor: string;
  ativo: boolean;
}

// Simulação de banco de dados de funcionários
const funcionarios: Funcionario[] = [
  {
    id: 'EMP001',
    nome: '<PERSON>',
    matricula: 'EMP001',
    cargo: 'Analista de Sistemas',
    setor: 'TI',
    ativo: true
  },
  {
    id: 'EMP002',
    nome: '<PERSON>',
    matricula: 'EMP002',
    cargo: 'Gerente de Vendas',
    setor: 'Vendas',
    ativo: true
  },
  {
    id: 'EMP003',
    nome: '<PERSON>',
    matricula: 'EMP003',
    cargo: 'Operador de Produção',
    setor: 'Produção',
    ativo: true
  },
  {
    id: 'EMP004',
    nome: '<PERSON>',
    matricula: 'EMP004',
    cargo: 'Analista de RH',
    setor: 'RH',
    ativo: true
  },
  {
    id: 'EMP005',
    nome: '<PERSON>',
    matricula: 'EMP005',
    cargo: 'Assistente Administrativo',
    setor: 'Administração',
    ativo: true
  },
  {
    id: 'EMP006',
    nome: 'Juliana Ferreira Alves',
    matricula: 'EMP006',
    cargo: 'Coordenadora de Marketing',
    setor: 'Marketing',
    ativo: true
  },
  {
    id: 'EMP007',
    nome: 'Roberto Carlos Mendes',
    matricula: 'EMP007',
    cargo: 'Técnico de Manutenção',
    setor: 'Manutenção',
    ativo: true
  },
  {
    id: 'EMP008',
    nome: 'Fernanda Lima Santos',
    matricula: 'EMP008',
    cargo: 'Analista Financeiro',
    setor: 'Financeiro',
    ativo: true
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const setor = searchParams.get('setor');
    const ativo = searchParams.get('ativo');

    let funcionariosFiltrados = [...funcionarios];

    // Filtrar apenas funcionários ativos por padrão
    if (ativo !== 'false') {
      funcionariosFiltrados = funcionariosFiltrados.filter(f => f.ativo);
    }

    // Filtrar por setor se especificado
    if (setor) {
      funcionariosFiltrados = funcionariosFiltrados.filter(
        f => f.setor.toLowerCase() === setor.toLowerCase()
      );
    }

    // Filtrar por busca (nome ou matrícula)
    if (search) {
      const searchLower = search.toLowerCase();
      funcionariosFiltrados = funcionariosFiltrados.filter(
        f => 
          f.nome.toLowerCase().includes(searchLower) ||
          f.matricula.toLowerCase().includes(searchLower)
      );
    }

    // Ordenar por nome
    funcionariosFiltrados.sort((a, b) => a.nome.localeCompare(b.nome));

    return NextResponse.json({
      success: true,
      funcionarios: funcionariosFiltrados.map(f => ({
        id: f.id,
        nome: f.nome,
        matricula: f.matricula,
        cargo: f.cargo,
        setor: f.setor
      })),
      total: funcionariosFiltrados.length
    });

  } catch (error) {
    console.error('Erro ao buscar funcionários:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

// Endpoint para buscar funcionário específico
export async function POST(request: NextRequest) {
  try {
    const { funcionarioId } = await request.json();

    if (!funcionarioId) {
      return NextResponse.json(
        {
          success: false,
          error: 'ID do funcionário é obrigatório',
        },
        { status: 400 }
      );
    }

    const funcionario = funcionarios.find(f => f.id === funcionarioId && f.ativo);

    if (!funcionario) {
      return NextResponse.json(
        {
          success: false,
          error: 'Funcionário não encontrado',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      funcionario: {
        id: funcionario.id,
        nome: funcionario.nome,
        matricula: funcionario.matricula,
        cargo: funcionario.cargo,
        setor: funcionario.setor
      }
    });

  } catch (error) {
    console.error('Erro ao buscar funcionário:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
