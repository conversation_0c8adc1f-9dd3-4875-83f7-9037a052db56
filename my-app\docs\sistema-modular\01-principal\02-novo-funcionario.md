# ➕ MÓDULO NOVO FUNCIONÁRIO - Sistema RLPONTO

## 📋 Visão Geral
Módulo especializado para o cadastro completo de novos funcionários com wizard step-by-step.

## 🎯 Funcionalidades
- Wizard de cadastro em etapas
- Validação em tempo real
- Upload de documentos
- Cadastro de biometria opcional
- Preview antes de salvar
- Integração com sistemas externos

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── funcionarios/
│           └── novo/
│               ├── page.tsx                # Página principal do wizard
│               └── components/
│                   ├── wizard-steps.tsx   # Componente de steps
│                   ├── step-pessoal.tsx   # Dados pessoais
│                   ├── step-profissional.tsx # Dados profissionais
│                   ├── step-documentos.tsx # Upload de documentos
│                   ├── step-biometria.tsx # Cadastro de biometria
│                   └── step-confirmacao.tsx # Confirmação final
├── components/
│   └── funcionarios/
│       └── novo/
│           ├── funcionario-wizard.tsx     # Wizard principal
│           ├── progress-indicator.tsx     # Indicador de progresso
│           ├── documento-upload.tsx       # Upload de documentos
│           └── biometria-capture.tsx      # Captura de biometria
└── hooks/
    └── use-wizard.ts                      # Hook para controle do wizard
```

## 🔧 Implementação Técnica

### 🧙‍♂️ Página Principal do Wizard (page.tsx)
```typescript
// app/(dashboard)/funcionarios/novo/page.tsx
import { Metadata } from 'next/metadata';
import { FuncionarioWizard } from '@/components/funcionarios/novo/funcionario-wizard';
import { Card } from '@/components/ui/card';
import { UserPlus, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Novo Funcionário - RLPONTO',
  description: 'Cadastro de novo funcionário no sistema',
};

export default function NovoFuncionarioPage() {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/funcionarios">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Link>
          </Button>
          <div className="flex items-center space-x-2">
            <UserPlus className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Novo Funcionário</h1>
              <p className="text-gray-600">Cadastre um novo funcionário no sistema</p>
            </div>
          </div>
        </div>
      </div>

      {/* Wizard */}
      <Card className="p-6">
        <FuncionarioWizard />
      </Card>
    </div>
  );
}
```

### 🧙‍♂️ Componente Wizard Principal (funcionario-wizard.tsx)
```typescript
// components/funcionarios/novo/funcionario-wizard.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useWizard } from '@/hooks/use-wizard';
import { ProgressIndicator } from './progress-indicator';
import { StepPessoal } from './step-pessoal';
import { StepProfissional } from './step-profissional';
import { StepDocumentos } from './step-documentos';
import { StepBiometria } from './step-biometria';
import { StepConfirmacao } from './step-confirmacao';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

const WIZARD_STEPS = [
  { id: 'pessoal', title: 'Dados Pessoais', description: 'Informações básicas do funcionário' },
  { id: 'profissional', title: 'Dados Profissionais', description: 'Cargo, setor e empresa' },
  { id: 'documentos', title: 'Documentos', description: 'Upload de documentos necessários' },
  { id: 'biometria', title: 'Biometria', description: 'Cadastro de biometria (opcional)' },
  { id: 'confirmacao', title: 'Confirmação', description: 'Revisar e confirmar dados' },
];

export interface FuncionarioData {
  // Dados Pessoais
  nomeCompleto: string;
  cpf: string;
  dataNascimento: string;
  telefone?: string;
  email?: string;
  enderecoCompleto?: string;
  fotoUrl?: string;
  
  // Dados Profissionais
  matriculaEmpresa?: string;
  setor: string;
  cargo: string;
  empresa: string;
  dataAdmissao: string;
  salario?: number;
  
  // Documentos
  documentos: {
    rg?: File;
    cpfDoc?: File;
    comprovanteResidencia?: File;
    contratoTrabalho?: File;
  };
  
  // Biometria
  biometrias?: {
    digital1?: string;
    digital2?: string;
    facial?: string;
  };
}

export function FuncionarioWizard() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [funcionarioData, setFuncionarioData] = useState<Partial<FuncionarioData>>({
    documentos: {},
  });

  const {
    currentStep,
    currentStepIndex,
    isFirstStep,
    isLastStep,
    nextStep,
    prevStep,
    goToStep,
    canProceed,
    setCanProceed,
  } = useWizard(WIZARD_STEPS);

  const updateData = (stepData: Partial<FuncionarioData>) => {
    setFuncionarioData(prev => ({
      ...prev,
      ...stepData,
    }));
  };

  const handleNext = () => {
    if (canProceed) {
      nextStep();
      setError(null);
    }
  };

  const handlePrev = () => {
    prevStep();
    setError(null);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Upload de documentos primeiro
      const documentUrls: Record<string, string> = {};
      
      if (funcionarioData.documentos) {
        for (const [key, file] of Object.entries(funcionarioData.documentos)) {
          if (file instanceof File) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', key);

            const uploadResponse = await fetch('/api/upload/documento', {
              method: 'POST',
              body: formData,
            });

            if (!uploadResponse.ok) {
              throw new Error(`Erro ao fazer upload do documento: ${key}`);
            }

            const { url } = await uploadResponse.json();
            documentUrls[key] = url;
          }
        }
      }

      // Criar funcionário
      const funcionarioPayload = {
        ...funcionarioData,
        documentos: documentUrls,
      };

      const response = await fetch('/api/funcionarios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(funcionarioPayload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao cadastrar funcionário');
      }

      const funcionario = await response.json();

      // Cadastrar biometrias se fornecidas
      if (funcionarioData.biometrias && Object.keys(funcionarioData.biometrias).length > 0) {
        await fetch(`/api/funcionarios/${funcionario.id}/biometria`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(funcionarioData.biometrias),
        });
      }

      // Redirecionar para a página do funcionário
      router.push(`/funcionarios/${funcionario.id}`);
    } catch (error) {
      console.error('Erro ao cadastrar funcionário:', error);
      setError(error instanceof Error ? error.message : 'Erro interno do servidor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep.id) {
      case 'pessoal':
        return (
          <StepPessoal
            data={funcionarioData}
            onUpdate={updateData}
            onValidationChange={setCanProceed}
          />
        );
      case 'profissional':
        return (
          <StepProfissional
            data={funcionarioData}
            onUpdate={updateData}
            onValidationChange={setCanProceed}
          />
        );
      case 'documentos':
        return (
          <StepDocumentos
            data={funcionarioData}
            onUpdate={updateData}
            onValidationChange={setCanProceed}
          />
        );
      case 'biometria':
        return (
          <StepBiometria
            data={funcionarioData}
            onUpdate={updateData}
            onValidationChange={setCanProceed}
          />
        );
      case 'confirmacao':
        return (
          <StepConfirmacao
            data={funcionarioData}
            onUpdate={updateData}
            onValidationChange={setCanProceed}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <ProgressIndicator
        steps={WIZARD_STEPS}
        currentStepIndex={currentStepIndex}
        onStepClick={goToStep}
      />

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Current Step Content */}
      <div className="min-h-[400px]">
        {renderCurrentStep()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t">
        <Button
          variant="outline"
          onClick={handlePrev}
          disabled={isFirstStep || isSubmitting}
        >
          Anterior
        </Button>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push('/funcionarios')}
            disabled={isSubmitting}
          >
            Cancelar
          </Button>

          {isLastStep ? (
            <Button
              onClick={handleSubmit}
              disabled={!canProceed || isSubmitting}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Cadastrar Funcionário
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!canProceed || isSubmitting}
            >
              Próximo
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
```

### 📊 Indicador de Progresso (progress-indicator.tsx)
```typescript
// components/funcionarios/novo/progress-indicator.tsx
'use client';

import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

interface Step {
  id: string;
  title: string;
  description: string;
}

interface ProgressIndicatorProps {
  steps: Step[];
  currentStepIndex: number;
  onStepClick?: (stepIndex: number) => void;
}

export function ProgressIndicator({ 
  steps, 
  currentStepIndex, 
  onStepClick 
}: ProgressIndicatorProps) {
  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isClickable = onStepClick && index <= currentStepIndex;

          return (
            <div key={step.id} className="flex items-center">
              {/* Step Circle */}
              <div
                className={cn(
                  'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors',
                  {
                    'bg-blue-600 border-blue-600 text-white': isCompleted,
                    'bg-blue-100 border-blue-600 text-blue-600': isCurrent,
                    'bg-gray-100 border-gray-300 text-gray-400': !isCompleted && !isCurrent,
                    'cursor-pointer hover:bg-blue-50': isClickable,
                  }
                )}
                onClick={() => isClickable && onStepClick(index)}
              >
                {isCompleted ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>

              {/* Step Info */}
              <div className="ml-3 min-w-0 flex-1">
                <p
                  className={cn(
                    'text-sm font-medium',
                    {
                      'text-blue-600': isCurrent,
                      'text-gray-900': isCompleted,
                      'text-gray-500': !isCompleted && !isCurrent,
                    }
                  )}
                >
                  {step.title}
                </p>
                <p className="text-xs text-gray-500">{step.description}</p>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    'flex-1 h-0.5 mx-4',
                    {
                      'bg-blue-600': index < currentStepIndex,
                      'bg-gray-300': index >= currentStepIndex,
                    }
                  )}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
```

### 👤 Step Dados Pessoais (step-pessoal.tsx)
```typescript
// components/funcionarios/novo/step-pessoal.tsx
'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FotoUpload } from '@/components/funcionarios/foto-upload';
import { cpfValidator, emailValidator, telefoneValidator } from '@/lib/validators';
import type { FuncionarioData } from './funcionario-wizard';

const stepPessoalSchema = z.object({
  nomeCompleto: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  cpf: z.string().refine(cpfValidator, 'CPF inválido'),
  dataNascimento: z.string().refine((date) => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 16;
  }, 'Funcionário deve ter pelo menos 16 anos'),
  telefone: z.string().refine(telefoneValidator, 'Telefone inválido').optional().or(z.literal('')),
  email: z.string().refine(emailValidator, 'Email inválido').optional().or(z.literal('')),
  enderecoCompleto: z.string().optional(),
});

type StepPessoalData = z.infer<typeof stepPessoalSchema>;

interface StepPessoalProps {
  data: Partial<FuncionarioData>;
  onUpdate: (data: Partial<FuncionarioData>) => void;
  onValidationChange: (isValid: boolean) => void;
}

export function StepPessoal({ data, onUpdate, onValidationChange }: StepPessoalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<StepPessoalData>({
    resolver: zodResolver(stepPessoalSchema),
    defaultValues: {
      nomeCompleto: data.nomeCompleto || '',
      cpf: data.cpf || '',
      dataNascimento: data.dataNascimento || '',
      telefone: data.telefone || '',
      email: data.email || '',
      enderecoCompleto: data.enderecoCompleto || '',
    },
    mode: 'onChange',
  });

  // Watch all fields to update parent component
  const watchedFields = watch();

  useEffect(() => {
    onUpdate(watchedFields);
    onValidationChange(isValid);
  }, [watchedFields, isValid, onUpdate, onValidationChange]);

  const handleFotoChange = (fotoUrl: string | null) => {
    onUpdate({ fotoUrl });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Dados Pessoais</h2>
        <p className="text-gray-600">Preencha as informações básicas do funcionário</p>
      </div>

      {/* Foto */}
      <Card>
        <CardHeader>
          <CardTitle>Foto do Funcionário</CardTitle>
        </CardHeader>
        <CardContent>
          <FotoUpload
            currentFoto={data.fotoUrl}
            onFotoChange={handleFotoChange}
          />
        </CardContent>
      </Card>

      {/* Dados Pessoais */}
      <Card>
        <CardHeader>
          <CardTitle>Informações Pessoais</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <Label htmlFor="nomeCompleto">Nome Completo *</Label>
            <Input
              id="nomeCompleto"
              {...register('nomeCompleto')}
              placeholder="Digite o nome completo"
            />
            {errors.nomeCompleto && (
              <p className="text-sm text-red-600 mt-1">{errors.nomeCompleto.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="cpf">CPF *</Label>
            <Input
              id="cpf"
              {...register('cpf')}
              placeholder="000.000.000-00"
              maxLength={14}
            />
            {errors.cpf && (
              <p className="text-sm text-red-600 mt-1">{errors.cpf.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="dataNascimento">Data de Nascimento *</Label>
            <Input
              id="dataNascimento"
              type="date"
              {...register('dataNascimento')}
            />
            {errors.dataNascimento && (
              <p className="text-sm text-red-600 mt-1">{errors.dataNascimento.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="telefone">Telefone</Label>
            <Input
              id="telefone"
              {...register('telefone')}
              placeholder="(11) 99999-9999"
            />
            {errors.telefone && (
              <p className="text-sm text-red-600 mt-1">{errors.telefone.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="enderecoCompleto">Endereço Completo</Label>
            <Input
              id="enderecoCompleto"
              {...register('enderecoCompleto')}
              placeholder="Rua, número, bairro, cidade - UF"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 🎣 Hook do Wizard (use-wizard.ts)
```typescript
// hooks/use-wizard.ts
'use client';

import { useState, useCallback } from 'react';

interface WizardStep {
  id: string;
  title: string;
  description: string;
}

export function useWizard(steps: WizardStep[]) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [canProceed, setCanProceed] = useState(false);

  const currentStep = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  const nextStep = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
      setCanProceed(false); // Reset validation for next step
    }
  }, [currentStepIndex, steps.length]);

  const prevStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
      setCanProceed(true); // Allow going back
    }
  }, [currentStepIndex]);

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStepIndex(stepIndex);
      setCanProceed(stepIndex <= currentStepIndex); // Can only go to completed steps
    }
  }, [steps.length, currentStepIndex]);

  const reset = useCallback(() => {
    setCurrentStepIndex(0);
    setCanProceed(false);
  }, []);

  return {
    currentStep,
    currentStepIndex,
    isFirstStep,
    isLastStep,
    canProceed,
    setCanProceed,
    nextStep,
    prevStep,
    goToStep,
    reset,
  };
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades do Wizard
- [ ] Navegação entre steps
- [ ] Validação em tempo real
- [ ] Indicador de progresso
- [ ] Dados pessoais
- [ ] Dados profissionais
- [ ] Upload de documentos
- [ ] Cadastro de biometria
- [ ] Confirmação final
- [ ] Salvamento completo

### 🔧 Validações e UX
- [ ] Validação de cada step
- [ ] Prevenção de navegação inválida
- [ ] Feedback visual de erros
- [ ] Loading states
- [ ] Confirmação antes de cancelar
- [ ] Auto-save (opcional)

## 🚀 Próximos Passos
1. **Implementar steps restantes** - Profissional, Documentos, Biometria
2. **Integração com API** - Upload de arquivos e biometria
3. **Testes completos** - Cada step e fluxo completo
