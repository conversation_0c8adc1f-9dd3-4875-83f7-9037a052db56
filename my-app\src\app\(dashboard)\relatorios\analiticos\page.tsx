import { Metadata } from 'next';
import { BarChart3, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Relatórios Analíticos - RLPONTO',
  description: 'Relatórios com análises avançadas e visualizações',
};

export default function RelatoriosAnaliticosPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/relatorios">
                <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <ArrowLeft className="h-4 w-4 mr-2 inline" />
                  Voltar
                </button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-600 rounded-lg">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Relatórios Analíticos</h1>
                  <p className="text-gray-600">Análises avançadas com gráficos e insights</p>
                </div>
              </div>
            </div>
          </div>

          {/* Descrição */}
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-purple-900 mb-3">Análises Inteligentes</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-purple-700">
              <div>
                <strong>📊 Dashboards Interativos</strong>
                <p className="mt-1">Visualizações dinâmicas com gráficos e métricas em tempo real</p>
              </div>
              <div>
                <strong>🔍 Insights Automáticos</strong>
                <p className="mt-1">Análises inteligentes que identificam padrões e tendências</p>
              </div>
              <div>
                <strong>📈 Comparativos Avançados</strong>
                <p className="mt-1">Comparações históricas e benchmarks de performance</p>
              </div>
            </div>
          </div>

          {/* Templates Analíticos */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Templates Analíticos
            </h2>
            <p className="text-sm text-gray-600 mb-6">
              Escolha o tipo de análise que deseja gerar
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Dashboard Executivo */}
              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Dashboard Executivo</h4>
                    <p className="text-xs text-gray-600">Visão geral com KPIs</p>
                  </div>
                </div>
                <button className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                  Gerar
                </button>
              </div>

              {/* Análise de Frequência */}
              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Análise de Frequência</h4>
                    <p className="text-xs text-gray-600">Presença e pontualidade</p>
                  </div>
                </div>
                <button className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                  Gerar
                </button>
              </div>

              {/* Horas Extras */}
              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-purple-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Horas Extras</h4>
                    <p className="text-xs text-gray-600">Distribuição e custos</p>
                  </div>
                </div>
                <button className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                  Gerar
                </button>
              </div>

              {/* Absenteísmo */}
              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-red-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Absenteísmo</h4>
                    <p className="text-xs text-gray-600">Análise de ausências</p>
                  </div>
                </div>
                <button className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                  Gerar
                </button>
              </div>

              {/* Comparativo */}
              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-indigo-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-indigo-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Comparativo</h4>
                    <p className="text-xs text-gray-600">Entre departamentos</p>
                  </div>
                </div>
                <button className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                  Gerar
                </button>
              </div>

              {/* Tendências */}
              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-orange-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Tendências</h4>
                    <p className="text-xs text-gray-600">Análise histórica</p>
                  </div>
                </div>
                <button className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                  Gerar
                </button>
              </div>
            </div>
          </div>

          {/* Recursos Disponíveis */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recursos Disponíveis</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  <h4 className="font-medium text-gray-900">Gráficos Dinâmicos</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Barras, linhas, pizza e gráficos de dispersão interativos
                </p>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                  </div>
                  <h4 className="font-medium text-gray-900">KPIs Automáticos</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Indicadores calculados automaticamente com alertas
                </p>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-yellow-600" />
                  </div>
                  <h4 className="font-medium text-gray-900">Tendências</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Análise de tendências e projeções futuras
                </p>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                  <h4 className="font-medium text-gray-900">Comparativos</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Comparações entre períodos, departamentos e funcionários
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


