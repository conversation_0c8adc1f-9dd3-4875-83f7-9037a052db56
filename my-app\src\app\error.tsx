'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui';
import { 
  AlertCircle, 
  Home, 
  RefreshCw, 
  Bug,
  Clock
} from 'lucide-react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log do erro para monitoramento
    console.error('Erro da aplicação:', error);
  }, [error]);

  const getErrorType = (error: Error) => {
    if (error.message.includes('fetch')) {
      return {
        type: 'Erro de Conexão',
        description: 'Problema de conectividade com o servidor',
        icon: '🌐'
      };
    }
    
    if (error.message.includes('auth')) {
      return {
        type: 'Erro de Autenticação',
        description: 'Problema com credenciais ou sessão expirada',
        icon: '🔐'
      };
    }
    
    if (error.message.includes('permission')) {
      return {
        type: '<PERSON><PERSON> de Permissão',
        description: 'Você não tem permissão para acessar este recurso',
        icon: '🚫'
      };
    }
    
    return {
      type: 'Erro Interno',
      description: 'Erro inesperado na aplicação',
      icon: '⚠️'
    };
  };

  const errorInfo = getErrorType(error);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
            <AlertCircle className="h-12 w-12 text-red-600" />
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Ops! Algo deu errado
          </h1>
          <p className="text-gray-600 mb-8">
            Ocorreu um erro inesperado. Nossa equipe foi notificada.
          </p>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Informações do Erro */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <span className="text-2xl">{errorInfo.icon}</span>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {errorInfo.type}
                </h3>
                <p className="text-sm text-gray-600">
                  {errorInfo.description}
                </p>
              </div>
            </div>
          </div>

          {/* Detalhes Técnicos */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Detalhes Técnicos:
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>Erro:</strong> {error.message}</p>
              {error.digest && (
                <p><strong>ID do Erro:</strong> {error.digest}</p>
              )}
              <p><strong>Timestamp:</strong> {new Date().toLocaleString('pt-BR')}</p>
              <p><strong>URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
            </div>
          </div>

          {/* Stack Trace (apenas em desenvolvimento) */}
          {process.env.NODE_ENV === 'development' && error.stack && (
            <div className="mb-8 p-4 bg-red-50 rounded-lg border border-red-200">
              <h4 className="text-sm font-medium text-red-900 mb-2 flex items-center">
                <Bug className="h-4 w-4 mr-2" />
                Stack Trace (Desenvolvimento):
              </h4>
              <pre className="text-xs text-red-700 overflow-x-auto whitespace-pre-wrap">
                {error.stack}
              </pre>
            </div>
          )}

          {/* Ações */}
          <div className="space-y-4">
            <div className="flex space-x-4">
              <Button 
                onClick={reset}
                variant="primary"
                className="flex-1"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar Novamente
              </Button>
              
              <Link href="/dashboard" className="flex-1">
                <Button variant="outline" className="w-full">
                  <Home className="h-4 w-4 mr-2" />
                  Ir para Dashboard
                </Button>
              </Link>
            </div>

            {/* Ações Específicas por Tipo de Erro */}
            {errorInfo.type === 'Erro de Autenticação' && (
              <Link href="/login" className="block">
                <Button variant="outline" className="w-full">
                  Fazer Login Novamente
                </Button>
              </Link>
            )}

            {errorInfo.type === 'Erro de Conexão' && (
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Verifique sua conexão com a internet e tente novamente.
                </p>
                <Button 
                  onClick={() => window.location.reload()}
                  variant="outline"
                  size="sm"
                >
                  Recarregar Página
                </Button>
              </div>
            )}
          </div>

          {/* Informações de Suporte */}
          <div className="border-t border-gray-200 pt-6 mt-6">
            <div className="text-center text-sm text-gray-500">
              <p className="mb-2">
                Se o problema persistir, entre em contato com o suporte técnico.
              </p>
              <div className="flex items-center justify-center space-x-2 text-xs">
                <Clock className="h-4 w-4" />
                <span>
                  <strong>Sistema RLPONTO</strong> - Controle de Ponto Eletrônico
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
