exports.id=322,exports.ids=[322],exports.modules={3867:(a,b,c)=>{"use strict";c.d(b,{DashboardNav:()=>s});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(49384),i=c(32192),j=c(41312),k=c(23026),l=c(48730),m=c(6123),n=c(53411),o=c(10022),p=c(25541),q=c(84027);let r=[{name:"Dashboard",href:"/dashboard",icon:i.A,description:"Vis\xe3o geral do sistema"},{name:"Funcion\xe1rios",href:"/funcionarios",icon:j.A,description:"Gest\xe3o de funcion\xe1rios",children:[{name:"Lista de Funcion\xe1rios",href:"/funcionarios",icon:j.A},{name:"Novo Funcion\xe1rio",href:"/funcionarios/novo",icon:k.A}]},{name:"<PERSON><PERSON>",href:"/ponto/biometrico",icon:l.A,description:"Registro de ponto",children:[{name:"Ponto Biom\xe9trico",href:"/ponto/biometrico",icon:l.A},{name:"Ponto Manual",href:"/ponto/manual",icon:m.A}]},{name:"Per\xedodo de Apura\xe7\xe3o",href:"/periodo-apuracao",icon:n.A,description:"An\xe1lise mensal"},{name:"Relat\xf3rios",href:"/relatorios",icon:o.A,description:"Relat\xf3rios e an\xe1lises",children:[{name:"Relat\xf3rio Individual",href:"/relatorios/funcionario",icon:o.A},{name:"Relat\xf3rio por Per\xedodo",href:"/relatorios/periodo",icon:o.A},{name:"Relat\xf3rios Anal\xedticos",href:"/relatorios/analiticos",icon:o.A}]},{name:"Estat\xedsticas",href:"/estatisticas",icon:p.A,description:"An\xe1lises e KPIs",children:[{name:"Produtividade",href:"/estatisticas/produtividade",icon:p.A},{name:"Absente\xedsmo",href:"/estatisticas/absenteismo",icon:p.A},{name:"Tend\xeancias",href:"/estatisticas/tendencias",icon:p.A},{name:"Comparativos",href:"/estatisticas/comparativos",icon:p.A}]},{name:"Administra\xe7\xe3o",href:"/administracao",icon:q.A,description:"Configura\xe7\xf5es do sistema"}];function s(){let a=(0,g.usePathname)();return(0,d.jsx)("nav",{className:"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen",children:(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-1",children:r.map(b=>{let c=a===b.href||a.startsWith(b.href+"/");return(0,d.jsxs)("div",{children:[(0,d.jsxs)(f(),{href:b.href,className:(0,h.$)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",c?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),children:[(0,d.jsx)(b.icon,{className:(0,h.$)("mr-3 h-5 w-5",c?"text-blue-700":"text-gray-400")}),b.name]}),b.children&&c&&(0,d.jsx)("div",{className:"ml-8 mt-1 space-y-1",children:b.children.map(b=>{let c=a===b.href;return(0,d.jsxs)(f(),{href:b.href,className:(0,h.$)("flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors",c?"bg-blue-100 text-blue-800":"text-gray-600 hover:bg-gray-50 hover:text-gray-800"),children:[(0,d.jsx)(b.icon,{className:"mr-2 h-3 w-3"}),b.name]},b.name)})})]},b.name)})})})})}},4536:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\node_modules\\next\\dist\\client\\app-dir\\link.js")},6123:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("hand",[["path",{d:"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2",key:"1fvzgz"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2",key:"1kc0my"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8",key:"10h0bg"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]])},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},23026:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},26373:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(61120);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},30705:(a,b,c)=>{"use strict";c.d(b,{DashboardHeader:()=>l});var d=c(60687),e=c(43210),f=c(42613),g=c(97051),h=c(58869),i=c(78272),j=c(84027),k=c(40083);function l(){let[a,b]=(0,e.useState)(!1),[c,l]=(0,e.useState)("");return(0,d.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,d.jsx)("div",{className:"px-6 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-sm",children:"RL"})}),(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RLPONTO"})]})}),(0,d.jsx)("div",{className:"flex-1 max-w-lg mx-8",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(f.WI,{className:"absolute left-3 top-1/2 -translate-y-1/2"}),(0,d.jsx)("input",{type:"text",value:c,onChange:a=>l(a.target.value),placeholder:"Buscar funcion\xe1rios, relat\xf3rios...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600"})]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100",children:[(0,d.jsx)(g.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("button",{onClick:()=>b(!a),className:"flex items-center space-x-2 p-2 text-gray-700 hover:text-gray-900 rounded-lg hover:bg-gray-100",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Administrador"}),(0,d.jsx)(i.A,{className:"h-4 w-4"})]}),a&&(0,d.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:[(0,d.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,d.jsx)(h.A,{className:"mr-3 h-4 w-4"}),"Meu Perfil"]}),(0,d.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,d.jsx)(j.A,{className:"mr-3 h-4 w-4"}),"Configura\xe7\xf5es"]}),(0,d.jsx)("hr",{className:"my-1"}),(0,d.jsxs)("button",{onClick:()=>{window.location.href="/login"},className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[(0,d.jsx)(k.A,{className:"mr-3 h-4 w-4"}),"Sair"]})]})]})]})]})})})}},40083:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53411:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},57675:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(37413),e=c(80857),f=c(71199);let g={title:"Dashboard - RLPONTO",description:"Sistema de controle de ponto eletr\xf4nico"};function h({children:a}){return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(f.DashboardHeader,{}),(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)(e.DashboardNav,{}),(0,d.jsx)("main",{className:"flex-1 overflow-auto",children:a})]})]})}},58869:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59574:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},59601:(a,b,c)=>{Promise.resolve().then(c.bind(c,30705)),Promise.resolve().then(c.bind(c,3867))},69329:(a,b,c)=>{Promise.resolve().then(c.bind(c,71199)),Promise.resolve().then(c.bind(c,80857))},71199:(a,b,c)=>{"use strict";c.d(b,{DashboardHeader:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call DashboardHeader() from the server but DashboardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-header.tsx","DashboardHeader")},78272:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},80857:(a,b,c)=>{"use strict";c.d(b,{DashboardNav:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call DashboardNav() from the server but DashboardNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-nav.tsx","DashboardNav")},84027:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},97051:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};