import { Metadata } from 'next';
import { FuncionarioWizard } from '@/components/funcionarios/novo/funcionario-wizard';
import { UserPlus, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Novo Funcionário - RLPONTO',
  description: 'Cadastro de novo funcionário no sistema',
};

export default function NovoFuncionarioPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/funcionarios">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <UserPlus className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Novo Funcionário</h1>
                  <p className="text-gray-600">Cadastre um novo funcionário no sistema</p>
                </div>
              </div>
            </div>
          </div>

          {/* Wizard */}
          <div className="bg-white rounded-lg shadow p-6">
            <FuncionarioWizard />
          </div>
        </div>
      </div>
    </div>
  );
}
