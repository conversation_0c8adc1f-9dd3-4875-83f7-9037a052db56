(()=>{var a={};a.id=392,a.ids=[392],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12640:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},13691:(a,b,c)=>{"use strict";c.d(b,{PeriodSelector:()=>l});var d=c(60687),e=c(43210),f=c(16189),g=c(42613),h=c(47033),i=c(40228),j=c(78272),k=c(14952);function l({currentPeriod:a}){let b=(0,f.useRouter)(),c=(0,f.useSearchParams)(),[l,m]=(0,e.useState)(!1),n=["Janeiro","Fevereiro","Mar\xe7o","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],o=Array.from({length:5},(a,b)=>new Date().getFullYear()-2+b),p=(a,d)=>{let e=new URLSearchParams(c);e.set("ano",a.toString()),e.set("mes",d.toString()),b.push(`/periodo-apuracao?${e.toString()}`),m(!1)},q=b=>{let c=a.ano,d=a.mes;"prev"===b?--d<1&&(d=12,c--):++d>12&&(d=1,c++),p(c,d)},r=()=>{let b=new Date;return a.ano===b.getFullYear()&&a.mes===b.getMonth()+1};return(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g.$n,{variant:"outline",size:"sm",onClick:()=>q("prev"),children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)(g.$n,{variant:"outline",onClick:()=>m(!l),className:"min-w-[180px] justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[n[a.mes-1]," ",a.ano]}),r()&&(0,d.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:"Atual"})]}),(0,d.jsx)(j.A,{className:"h-4 w-4"})]}),l&&(0,d.jsx)("div",{className:"absolute top-full mt-2 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[300px]",children:(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ano"}),(0,d.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:o.map(b=>(0,d.jsx)("button",{onClick:()=>p(b,a.mes),className:`w-full text-left px-3 py-2 rounded text-sm transition-colors ${b===a.ano?"bg-blue-100 text-blue-900 font-medium":"hover:bg-gray-100"}`,children:b},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xeas"}),(0,d.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:n.map((b,c)=>{let e=c+1,f=new Date().getFullYear()===a.ano&&new Date().getMonth()+1===e;return(0,d.jsxs)("button",{onClick:()=>p(a.ano,e),className:`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center justify-between ${e===a.mes?"bg-blue-100 text-blue-900 font-medium":"hover:bg-gray-100"}`,children:[(0,d.jsx)("span",{children:b}),f&&(0,d.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-1 rounded",children:"Atual"})]},e)})})]})]}),(0,d.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(g.$n,{variant:"outline",size:"sm",onClick:()=>{let a=new Date;p(a.getFullYear(),a.getMonth()+1)},className:"flex-1",children:"M\xeas Atual"}),(0,d.jsx)(g.$n,{variant:"outline",size:"sm",onClick:()=>{let a=new Date;a.setMonth(a.getMonth()-1),p(a.getFullYear(),a.getMonth()+1)},className:"flex-1",children:"M\xeas Anterior"})]})})]})})]}),(0,d.jsx)(g.$n,{variant:"outline",size:"sm",onClick:()=>q("next"),disabled:r(),children:(0,d.jsx)(k.A,{className:"h-4 w-4"})})]}),l&&(0,d.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>m(!1)})]})}},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19055:(a,b,c)=>{"use strict";c.d(b,{EmployeeSummary:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call EmployeeSummary() from the server but EmployeeSummary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\employee-summary.tsx","EmployeeSummary")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20912:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["periodo-apuracao",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,34976)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\periodo-apuracao\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\periodo-apuracao\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/periodo-apuracao/page",pathname:"/periodo-apuracao",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/periodo-apuracao/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},24085:(a,b,c)=>{"use strict";c.d(b,{EmployeeSummary:()=>m});var d=c(60687),e=c(43210),f=c(31158),g=c(25541),h=c(12640),i=c(58869),j=c(48730),k=c(43649),l=c(42613);function m({period:a}){let[b,c]=(0,e.useState)([]),[m,n]=(0,e.useState)(!0),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)("nome"),[s,t]=(0,e.useState)("asc"),u=b.filter(a=>a.funcionario.nome.toLowerCase().includes(o.toLowerCase())||a.funcionario.matricula.toLowerCase().includes(o.toLowerCase())||a.funcionario.cargo.toLowerCase().includes(o.toLowerCase())).sort((a,b)=>{let c,d;switch(q){case"nome":default:c=a.funcionario.nome,d=b.funcionario.nome;break;case"frequencia":c=a.frequencia,d=b.frequencia;break;case"horas":c=a.horasTrabalhadas,d=b.horasTrabalhadas}return"string"==typeof c&&"string"==typeof d?"asc"===s?c.localeCompare(d):d.localeCompare(c):"asc"===s?c-d:d-c}),v=a=>{q===a?t("asc"===s?"desc":"asc"):(r(a),t("asc"))};return m?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4 pb-2 border-b",children:[...Array(6)].map((a,b)=>(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"},b))}),[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4 py-3",children:[...Array(6)].map((a,b)=>(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"},b))},b))]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(l.WI,{className:"absolute left-3 top-1/2 -translate-y-1/2"}),(0,d.jsx)(l.pd,{placeholder:"Buscar funcion\xe1rio...",value:o,onChange:a=>p(a.target.value),className:"pl-10 w-64"})]})}),(0,d.jsxs)(l.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Exportar"]})]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900",onClick:()=>v("nome"),children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{children:"Funcion\xe1rio"}),"nome"===q&&("asc"===s?(0,d.jsx)(g.A,{className:"h-4 w-4"}):(0,d.jsx)(h.A,{className:"h-4 w-4"}))]})}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900",onClick:()=>v("frequencia"),children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{children:"Frequ\xeancia"}),"frequencia"===q&&("asc"===s?(0,d.jsx)(g.A,{className:"h-4 w-4"}):(0,d.jsx)(h.A,{className:"h-4 w-4"}))]})}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:text-gray-900",onClick:()=>v("horas"),children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{children:"Horas"}),"horas"===q&&("asc"===s?(0,d.jsx)(g.A,{className:"h-4 w-4"}):(0,d.jsx)(h.A,{className:"h-4 w-4"}))]})}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Extras"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Saldo"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Status"})]})}),(0,d.jsx)("tbody",{children:u.map(a=>(0,d.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-full",children:(0,d.jsx)(i.A,{className:"h-4 w-4 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:a.funcionario.nome}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:[a.funcionario.matricula," • ",a.funcionario.cargo]})]})]})}),(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("span",{className:`font-medium ${a.frequencia>=95?"text-green-600":a.frequencia>=90?"text-yellow-600":"text-red-600"}`,children:[a.frequencia.toFixed(1),"%"]}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:["(",a.diasPresentes,"/",a.diasPresentes+a.diasAusentes,")"]})]})}),(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 text-gray-500"}),(0,d.jsxs)("span",{className:"font-medium",children:[a.horasTrabalhadas,"h"]})]})}),(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("span",{className:`font-medium ${a.horasExtras>20?"text-red-600":a.horasExtras>10?"text-yellow-600":"text-gray-900"}`,children:[a.horasExtras,"h"]})}),(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("span",{className:`font-medium ${a.saldoHoras>0?"text-green-600":a.saldoHoras<0?"text-red-600":"text-gray-900"}`,children:[a.saldoHoras>0?"+":"",a.saldoHoras,"h"]})}),(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${(a=>{switch(a){case"regular":return"bg-green-100 text-green-800";case"atencao":return"bg-yellow-100 text-yellow-800";case"critico":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.status)}`,children:(a=>{switch(a){case"regular":return"Regular";case"atencao":return"Aten\xe7\xe3o";case"critico":return"Cr\xedtico";default:return"N/A"}})(a.status)}),a.alertas>0&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 text-red-500"}),(0,d.jsx)("span",{className:"text-xs text-red-600",children:a.alertas})]})]})})]},a.funcionario.id))})]})}),0===u.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)(i.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-sm",children:"Nenhum funcion\xe1rio encontrado"})]})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33282:(a,b,c)=>{"use strict";c.d(b,{AlertsPanel:()=>l});var d=c(60687),e=c(43210),f=c(35071),g=c(43649),h=c(96882);c(48730);var i=c(58869),j=c(5336),k=c(40228);function l({period:a}){let[b,c]=(0,e.useState)([]),[l,m]=(0,e.useState)(!0),[n,o]=(0,e.useState)("todos"),p=b.filter(a=>"todos"===n||a.status===n),q={total:b.length,pendente:b.filter(a=>"pendente"===a.status).length,resolvido:b.filter(a=>"resolvido"===a.status).length,alta:b.filter(a=>"alta"===a.severidade).length};return l?(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded animate-pulse",children:[(0,d.jsx)("div",{className:"w-5 h-5 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"w-3/4 h-4 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-1/2 h-3 bg-gray-200 rounded"})]})]},b))}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-red-700",children:q.pendente}),(0,d.jsx)("div",{className:"text-xs text-red-600",children:"Pendentes"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-green-700",children:q.resolvido}),(0,d.jsx)("div",{className:"text-xs text-green-600",children:"Resolvidos"})]})]}),(0,d.jsx)("div",{className:"flex space-x-2",children:["todos","pendente","resolvido"].map(a=>(0,d.jsx)("button",{onClick:()=>o(a),className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${n===a?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:a.charAt(0).toUpperCase()+a.slice(1)},a))}),(0,d.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:0===p.length?(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)(j.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-sm",children:"Nenhum alerta encontrado"})]}):p.map(a=>(0,d.jsx)("div",{className:`p-3 border rounded-lg transition-colors ${"pendente"===a.status?"border-red-200 bg-red-50":"resolvido"===a.status?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50"}`,children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:`p-1 rounded ${(a=>{switch(a){case"alta":return"text-red-600 bg-red-100";case"media":return"text-yellow-600 bg-yellow-100";case"baixa":return"text-blue-600 bg-blue-100";default:return"text-gray-600 bg-gray-100"}})(a.severidade)}`,children:(a=>{switch(a){case"alta":return(0,d.jsx)(f.A,{className:"h-4 w-4"});case"media":return(0,d.jsx)(g.A,{className:"h-4 w-4"});default:return(0,d.jsx)(h.A,{className:"h-4 w-4"})}})(a.severidade)}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:a.titulo}),(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"pendente"===a.status?"bg-red-100 text-red-800":"resolvido"===a.status?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:"pendente"===a.status?"Pendente":"resolvido"===a.status?"Resolvido":"Ignorado"})]}),(0,d.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:a.descricao}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:a.funcionario.nome}),(0,d.jsxs)("span",{children:["(",a.funcionario.matricula,")"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(k.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:a.data})]})]})]})]})},a.id))}),q.pendente>0&&(0,d.jsx)("div",{className:"pt-3 border-t border-gray-200",children:(0,d.jsxs)("button",{className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:["Ver todos os alertas (",q.total,")"]})})]})}},33873:a=>{"use strict";a.exports=require("path")},34976:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q,metadata:()=>p});var d=c(37413),e=c(61120),f=c(67473),g=c(97615),h=c(39132),i=c(19055),j=c(46661),k=c(51465),l=c(72845),m=c(75243),n=c(4536),o=c.n(n);let p={title:"Dashboard - Per\xedodo de Apura\xe7\xe3o",description:"Vis\xe3o geral do per\xedodo de apura\xe7\xe3o de ponto"};async function q({searchParams:a}){let b=await a,c={ano:parseInt(b.ano||new Date().getFullYear().toString()),mes:parseInt(b.mes||(new Date().getMonth()+1).toString())};return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(o(),{href:"/dashboard",children:(0,d.jsxs)(m.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(l.A,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Per\xedodo de Apura\xe7\xe3o"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Dashboard de an\xe1lise e controle de frequ\xeancia"})]})]})]}),(0,d.jsx)(j.PeriodSelector,{currentPeriod:c})]}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(r,{}),children:(0,d.jsx)(f.MetricsOverview,{period:c})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-2",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequ\xeancia Mensal"}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(s,{}),children:(0,d.jsx)(g.FrequencyChart,{period:c})})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Alertas e Inconsist\xeancias"}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(t,{}),children:(0,d.jsx)(h.AlertsPanel,{period:c})})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Resumo por Funcion\xe1rio"})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(u,{}),children:(0,d.jsx)(i.EmployeeSummary,{period:c})})})]})]})})})}function r(){return(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-16 h-4 bg-gray-200 rounded"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded mb-2"}),(0,d.jsx)("div",{className:"w-24 h-4 bg-gray-200 rounded"})]})},b))})}function s(){return(0,d.jsx)("div",{className:"h-80 bg-gray-200 rounded animate-pulse"})}function t(){return(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded animate-pulse",children:[(0,d.jsx)("div",{className:"w-5 h-5 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"w-3/4 h-4 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-1/2 h-3 bg-gray-200 rounded"})]})]},b))})}function u(){return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4 pb-2 border-b",children:[...Array(6)].map((a,b)=>(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded"},b))}),[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4 py-3",children:[...Array(6)].map((a,b)=>(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded"},b))},b))]})}},39132:(a,b,c)=>{"use strict";c.d(b,{AlertsPanel:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AlertsPanel() from the server but AlertsPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\alerts-panel.tsx","AlertsPanel")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46661:(a,b,c)=>{"use strict";c.d(b,{PeriodSelector:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call PeriodSelector() from the server but PeriodSelector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\period-selector.tsx","PeriodSelector")},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67473:(a,b,c)=>{"use strict";c.d(b,{MetricsOverview:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call MetricsOverview() from the server but MetricsOverview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\metrics-overview.tsx","MetricsOverview")},72845:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},77557:(a,b,c)=>{"use strict";c.d(b,{FrequencyChart:()=>i});var d=c(60687),e=c(43210),f=c(53411),g=c(25541),h=c(12640);function i({period:a}){let[b,c]=(0,e.useState)([]),[i,j]=(0,e.useState)(!0),[k,l]=(0,e.useState)("presenca");if(i)return(0,d.jsx)("div",{className:"h-80 bg-gray-200 rounded animate-pulse flex items-center justify-center",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-gray-400"})});let m="presenca"===k?Math.max(...b.map(a=>Math.max(a.presentes,a.ausentes,a.atrasados))):Math.max(...b.map(a=>a.horasExtras)),n=a=>m>0?a/m*100:0,o=b.reduce((a,b)=>a+b.presentes,0),p=b.reduce((a,b)=>a+b.ausentes,0),q=b.reduce((a,b)=>a+b.atrasados,0),r=b.reduce((a,b)=>a+b.horasExtras,0),s=o+p>0?o/(o+p)*100:0;return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("button",{onClick:()=>l("presenca"),className:`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${"presenca"===k?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:"Presen\xe7a"}),(0,d.jsx)("button",{onClick:()=>l("horas"),className:`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${"horas"===k?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:"Horas Extras"})]}),(0,d.jsx)("div",{className:"flex items-center space-x-4 text-sm",children:"presenca"===k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,d.jsx)("span",{children:"Presentes"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded"}),(0,d.jsx)("span",{children:"Ausentes"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded"}),(0,d.jsx)("span",{children:"Atrasados"})]})]}):(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,d.jsx)("span",{children:"Horas Extras"})]})})]}),(0,d.jsx)("div",{className:"relative h-64 border-b border-l border-gray-200",children:(0,d.jsx)("div",{className:"absolute inset-0 flex items-end justify-between px-2",children:b.map((a,b)=>(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-1 flex-1 max-w-[30px]",children:[(0,d.jsx)("div",{className:"relative w-full flex flex-col items-center justify-end h-56",children:"presenca"===k?(0,d.jsxs)("div",{className:"w-full flex flex-col items-center justify-end space-y-1",children:[a.presentes>0&&(0,d.jsx)("div",{className:"w-full bg-green-500 rounded-t",style:{height:`${n(a.presentes)}%`},title:`${a.presentes} presentes`}),a.atrasados>0&&(0,d.jsx)("div",{className:"w-full bg-yellow-500",style:{height:`${n(a.atrasados)}%`},title:`${a.atrasados} atrasados`}),a.ausentes>0&&(0,d.jsx)("div",{className:"w-full bg-red-500 rounded-b",style:{height:`${n(a.ausentes)}%`},title:`${a.ausentes} ausentes`})]}):(0,d.jsx)("div",{className:"w-full bg-blue-500 rounded-t",style:{height:`${n(a.horasExtras)}%`},title:`${a.horasExtras}h extras`})}),(0,d.jsxs)("div",{className:"text-xs text-center",children:[(0,d.jsx)("div",{className:"font-medium",children:a.dia}),(0,d.jsx)("div",{className:"text-gray-500",children:a.diaSemana})]})]},b))})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-700",children:o}),(0,d.jsx)("div",{className:"text-sm text-green-600",children:"Total Presentes"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-red-700",children:p}),(0,d.jsx)("div",{className:"text-sm text-red-600",children:"Total Ausentes"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-700",children:q}),(0,d.jsx)("div",{className:"text-sm text-yellow-600",children:"Total Atrasados"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"text-2xl font-bold text-blue-700",children:[r,"h"]}),(0,d.jsx)("div",{className:"text-sm text-blue-600",children:"Horas Extras"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Frequ\xeancia Geral do Per\xedodo"}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[s>=95?(0,d.jsx)(g.A,{className:"h-4 w-4 text-green-600"}):(0,d.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,d.jsxs)("span",{className:`text-sm font-medium ${s>=95?"text-green-600":"text-red-600"}`,children:[s.toFixed(1),"%"]})]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${s>=95?"bg-green-600":"bg-red-600"}`,style:{width:`${s}%`}})})]})]})}},81570:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,39132)),Promise.resolve().then(c.bind(c,19055)),Promise.resolve().then(c.bind(c,97615)),Promise.resolve().then(c.bind(c,67473)),Promise.resolve().then(c.bind(c,46661)),Promise.resolve().then(c.bind(c,58570))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91559:(a,b,c)=>{"use strict";c.d(b,{MetricsOverview:()=>m});var d=c(60687),e=c(43210),f=c(41312),g=c(5336),h=c(48730),i=c(43649);let j=(0,c(62688).A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var k=c(40228),l=c(35071);function m({period:a}){let[b,c]=(0,e.useState)(null),[m,n]=(0,e.useState)(!0);if(m||!b)return(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 animate-pulse",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-16 h-4 bg-gray-200 rounded"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded mb-2"}),(0,d.jsx)("div",{className:"w-24 h-4 bg-gray-200 rounded"})]},b))});let o=[{title:"Funcion\xe1rios Ativos",value:b.funcionariosAtivos,total:b.totalFuncionarios,icon:f.A,color:"blue",trend:"+2.5%",description:"funcion\xe1rios trabalhando"},{title:"Frequ\xeancia M\xe9dia",value:`${b.frequenciaMedia}%`,icon:g.A,color:"green",trend:"+1.2%",description:"de presen\xe7a no per\xedodo"},{title:"Horas Trabalhadas",value:`${b.horasTrabalhadasTotal}h`,icon:h.A,color:"purple",trend:"+5.8%",description:"total no per\xedodo"},{title:"Alertas Pendentes",value:b.alertasTotal,icon:i.A,color:"red",trend:"-12%",description:"inconsist\xeancias detectadas"}];return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map((a,b)=>{var c;return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:`p-2 rounded-lg ${(a=>{let b={blue:"bg-blue-100 text-blue-600",green:"bg-green-100 text-green-600",purple:"bg-purple-100 text-purple-600",red:"bg-red-100 text-red-600",yellow:"bg-yellow-100 text-yellow-600"};return b[a]||b.blue})(a.color)}`,children:(0,d.jsx)(a.icon,{className:"h-6 w-6"})}),(0,d.jsx)("span",{className:`text-sm font-medium ${(c=a.trend).startsWith("+")?"text-green-600":c.startsWith("-")?"text-red-600":"text-gray-600"}`,children:a.trend})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:a.value}),a.total&&(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["/ ",a.total]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.description}),(0,d.jsx)("p",{className:"text-xs font-medium text-gray-900",children:a.title})]})]},b)})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 text-blue-600 rounded-lg",children:(0,d.jsx)(j,{className:"h-5 w-5"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Horas Extras"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Total do per\xedodo"}),(0,d.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[b.horasExtrasTotal,"h"]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"M\xe9dia por funcion\xe1rio"}),(0,d.jsxs)("span",{className:"text-sm text-gray-900",children:[(b.horasExtrasTotal/b.funcionariosAtivos).toFixed(1),"h"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${Math.min(b.horasExtrasTotal/100*100,100)}%`}})})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 text-green-600 rounded-lg",children:(0,d.jsx)(k.A,{className:"h-5 w-5"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dias Trabalhados"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Dias \xfateis"}),(0,d.jsx)("span",{className:"text-lg font-bold text-gray-900",children:b.diasUteis})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Trabalhados"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:b.diasTrabalhados})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${b.diasTrabalhados/b.diasUteis*100}%`}})})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"p-2 bg-yellow-100 text-yellow-600 rounded-lg",children:(0,d.jsx)(l.A,{className:"h-5 w-5"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Absente\xedsmo"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Taxa do per\xedodo"}),(0,d.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[b.absenteismo,"%"]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Registros pendentes"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:b.registrosPendentes})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-yellow-600 h-2 rounded-full",style:{width:`${Math.min(10*b.absenteismo,100)}%`}})})]})]})]})]})}},97615:(a,b,c)=>{"use strict";c.d(b,{FrequencyChart:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call FrequencyChart() from the server but FrequencyChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\periodo-apuracao\\frequency-chart.tsx","FrequencyChart")},99722:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,33282)),Promise.resolve().then(c.bind(c,24085)),Promise.resolve().then(c.bind(c,77557)),Promise.resolve().then(c.bind(c,91559)),Promise.resolve().then(c.bind(c,13691)),Promise.resolve().then(c.bind(c,98316))}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,327,556,40,121],()=>b(b.s=20912));module.exports=c})();