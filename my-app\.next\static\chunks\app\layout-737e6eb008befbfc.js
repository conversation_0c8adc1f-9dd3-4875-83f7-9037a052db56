(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},6062:(e,t,o)=>{"use strict";o.d(t,{SessionProvider:()=>s});var l=o(5155),a=o(2115);let r=(0,a.createContext)(void 0);function s(e){let{children:t}=e,[o,s]=(0,a.useState)(null),[n,i]=(0,a.useState)(!0),[c,u]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{u(!0);try{{let e=localStorage.getItem("rlponto_user");if(e){let t=JSON.parse(e);s(t)}}}catch(e){console.error("Erro ao recuperar usu\xe1rio do localStorage:",e),localStorage.removeItem("rlponto_user")}finally{i(!1)}},[]),(0,l.jsx)(r.Provider,{value:{user:o,isLoading:n||!c,login:e=>{s(e),localStorage.setItem("rlponto_user",JSON.stringify(e))},logout:()=>{s(null),localStorage.removeItem("rlponto_user"),window.location.href="/login"},isAuthenticated:!!o&&c},children:t})}},6767:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,2093,23)),Promise.resolve().then(o.t.bind(o,7735,23)),Promise.resolve().then(o.t.bind(o,347,23)),Promise.resolve().then(o.bind(o,6062))},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{e.O(0,[360,441,964,358],()=>e(e.s=6767)),_N_E=e.O()}]);