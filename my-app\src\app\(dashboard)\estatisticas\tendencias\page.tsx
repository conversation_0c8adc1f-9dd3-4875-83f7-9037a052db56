import { Metadata } from 'next';
import { 
  Activity, 
  ArrowLeft, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  BarChart3,
  Download,
  Clock
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'An<PERSON><PERSON><PERSON> de Tendências - RLPONTO',
  description: 'Análise temporal e identificação de tendências',
};

// Dados mockados para demonstração
const tendenciasData = {
  resumo: {
    tendenciaGeral: 'positiva',
    crescimentoMensal: 2.3,
    sazonalidade: 'detectada',
    previsaoProximoMes: 89.2
  },
  metricas: [
    { nome: 'Pontualidade', atual: 94.2, anterior: 92.1, tendencia: 'up', variacao: 2.1 },
    { nome: 'Produtividade', atual: 87.6, anterior: 85.3, tendencia: 'up', variacao: 2.3 },
    { nome: 'Absenteísmo', atual: 3.8, anterior: 4.3, tendencia: 'down', variacao: -0.5 },
    { nome: 'Horas Extras', atual: 127.5, anterior: 112.3, tendencia: 'up', variacao: 15.2 }
  ],
  padroesSazonais: [
    { periodo: 'Janeiro', pontualidade: 91.2, produtividade: 84.1, absenteismo: 5.2 },
    { periodo: 'Fevereiro', pontualidade: 89.8, produtividade: 82.3, absenteismo: 6.1 },
    { periodo: 'Março', pontualidade: 92.5, produtividade: 86.7, absenteismo: 4.8 },
    { periodo: 'Abril', pontualidade: 93.1, produtividade: 87.2, absenteismo: 4.2 },
    { periodo: 'Maio', pontualidade: 94.2, produtividade: 88.9, absenteismo: 3.8 },
    { periodo: 'Junho', pontualidade: 95.1, produtividade: 89.5, absenteismo: 3.2 }
  ],
  alertas: [
    {
      tipo: 'tendencia_negativa',
      titulo: 'Queda na Pontualidade - Sextas-feiras',
      descricao: 'Detectada tendência de queda na pontualidade às sextas-feiras nos últimos 3 meses.',
      severidade: 'media'
    },
    {
      tipo: 'sazonalidade',
      titulo: 'Padrão Sazonal - Início do Ano',
      descricao: 'Historicamente, janeiro e fevereiro apresentam menor produtividade.',
      severidade: 'baixa'
    },
    {
      tipo: 'crescimento',
      titulo: 'Melhoria Contínua - Departamento TI',
      descricao: 'Departamento de TI mostra crescimento consistente na produtividade.',
      severidade: 'positiva'
    }
  ]
};

export default function TendenciasPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Link href="/estatisticas">
                <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </button>
              </Link>
              <div className="p-2 bg-purple-600 rounded-lg">
                <Activity className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Análise de Tendências</h1>
                <p className="text-gray-600">Identificação de padrões temporais e previsões</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option>Últimos 6 meses</option>
                <option>Último ano</option>
                <option>Últimos 2 anos</option>
              </select>
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>

          {/* Resumo das Tendências */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tendência Geral</p>
                  <p className="text-lg font-semibold text-green-600 capitalize">
                    {tendenciasData.resumo.tendenciaGeral}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Crescimento Mensal</p>
                  <p className="text-lg font-semibold text-blue-600">
                    +{tendenciasData.resumo.crescimentoMensal}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Sazonalidade</p>
                  <p className="text-lg font-semibold text-yellow-600 capitalize">
                    {tendenciasData.resumo.sazonalidade}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Previsão Próximo Mês</p>
                  <p className="text-lg font-semibold text-purple-600">
                    {tendenciasData.resumo.previsaoProximoMes}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Gráfico de Tendências (Placeholder) */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Evolução Temporal das Métricas</h2>
            </div>
            <div className="p-6">
              <div className="h-80 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Gráfico de Tendências Temporais</p>
                  <p className="text-sm text-gray-400">Implementação com Chart.js ou Recharts</p>
                  <p className="text-xs text-gray-400 mt-2">
                    Mostrará linhas de tendência para pontualidade, produtividade e absenteísmo
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Comparação de Métricas */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Comparação Mensal</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {tendenciasData.metricas.map((metrica, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-3 ${
                          metrica.tendencia === 'up' ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{metrica.nome}</p>
                          <p className="text-xs text-gray-500">
                            Anterior: {metrica.anterior}
                            {metrica.nome === 'Horas Extras' ? 'h' : '%'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {metrica.atual}{metrica.nome === 'Horas Extras' ? 'h' : '%'}
                        </p>
                        <p className={`text-xs font-medium ${
                          metrica.tendencia === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {metrica.variacao > 0 ? '+' : ''}{metrica.variacao}
                          {metrica.nome === 'Horas Extras' ? 'h' : '%'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Padrões Sazonais */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Padrões Sazonais</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {tendenciasData.padroesSazonais.map((periodo, index) => (
                    <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                      <span className="text-sm font-medium text-gray-900">{periodo.periodo}</span>
                      <div className="flex space-x-4 text-xs">
                        <span className="text-green-600">P: {periodo.pontualidade}%</span>
                        <span className="text-blue-600">Prod: {periodo.produtividade}%</span>
                        <span className="text-red-600">Abs: {periodo.absenteismo}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Alertas e Insights */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Alertas e Insights</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {tendenciasData.alertas.map((alerta, index) => (
                  <div key={index} className={`p-4 rounded-lg border ${
                    alerta.severidade === 'positiva' ? 'bg-green-50 border-green-200' :
                    alerta.severidade === 'media' ? 'bg-yellow-50 border-yellow-200' :
                    'bg-blue-50 border-blue-200'
                  }`}>
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        {alerta.severidade === 'positiva' ? (
                          <TrendingUp className="h-5 w-5 text-green-600" />
                        ) : alerta.severidade === 'media' ? (
                          <TrendingDown className="h-5 w-5 text-yellow-600" />
                        ) : (
                          <Activity className="h-5 w-5 text-blue-600" />
                        )}
                      </div>
                      <div className="ml-3">
                        <h3 className={`text-sm font-medium ${
                          alerta.severidade === 'positiva' ? 'text-green-800' :
                          alerta.severidade === 'media' ? 'text-yellow-800' :
                          'text-blue-800'
                        }`}>
                          {alerta.titulo}
                        </h3>
                        <p className={`text-sm mt-1 ${
                          alerta.severidade === 'positiva' ? 'text-green-700' :
                          alerta.severidade === 'media' ? 'text-yellow-700' :
                          'text-blue-700'
                        }`}>
                          {alerta.descricao}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Previsões */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Previsões Baseadas em Tendências</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-green-800">Próximo Mês</h3>
                  <p className="text-sm text-green-700">
                    Baseado na tendência atual, espera-se melhoria de 1.5% na produtividade geral.
                  </p>
                </div>

                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Calendar className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-blue-800">Próximo Trimestre</h3>
                  <p className="text-sm text-blue-700">
                    Padrão sazonal indica possível queda de 2% no absenteísmo durante o verão.
                  </p>
                </div>

                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-purple-800">Longo Prazo</h3>
                  <p className="text-sm text-purple-700">
                    Tendência de crescimento sustentável com melhoria contínua de 0.5% ao mês.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

