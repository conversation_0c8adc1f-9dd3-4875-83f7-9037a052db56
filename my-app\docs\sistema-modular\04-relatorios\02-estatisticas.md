# 📈 MÓDULO ESTATÍSTICAS - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável por análises estatísticas avançadas, KPIs, dashboards executivos e insights sobre dados de ponto e produtividade.

## 🎯 Funcionalidades
- Dashboard executivo com KPIs
- Análises de tendências temporais
- Comparativos entre períodos
- Estatísticas de produtividade
- Análise de absenteísmo
- Métricas de pontualidade
- Heatmaps de frequência
- Previsões e projeções
- Alertas inteligentes
- Benchmarking departamental

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── estatisticas/
│           ├── page.tsx                    # Dashboard principal
│           ├── produtividade/
│           │   └── page.tsx                # Análise de produtividade
│           ├── absenteismo/
│           │   └── page.tsx                # Análise de absenteísmo
│           ├── tendencias/
│           │   └── page.tsx                # Análise de tendências
│           ├── comparativos/
│           │   └── page.tsx                # Comparativos
│           └── components/
│               ├── kpi-dashboard.tsx       # Dashboard de KPIs
│               ├── trend-chart.tsx         # Gráfico de tendências
│               ├── heatmap.tsx             # Mapa de calor
│               ├── comparison-chart.tsx    # Gráfico comparativo
│               └── insights-panel.tsx      # Painel de insights
├── components/
│   └── estatisticas/
│       ├── metric-card.tsx                # Card de métrica
│       ├── chart-container.tsx            # Container de gráficos
│       ├── data-visualization.tsx         # Visualizações de dados
│       ├── period-selector.tsx            # Seletor de período
│       └── export-dashboard.tsx           # Exportação de dashboard
└── api/
    └── estatisticas/
        ├── route.ts                       # API principal
        ├── kpis/
        │   └── route.ts                   # KPIs
        ├── tendencias/
        │   └── route.ts                   # Análise de tendências
        ├── comparativos/
        │   └── route.ts                   # Dados comparativos
        └── insights/
            └── route.ts                   # Insights automáticos
```

## 🔧 Implementação Técnica

### 📈 Dashboard Principal (page.tsx)
```typescript
// app/(dashboard)/estatisticas/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { KpiDashboard } from './components/kpi-dashboard';
import { TrendChart } from './components/trend-chart';
import { Heatmap } from './components/heatmap';
import { InsightsPanel } from './components/insights-panel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3, TrendingUp, Users, Clock, Target, Download, Calendar, AlertTriangle } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Estatísticas - RLPONTO',
  description: 'Análises estatísticas e KPIs do sistema de ponto',
};

interface EstatisticasPageProps {
  searchParams: {
    periodo?: string;
    departamento?: string;
    view?: string;
  };
}

export default function EstatisticasPage({ searchParams }: EstatisticasPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Estatísticas</h1>
            <p className="text-gray-600">Análises avançadas e insights de produtividade</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar Dashboard
          </Button>
          <Button asChild>
            <Link href="/estatisticas/relatorio">
              <BarChart3 className="h-4 w-4 mr-2" />
              Relatório Executivo
            </Link>
          </Button>
        </div>
      </div>

      {/* KPIs Principais */}
      <Suspense fallback={<KpiSkeleton />}>
        <KpiDashboard searchParams={searchParams} />
      </Suspense>

      {/* Tabs de Análises */}
      <Tabs defaultValue="visao-geral" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="visao-geral">Visão Geral</TabsTrigger>
          <TabsTrigger value="produtividade">Produtividade</TabsTrigger>
          <TabsTrigger value="absenteismo">Absenteísmo</TabsTrigger>
          <TabsTrigger value="tendencias">Tendências</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Visão Geral */}
        <TabsContent value="visao-geral" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Gráfico de Tendências */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  <span>Tendência de Frequência</span>
                </CardTitle>
                <CardDescription>
                  Evolução da frequência nos últimos 6 meses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Suspense fallback={<ChartSkeleton />}>
                  <TrendChart type="frequencia" period="6m" />
                </Suspense>
              </CardContent>
            </Card>

            {/* Heatmap de Presença */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-green-600" />
                  <span>Mapa de Presença</span>
                </CardTitle>
                <CardDescription>
                  Distribuição de presença por dia da semana
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Suspense fallback={<ChartSkeleton />}>
                  <Heatmap type="presenca" />
                </Suspense>
              </CardContent>
            </Card>
          </div>

          {/* Métricas por Departamento */}
          <Card>
            <CardHeader>
              <CardTitle>Performance por Departamento</CardTitle>
              <CardDescription>
                Comparativo de métricas entre departamentos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<TableSkeleton />}>
                <DepartmentMetrics />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Produtividade */}
        <TabsContent value="produtividade" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Horas Trabalhadas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">8,234h</div>
                <p className="text-sm text-gray-600">+12% vs mês anterior</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Produtividade Média</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">94.2%</div>
                <p className="text-sm text-gray-600">+2.1% vs mês anterior</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Horas Extras</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-600">234h</div>
                <p className="text-sm text-gray-600">-8% vs mês anterior</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Análise de Produtividade</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<ChartSkeleton />}>
                <ProductivityAnalysis />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Absenteísmo */}
        <TabsContent value="absenteismo" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Taxa de Absenteísmo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">3.2%</div>
                <p className="text-xs text-gray-600">Meta: < 5%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Faltas Justificadas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">67%</div>
                <p className="text-xs text-gray-600">das faltas totais</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Atrasos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">89</div>
                <p className="text-xs text-gray-600">este mês</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Saídas Antecipadas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">23</div>
                <p className="text-xs text-gray-600">este mês</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Análise de Absenteísmo</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<ChartSkeleton />}>
                <AbsenteeismAnalysis />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tendências */}
        <TabsContent value="tendencias" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Tendências</CardTitle>
              <CardDescription>
                Identificação de padrões e tendências nos dados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<ChartSkeleton />}>
                <TrendAnalysis />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights */}
        <TabsContent value="insights" className="space-y-4">
          <Suspense fallback={<InsightsSkeleton />}>
            <InsightsPanel />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Skeletons
function KpiSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-24 bg-gray-200 rounded-lg animate-pulse" />
      ))}
    </div>
  );
}

function ChartSkeleton() {
  return <div className="h-80 bg-gray-200 rounded animate-pulse" />;
}

function TableSkeleton() {
  return (
    <div className="space-y-2">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}

function InsightsSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-20 bg-gray-200 rounded-lg animate-pulse" />
      ))}
    </div>
  );
}

// Placeholder components
function DepartmentMetrics() {
  return <div>Department metrics component</div>;
}

function ProductivityAnalysis() {
  return <div>Productivity analysis component</div>;
}

function AbsenteeismAnalysis() {
  return <div>Absenteeism analysis component</div>;
}

function TrendAnalysis() {
  return <div>Trend analysis component</div>;
}
```

### 📊 Dashboard de KPIs (kpi-dashboard.tsx)
```typescript
// app/(dashboard)/estatisticas/components/kpi-dashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, TrendingDown, Users, Clock, Target, AlertTriangle, CheckCircle, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface KPI {
  id: string;
  title: string;
  value: string | number;
  previousValue?: string | number;
  target?: string | number;
  unit?: string;
  trend: 'up' | 'down' | 'stable';
  trendValue: number;
  status: 'good' | 'warning' | 'critical';
  icon: any;
  description: string;
}

interface KpiDashboardProps {
  searchParams: {
    periodo?: string;
    departamento?: string;
  };
}

export function KpiDashboard({ searchParams }: KpiDashboardProps) {
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchKpis();
  }, [searchParams]);

  const fetchKpis = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchParams.periodo) params.append('periodo', searchParams.periodo);
      if (searchParams.departamento) params.append('departamento', searchParams.departamento);

      const response = await fetch(`/api/estatisticas/kpis?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setKpis(data.kpis);
      }
    } catch (error) {
      console.error('Erro ao buscar KPIs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      good: { label: 'Bom', variant: 'default' as const, className: 'bg-green-100 text-green-800' },
      warning: { label: 'Atenção', variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800' },
      critical: { label: 'Crítico', variant: 'destructive' as const, className: 'bg-red-100 text-red-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.good;
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const calculateProgress = (value: number, target: number) => {
    return Math.min((value / target) * 100, 100);
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  // Dados simulados se não houver dados da API
  const defaultKpis: KPI[] = [
    {
      id: 'frequencia',
      title: 'Taxa de Frequência',
      value: 96.8,
      target: 95,
      unit: '%',
      trend: 'up',
      trendValue: 2.1,
      status: 'good',
      icon: Users,
      description: 'Percentual de presença dos funcionários',
    },
    {
      id: 'pontualidade',
      title: 'Pontualidade',
      value: 89.2,
      target: 90,
      unit: '%',
      trend: 'down',
      trendValue: -1.5,
      status: 'warning',
      icon: Clock,
      description: 'Funcionários que chegam no horário',
    },
    {
      id: 'absenteismo',
      title: 'Taxa de Absenteísmo',
      value: 3.2,
      target: 5,
      unit: '%',
      trend: 'down',
      trendValue: -0.8,
      status: 'good',
      icon: AlertTriangle,
      description: 'Percentual de faltas não justificadas',
    },
    {
      id: 'produtividade',
      title: 'Índice de Produtividade',
      value: 94.5,
      target: 90,
      unit: '%',
      trend: 'up',
      trendValue: 3.2,
      status: 'good',
      icon: Target,
      description: 'Índice geral de produtividade',
    },
    {
      id: 'horas_extras',
      title: 'Horas Extras',
      value: 234,
      previousValue: 256,
      unit: 'h',
      trend: 'down',
      trendValue: -8.6,
      status: 'good',
      icon: Clock,
      description: 'Total de horas extras no período',
    },
    {
      id: 'funcionarios_ativos',
      title: 'Funcionários Ativos',
      value: 155,
      previousValue: 152,
      unit: '',
      trend: 'up',
      trendValue: 2.0,
      status: 'good',
      icon: Users,
      description: 'Número de funcionários ativos',
    },
    {
      id: 'registros_pendentes',
      title: 'Registros Pendentes',
      value: 12,
      previousValue: 18,
      unit: '',
      trend: 'down',
      trendValue: -33.3,
      status: 'warning',
      icon: AlertTriangle,
      description: 'Registros aguardando aprovação',
    },
    {
      id: 'dias_trabalhados',
      title: 'Dias Trabalhados',
      value: 22,
      target: 22,
      unit: 'dias',
      trend: 'stable',
      trendValue: 0,
      status: 'good',
      icon: Calendar,
      description: 'Dias úteis trabalhados no mês',
    },
  ];

  const kpisToShow = kpis.length > 0 ? kpis : defaultKpis;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {kpisToShow.map((kpi) => {
        const Icon = kpi.icon;
        const hasTarget = kpi.target !== undefined;
        const progress = hasTarget ? calculateProgress(Number(kpi.value), Number(kpi.target)) : 0;

        return (
          <Card key={kpi.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
              <Icon className={cn('h-4 w-4', getStatusColor(kpi.status))} />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {/* Valor Principal */}
                <div className="flex items-baseline space-x-2">
                  <div className={cn('text-2xl font-bold', getStatusColor(kpi.status))}>
                    {kpi.value}
                    {kpi.unit && <span className="text-sm ml-1">{kpi.unit}</span>}
                  </div>
                  {getStatusBadge(kpi.status)}
                </div>

                {/* Tendência */}
                <div className="flex items-center space-x-2 text-sm">
                  {getTrendIcon(kpi.trend)}
                  <span className={cn(
                    kpi.trend === 'up' ? 'text-green-600' : 
                    kpi.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  )}>
                    {kpi.trendValue > 0 ? '+' : ''}{kpi.trendValue}%
                  </span>
                  <span className="text-gray-500">vs anterior</span>
                </div>

                {/* Meta (se houver) */}
                {hasTarget && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>Meta: {kpi.target}{kpi.unit}</span>
                      <span>{progress.toFixed(1)}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>
                )}

                {/* Descrição */}
                <p className="text-xs text-gray-500">{kpi.description}</p>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
```

### 🔍 Painel de Insights (insights-panel.tsx)
```typescript
// app/(dashboard)/estatisticas/components/insights-panel.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lightbulb, TrendingUp, AlertTriangle, CheckCircle, Target, Users, Clock, Calendar } from 'lucide-react';

interface Insight {
  id: string;
  type: 'trend' | 'alert' | 'opportunity' | 'achievement';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  data: any;
  actionable: boolean;
  createdAt: string;
}

export function InsightsPanel() {
  const [insights, setInsights] = useState<Insight[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchInsights();
  }, []);

  const fetchInsights = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/estatisticas/insights');
      if (response.ok) {
        const data = await response.json();
        setInsights(data.insights);
      }
    } catch (error) {
      console.error('Erro ao buscar insights:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <TrendingUp className="h-5 w-5 text-blue-600" />;
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'opportunity':
        return <Target className="h-5 w-5 text-green-600" />;
      case 'achievement':
        return <CheckCircle className="h-5 w-5 text-purple-600" />;
      default:
        return <Lightbulb className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getImpactBadge = (impact: string) => {
    const impactConfig = {
      high: { label: 'Alto Impacto', className: 'bg-red-100 text-red-800' },
      medium: { label: 'Médio Impacto', className: 'bg-yellow-100 text-yellow-800' },
      low: { label: 'Baixo Impacto', className: 'bg-blue-100 text-blue-800' },
    };

    const config = impactConfig[impact as keyof typeof impactConfig] || impactConfig.medium;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const getTypeLabel = (type: string) => {
    const typeLabels = {
      trend: 'Tendência',
      alert: 'Alerta',
      opportunity: 'Oportunidade',
      achievement: 'Conquista',
    };
    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  // Dados simulados se não houver dados da API
  const defaultInsights: Insight[] = [
    {
      id: '1',
      type: 'alert',
      title: 'Aumento de Atrasos no Departamento de TI',
      description: 'O departamento de TI apresentou um aumento de 25% nos atrasos nas últimas 2 semanas. Isso pode indicar problemas de transporte ou desmotivação da equipe.',
      impact: 'high',
      category: 'Pontualidade',
      data: { department: 'TI', increase: 25, period: '2 semanas' },
      actionable: true,
      createdAt: '2024-03-15T10:30:00Z',
    },
    {
      id: '2',
      type: 'opportunity',
      title: 'Potencial de Redução de Horas Extras',
      description: 'Análise mostra que 30% das horas extras poderiam ser evitadas com melhor distribuição de tarefas entre os turnos.',
      impact: 'medium',
      category: 'Produtividade',
      data: { potential_reduction: 30, cost_savings: 15000 },
      actionable: true,
      createdAt: '2024-03-14T14:15:00Z',
    },
    {
      id: '3',
      type: 'achievement',
      title: 'Meta de Frequência Alcançada',
      description: 'A empresa atingiu 96.8% de frequência este mês, superando a meta de 95%. Parabéns à equipe!',
      impact: 'medium',
      category: 'Frequência',
      data: { achieved: 96.8, target: 95 },
      actionable: false,
      createdAt: '2024-03-13T09:45:00Z',
    },
    {
      id: '4',
      type: 'trend',
      title: 'Tendência de Melhoria na Pontualidade',
      description: 'A pontualidade vem melhorando consistentemente nos últimos 3 meses, com aumento médio de 2% ao mês.',
      impact: 'low',
      category: 'Pontualidade',
      data: { trend: 'improving', rate: 2, period: '3 meses' },
      actionable: false,
      createdAt: '2024-03-12T16:20:00Z',
    },
    {
      id: '5',
      type: 'alert',
      title: 'Padrão de Faltas às Sextas-feiras',
      description: 'Identificado padrão de aumento de faltas às sextas-feiras (40% acima da média). Considere investigar as causas.',
      impact: 'medium',
      category: 'Absenteísmo',
      data: { day: 'sexta', increase: 40 },
      actionable: true,
      createdAt: '2024-03-11T11:10:00Z',
    },
  ];

  const insightsToShow = insights.length > 0 ? insights : defaultInsights;

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="h-6 w-6 text-yellow-600" />
            <span>Insights Inteligentes</span>
          </CardTitle>
          <CardDescription>
            Análises automáticas e recomendações baseadas nos dados do sistema
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Insights */}
      <div className="space-y-4">
        {insightsToShow.map((insight) => (
          <Card key={insight.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-gray-900">{insight.title}</h3>
                      <Badge variant="outline">{getTypeLabel(insight.type)}</Badge>
                      {getImpactBadge(insight.impact)}
                    </div>
                    <p className="text-gray-600 text-sm">{insight.description}</p>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(insight.createdAt).toLocaleDateString('pt-BR')}
                </div>
              </div>
            </CardHeader>
            
            {insight.actionable && (
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    <Target className="h-3 w-3 mr-1" />
                    Ação Recomendada
                  </Badge>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      Ver Detalhes
                    </Button>
                    <Button size="sm">
                      Criar Ação
                    </Button>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Resumo de Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Resumo de Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {insightsToShow.filter(i => i.type === 'alert').length}
              </div>
              <p className="text-sm text-gray-600">Alertas</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {insightsToShow.filter(i => i.type === 'opportunity').length}
              </div>
              <p className="text-sm text-gray-600">Oportunidades</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {insightsToShow.filter(i => i.type === 'trend').length}
              </div>
              <p className="text-sm text-gray-600">Tendências</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {insightsToShow.filter(i => i.type === 'achievement').length}
              </div>
              <p className="text-sm text-gray-600">Conquistas</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 🔌 API Routes

### 📈 API de KPIs (kpis/route.ts)
```typescript
// app/api/estatisticas/kpis/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { startOfMonth, endOfMonth, subMonths } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const periodo = searchParams.get('periodo') || 'current';
    const departamento = searchParams.get('departamento');

    // Definir período
    let dataInicio: Date;
    let dataFim: Date;

    if (periodo === 'current') {
      dataInicio = startOfMonth(new Date());
      dataFim = endOfMonth(new Date());
    } else {
      // Implementar outros períodos conforme necessário
      dataInicio = startOfMonth(new Date());
      dataFim = endOfMonth(new Date());
    }

    const periodoAnterior = {
      inicio: startOfMonth(subMonths(dataInicio, 1)),
      fim: endOfMonth(subMonths(dataInicio, 1)),
    };

    // Filtros
    const whereClause: any = {
      data: { gte: dataInicio, lte: dataFim },
    };

    if (departamento) {
      whereClause.funcionario = {
        departamento,
      };
    }

    // Buscar dados
    const [
      totalFuncionarios,
      funcionariosAtivos,
      registrosPonto,
      registrosAnterior,
      faltas,
      faltasAnterior,
      atrasos,
      atrasosAnterior,
      horasExtras,
      horasExtrasAnterior,
    ] = await Promise.all([
      prisma.funcionario.count({
        where: departamento ? { departamento, ativo: true } : { ativo: true },
      }),
      prisma.funcionario.count({
        where: departamento ? { departamento, ativo: true } : { ativo: true },
      }),
      prisma.registroPonto.count({ where: whereClause }),
      prisma.registroPonto.count({
        where: {
          ...whereClause,
          data: { gte: periodoAnterior.inicio, lte: periodoAnterior.fim },
        },
      }),
      prisma.registroPonto.count({
        where: { ...whereClause, falta: true },
      }),
      prisma.registroPonto.count({
        where: {
          ...whereClause,
          data: { gte: periodoAnterior.inicio, lte: periodoAnterior.fim },
          falta: true,
        },
      }),
      prisma.registroPonto.count({
        where: { ...whereClause, atraso: { gt: 0 } },
      }),
      prisma.registroPonto.count({
        where: {
          ...whereClause,
          data: { gte: periodoAnterior.inicio, lte: periodoAnterior.fim },
          atraso: { gt: 0 },
        },
      }),
      prisma.registroClassificacao.aggregate({
        where: {
          data: { gte: dataInicio, lte: dataFim },
          tipoHora: { in: ['extra_50', 'extra_100'] },
        },
        _sum: { horasTrabalhadas: true },
      }),
      prisma.registroClassificacao.aggregate({
        where: {
          data: { gte: periodoAnterior.inicio, lte: periodoAnterior.fim },
          tipoHora: { in: ['extra_50', 'extra_100'] },
        },
        _sum: { horasTrabalhadas: true },
      }),
    ]);

    // Calcular KPIs
    const diasUteis = calcularDiasUteis(dataInicio, dataFim);
    const registrosEsperados = funcionariosAtivos * diasUteis;
    
    const frequencia = registrosEsperados > 0 ? ((registrosPonto / registrosEsperados) * 100) : 0;
    const frequenciaAnterior = registrosAnterior > 0 ? ((registrosAnterior / registrosEsperados) * 100) : 0;
    
    const absenteismo = registrosPonto > 0 ? ((faltas / registrosPonto) * 100) : 0;
    const absenteismoAnterior = registrosAnterior > 0 ? ((faltasAnterior / registrosAnterior) * 100) : 0;
    
    const pontualidade = registrosPonto > 0 ? (((registrosPonto - atrasos) / registrosPonto) * 100) : 0;
    const pontualidadeAnterior = registrosAnterior > 0 ? (((registrosAnterior - atrasosAnterior) / registrosAnterior) * 100) : 0;

    const kpis = [
      {
        id: 'frequencia',
        title: 'Taxa de Frequência',
        value: Number(frequencia.toFixed(1)),
        target: 95,
        unit: '%',
        trend: frequencia > frequenciaAnterior ? 'up' : frequencia < frequenciaAnterior ? 'down' : 'stable',
        trendValue: Number(((frequencia - frequenciaAnterior) / frequenciaAnterior * 100).toFixed(1)),
        status: frequencia >= 95 ? 'good' : frequencia >= 90 ? 'warning' : 'critical',
        icon: 'Users',
        description: 'Percentual de presença dos funcionários',
      },
      {
        id: 'pontualidade',
        title: 'Pontualidade',
        value: Number(pontualidade.toFixed(1)),
        target: 90,
        unit: '%',
        trend: pontualidade > pontualidadeAnterior ? 'up' : pontualidade < pontualidadeAnterior ? 'down' : 'stable',
        trendValue: Number(((pontualidade - pontualidadeAnterior) / pontualidadeAnterior * 100).toFixed(1)),
        status: pontualidade >= 90 ? 'good' : pontualidade >= 80 ? 'warning' : 'critical',
        icon: 'Clock',
        description: 'Funcionários que chegam no horário',
      },
      {
        id: 'absenteismo',
        title: 'Taxa de Absenteísmo',
        value: Number(absenteismo.toFixed(1)),
        target: 5,
        unit: '%',
        trend: absenteismo < absenteismoAnterior ? 'up' : absenteismo > absenteismoAnterior ? 'down' : 'stable',
        trendValue: Number(((absenteismo - absenteismoAnterior) / absenteismoAnterior * 100).toFixed(1)),
        status: absenteismo <= 5 ? 'good' : absenteismo <= 10 ? 'warning' : 'critical',
        icon: 'AlertTriangle',
        description: 'Percentual de faltas não justificadas',
      },
      {
        id: 'horas_extras',
        title: 'Horas Extras',
        value: Math.round((horasExtras._sum.horasTrabalhadas || 0) / 60),
        previousValue: Math.round((horasExtrasAnterior._sum.horasTrabalhadas || 0) / 60),
        unit: 'h',
        trend: (horasExtras._sum.horasTrabalhadas || 0) < (horasExtrasAnterior._sum.horasTrabalhadas || 0) ? 'down' : 'up',
        trendValue: horasExtrasAnterior._sum.horasTrabalhadas ? 
          Number((((horasExtras._sum.horasTrabalhadas || 0) - (horasExtrasAnterior._sum.horasTrabalhadas || 0)) / (horasExtrasAnterior._sum.horasTrabalhadas || 1) * 100).toFixed(1)) : 0,
        status: 'good',
        icon: 'Clock',
        description: 'Total de horas extras no período',
      },
    ];

    return NextResponse.json({ kpis });

  } catch (error) {
    console.error('Erro ao buscar KPIs:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

function calcularDiasUteis(inicio: Date, fim: Date): number {
  let count = 0;
  const current = new Date(inicio);
  
  while (current <= fim) {
    const dayOfWeek = current.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      count++;
    }
    current.setDate(current.getDate() + 1);
  }
  
  return count;
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Dashboard de KPIs
- [ ] Análises de tendências
- [ ] Comparativos entre períodos
- [ ] Estatísticas de produtividade
- [ ] Análise de absenteísmo
- [ ] Métricas de pontualidade
- [ ] Heatmaps e visualizações
- [ ] Insights automáticos
- [ ] Alertas inteligentes
- [ ] Exportação de dashboards

### 📊 Visualizações
- [ ] Gráficos de linha
- [ ] Gráficos de barras
- [ ] Mapas de calor
- [ ] Gráficos de pizza
- [ ] Indicadores de progresso
- [ ] Sparklines
- [ ] Dashboards interativos

## 🚀 Próximos Passos
1. **Empresa Principal** - Gestão da empresa
2. **Configurações de Ponto** - Parâmetros específicos
3. **Funcionários Desligados** - Gestão de ex-funcionários
