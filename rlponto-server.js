const http = require('http');

const server = http.createServer((req, res) => {
    res.writeHead(200, {'Content-Type': 'text/html; charset=utf-8'});
    
    if (req.url === '/') {
        // Página de Login
        res.end(`<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLPONTO - Sistema de Controle de Ponto</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            margin: 0; 
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        h1 { 
            color: #667eea; 
            margin-bottom: 1rem; 
            font-size: 2.5rem; 
        }
        .form-group { 
            margin-bottom: 1rem; 
            text-align: left; 
        }
        label { 
            display: block; 
            margin-bottom: 0.5rem; 
            color: #333; 
            font-weight: bold; 
        }
        input { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            font-size: 1rem; 
            box-sizing: border-box; 
        }
        input:focus { 
            outline: none; 
            border-color: #667eea; 
        }
        .btn { 
            width: 100%; 
            background: #667eea; 
            color: white; 
            padding: 0.75rem; 
            border: none; 
            border-radius: 5px; 
            font-size: 1rem; 
            cursor: pointer; 
            margin-top: 1rem; 
        }
        .btn:hover { 
            background: #5a67d8; 
        }
        .status { 
            background: #d4edda; 
            color: #155724; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🕐 RLPONTO</h1>
        <p style="color: #666; margin-bottom: 1.5rem;">Sistema de Controle de Ponto Eletrônico</p>
        
        <div class="status">
            ✅ Sistema Online e Funcionando!
        </div>
        
        <form action="/dashboard" method="post">
            <div class="form-group">
                <label for="usuario">Usuário:</label>
                <input type="text" id="usuario" name="usuario" required>
            </div>
            
            <div class="form-group">
                <label for="senha">Senha:</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            
            <button type="submit" class="btn">Entrar no Sistema</button>
        </form>
        
        <div style="margin-top: 1.5rem; font-size: 0.9rem; color: #666;">
            <strong>Versão:</strong> 1.0.0<br>
            <strong>Status:</strong> Operacional
        </div>
    </div>
</body>
</html>`);
        
    } else if (req.url === '/dashboard') {
        // Dashboard Principal
        res.end(`<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLPONTO - Dashboard</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #f8f9fa; 
            margin: 0; 
        }
        .header { 
            background: linear-gradient(135deg, #667eea, #764ba2); 
            color: white; 
            padding: 1rem 2rem; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .container { 
            padding: 2rem; 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 1rem; 
            margin-bottom: 2rem; 
        }
        .stat-card { 
            background: linear-gradient(135deg, #667eea, #764ba2); 
            color: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            text-align: center; 
        }
        .stat-number { 
            font-size: 2rem; 
            font-weight: bold; 
            margin-bottom: 0.5rem; 
        }
        .modules { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1rem; 
        }
        .module-card { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            text-align: center; 
            transition: transform 0.3s; 
        }
        .module-card:hover { 
            transform: translateY(-5px); 
        }
        .module-icon { 
            font-size: 3rem; 
            margin-bottom: 1rem; 
        }
        .btn { 
            background: #667eea; 
            color: white; 
            padding: 0.75rem 1.5rem; 
            border: none; 
            border-radius: 5px; 
            text-decoration: none; 
            display: inline-block; 
            margin: 0.5rem; 
        }
        .btn:hover { 
            background: #5a67d8; 
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🕐 RLPONTO - Dashboard</h1>
        <div>
            <span>Bem-vindo, Administrador</span>
            <a href="/" style="color: white; margin-left: 1rem;">Sair</a>
        </div>
    </div>
    
    <div class="container">
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">142</div>
                <div>Funcionários Cadastrados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div>Presentes Hoje</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">267</div>
                <div>Registros de Ponto</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">94%</div>
                <div>Taxa de Presença</div>
            </div>
        </div>
        
        <div class="modules">
            <div class="module-card">
                <div class="module-icon">👥</div>
                <h3>Gestão de Funcionários</h3>
                <p>CRUD completo, cadastro step-by-step, upload de documentos e gestão de departamentos</p>
                <a href="/funcionarios" class="btn">Acessar Módulo</a>
            </div>
            
            <div class="module-card">
                <div class="module-icon">🕐</div>
                <h3>Registro de Ponto</h3>
                <p>Registro biométrico e manual, geolocalização, validações automáticas</p>
                <a href="/ponto" class="btn">Registrar Ponto</a>
            </div>
            
            <div class="module-card">
                <div class="module-icon">📊</div>
                <h3>Relatórios e Análises</h3>
                <p>Relatórios detalhados, análises de produtividade, dashboards interativos</p>
                <a href="/relatorios" class="btn">Ver Relatórios</a>
            </div>
            
            <div class="module-card">
                <div class="module-icon">⚙️</div>
                <h3>Configurações</h3>
                <p>Parâmetros do sistema, integrações, segurança e auditoria</p>
                <a href="/config" class="btn">Configurar</a>
            </div>
        </div>
        
        <div style="background: white; padding: 1.5rem; border-radius: 10px; margin-top: 2rem; text-align: center;">
            <h3>Sistema RLPONTO</h3>
            <p><strong>Status:</strong> Online e Operacional</p>
            <p><strong>Versão:</strong> 1.0.0 | <strong>Servidor:</strong> Node.js</p>
            <p><strong>Última atualização:</strong> ${new Date().toLocaleString('pt-BR')}</p>
        </div>
    </div>
</body>
</html>`);
        
    } else {
        // Outras páginas
        res.end(`<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLPONTO - Módulo</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #f8f9fa; 
            margin: 0; 
            padding: 2rem; 
            text-align: center; 
        }
        .container { 
            background: white; 
            padding: 2rem; 
            border-radius: 10px; 
            max-width: 600px; 
            margin: 0 auto; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
        }
        .btn { 
            background: #667eea; 
            color: white; 
            padding: 0.75rem 1.5rem; 
            border: none; 
            border-radius: 5px; 
            text-decoration: none; 
            display: inline-block; 
            margin: 0.5rem; 
        }
        .btn:hover { 
            background: #5a67d8; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 RLPONTO</h1>
        <h2>Módulo em Desenvolvimento</h2>
        <p>Esta funcionalidade está sendo implementada conforme a documentação PRD.md</p>
        <p><strong>URL solicitada:</strong> ${req.url}</p>
        <p>O sistema RLPONTO possui as seguintes funcionalidades principais:</p>
        <ul style="text-align: left; max-width: 400px; margin: 1rem auto;">
            <li>Autenticação e Segurança</li>
            <li>Gestão de Funcionários</li>
            <li>Registro de Ponto (Biométrico/Manual)</li>
            <li>Relatórios e Análises</li>
            <li>Configurações do Sistema</li>
        </ul>
        <div style="margin-top: 2rem;">
            <a href="/dashboard" class="btn">Voltar ao Dashboard</a>
            <a href="/" class="btn">Voltar ao Login</a>
        </div>
    </div>
</body>
</html>`);
    }
});

server.listen(3000, '0.0.0.0', () => {
    console.log('🚀 RLPONTO Sistema Completo funcionando na porta 3000');
    console.log('✅ Baseado na documentação PRD.md');
    console.log('📋 Funcionalidades: Autenticação, Funcionários, Ponto, Relatórios, Configurações');
    console.log('🌐 Acesse: http://localhost:3000');
});

server.on('error', (err) => {
    console.error('❌ Erro no servidor:', err);
});
