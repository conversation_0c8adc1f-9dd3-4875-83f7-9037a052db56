{"/api/auth/callback/credentials/route": "app/api/auth/callback/credentials/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/funcionarios/route": "app/api/funcionarios/route.js", "/api/ponto/biometrico/route": "app/api/ponto/biometrico/route.js", "/api/ponto/manual/funcionarios/route": "app/api/ponto/manual/funcionarios/route.js", "/api/ponto/manual/route": "app/api/ponto/manual/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/dashboard/page": "app/dashboard/page.js", "/page": "app/page.js", "/login/page": "app/login/page.js", "/(dashboard)/administracao/page": "app/(dashboard)/administracao/page.js", "/(dashboard)/estatisticas/page": "app/(dashboard)/estatisticas/page.js", "/(dashboard)/funcionarios/novo/page": "app/(dashboard)/funcionarios/novo/page.js", "/(dashboard)/ponto/page": "app/(dashboard)/ponto/page.js", "/(dashboard)/estatisticas/tendencias/page": "app/(dashboard)/estatisticas/tendencias/page.js", "/(dashboard)/estatisticas/comparativos/page": "app/(dashboard)/estatisticas/comparativos/page.js", "/(dashboard)/funcionarios/page": "app/(dashboard)/funcionarios/page.js", "/(dashboard)/ponto/manual/page": "app/(dashboard)/ponto/manual/page.js", "/(dashboard)/estatisticas/absenteismo/page": "app/(dashboard)/estatisticas/absenteismo/page.js", "/(dashboard)/periodo-apuracao/page": "app/(dashboard)/periodo-apuracao/page.js", "/(dashboard)/estatisticas/produtividade/page": "app/(dashboard)/estatisticas/produtividade/page.js", "/(dashboard)/ponto/biometrico/page": "app/(dashboard)/ponto/biometrico/page.js", "/(dashboard)/relatorios/analiticos/page": "app/(dashboard)/relatorios/analiticos/page.js", "/(dashboard)/relatorios/funcionario/page": "app/(dashboard)/relatorios/funcionario/page.js", "/(dashboard)/relatorios/agendamentos/page": "app/(dashboard)/relatorios/agendamentos/page.js", "/(dashboard)/relatorios/page": "app/(dashboard)/relatorios/page.js", "/(dashboard)/relatorios/insights/page": "app/(dashboard)/relatorios/insights/page.js", "/(dashboard)/relatorios/periodo/page": "app/(dashboard)/relatorios/periodo/page.js", "/(dashboard)/relatorios/construtor/page": "app/(dashboard)/relatorios/construtor/page.js"}