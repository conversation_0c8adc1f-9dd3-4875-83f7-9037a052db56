'use client';

import { useEffect, useState } from 'react';
import { Input } from '@/components/ui';
import { Briefcase, Building, Clock, DollarSign } from 'lucide-react';
import { FuncionarioData } from './funcionario-wizard';

interface StepProfissionalProps {
  data: Partial<FuncionarioData>;
  onDataChange: (data: Partial<FuncionarioData>) => void;
  onValidationChange: (isValid: boolean) => void;
}

export function StepProfissional({ data, onDataChange, onValidationChange }: StepProfissionalProps) {
  const [formData, setFormData] = useState({
    matricula: data.matricula || '',
    cargo: data.cargo || '',
    setor: data.setor || '',
    dataAdmissao: data.dataAdmissao || '',
    salario: data.salario || 0,
    cargaHoraria: data.cargaHoraria || 40,
    horarioEntrada: data.horarioEntrada || '',
    horarioSaida: data.horarioSaida || '',
    intervaloInicio: data.intervaloInicio || '',
    intervaloFim: data.intervaloFim || '',
    observacoes: data.observacoes || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.matricula.trim()) {
      newErrors.matricula = 'Matrícula é obrigatória';
    }

    if (!formData.cargo.trim()) {
      newErrors.cargo = 'Cargo é obrigatório';
    }

    if (!formData.setor.trim()) {
      newErrors.setor = 'Setor é obrigatório';
    }

    if (!formData.dataAdmissao) {
      newErrors.dataAdmissao = 'Data de admissão é obrigatória';
    }

    if (!formData.horarioEntrada) {
      newErrors.horarioEntrada = 'Horário de entrada é obrigatório';
    }

    if (!formData.horarioSaida) {
      newErrors.horarioSaida = 'Horário de saída é obrigatório';
    }

    if (formData.cargaHoraria <= 0) {
      newErrors.cargaHoraria = 'Carga horária deve ser maior que zero';
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  const handleInputChange = (field: keyof typeof formData, value: string | number) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);

    // Converter salario para number se necessário
    const dataToSend = { ...newFormData };
    if (field === 'salario' && typeof value === 'string') {
      dataToSend.salario = parseFloat(value) || 0;
    }

    onDataChange(dataToSend);
  };

  const formatCurrency = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    const amount = parseFloat(numbers) / 100;
    return amount.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
  };

  useEffect(() => {
    validateForm();
  }, [formData]);

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-green-100 rounded-full">
            <Briefcase className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Dados Profissionais</h2>
        <p className="text-gray-600">Informe os dados profissionais do funcionário</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Matrícula */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Matrícula *
          </label>
          <Input
            placeholder="Digite a matrícula"
            value={formData.matricula}
            onChange={(e) => handleInputChange('matricula', e.target.value)}
            error={errors.matricula}
          />
        </div>

        {/* Data de Admissão */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Data de Admissão *
          </label>
          <input
            type="date"
            value={formData.dataAdmissao}
            onChange={(e) => handleInputChange('dataAdmissao', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.dataAdmissao ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.dataAdmissao && (
            <p className="mt-1 text-sm text-red-600">{errors.dataAdmissao}</p>
          )}
        </div>

        {/* Cargo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cargo *
          </label>
          <Input
            placeholder="Digite o cargo"
            value={formData.cargo}
            onChange={(e) => handleInputChange('cargo', e.target.value)}
            error={errors.cargo}
          />
        </div>

        {/* Setor */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Setor *
          </label>
          <select
            value={formData.setor}
            onChange={(e) => handleInputChange('setor', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.setor ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Selecione o setor</option>
            <option value="administracao">Administração</option>
            <option value="producao">Produção</option>
            <option value="vendas">Vendas</option>
            <option value="rh">Recursos Humanos</option>
            <option value="ti">Tecnologia</option>
            <option value="financeiro">Financeiro</option>
            <option value="marketing">Marketing</option>
            <option value="operacoes">Operações</option>
          </select>
          {errors.setor && (
            <p className="mt-1 text-sm text-red-600">{errors.setor}</p>
          )}
        </div>

        {/* Salário */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Salário
          </label>
          <Input
            placeholder="R$ 0,00"
            value={formData.salario.toString()}
            onChange={(e) => handleInputChange('salario', e.target.value)}
          />
        </div>

        {/* Carga Horária */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Carga Horária Semanal *
          </label>
          <input
            type="number"
            placeholder="40"
            value={formData.cargaHoraria.toString()}
            onChange={(e) => handleInputChange('cargaHoraria', parseInt(e.target.value) || 0)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.cargaHoraria ? 'border-red-500' : 'border-gray-300'
            }`}
            min="1"
            max="60"
          />
          {errors.cargaHoraria && (
            <p className="mt-1 text-sm text-red-600">{errors.cargaHoraria}</p>
          )}
        </div>

        {/* Horário de Entrada */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Horário de Entrada *
          </label>
          <input
            type="time"
            value={formData.horarioEntrada}
            onChange={(e) => handleInputChange('horarioEntrada', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.horarioEntrada ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.horarioEntrada && (
            <p className="mt-1 text-sm text-red-600">{errors.horarioEntrada}</p>
          )}
        </div>

        {/* Horário de Saída */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Horário de Saída *
          </label>
          <input
            type="time"
            value={formData.horarioSaida}
            onChange={(e) => handleInputChange('horarioSaida', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.horarioSaida ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.horarioSaida && (
            <p className="mt-1 text-sm text-red-600">{errors.horarioSaida}</p>
          )}
        </div>

        {/* Intervalo Início */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Início do Intervalo
          </label>
          <input
            type="time"
            value={formData.intervaloInicio}
            onChange={(e) => handleInputChange('intervaloInicio', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Intervalo Fim */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fim do Intervalo
          </label>
          <input
            type="time"
            value={formData.intervaloFim}
            onChange={(e) => handleInputChange('intervaloFim', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Observações */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Observações
          </label>
          <textarea
            placeholder="Observações adicionais sobre o funcionário..."
            value={formData.observacoes}
            onChange={(e) => handleInputChange('observacoes', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Resumo dos Horários */}
      {formData.horarioEntrada && formData.horarioSaida && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Resumo dos Horários</h3>
          <div className="text-sm text-blue-800">
            <p>
              <strong>Expediente:</strong> {formData.horarioEntrada} às {formData.horarioSaida}
            </p>
            {formData.intervaloInicio && formData.intervaloFim && (
              <p>
                <strong>Intervalo:</strong> {formData.intervaloInicio} às {formData.intervaloFim}
              </p>
            )}
            <p>
              <strong>Carga Horária:</strong> {formData.cargaHoraria}h semanais
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
