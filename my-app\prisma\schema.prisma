// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Modelo de Usuário para Autenticação
model Usuario {
  id          Int      @id @default(autoincrement())
  usuario     String   @unique @db.VarChar(50)
  nome        String   @db.VarChar(100)
  email       String?  @unique @db.VarChar(100)
  senhaHash   String   @db.VarChar(255)
  nivelAcesso String   @default("usuario") @db.VarChar(20) // admin, hr, manager, user, readonly
  ativo       Boolean  @default(true)
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  funcionario Funcionario?

  @@map("usuarios")
}

// Modelo de Funcionário
model Funcionario {
  id              Int      @id @default(autoincrement())
  nome            String   @db.VarChar(100)
  cpf             String   @unique @db.VarChar(14)
  rg              String?  @db.VarChar(20)
  email           String?  @db.VarChar(100)
  telefone        String?  @db.VarChar(20)
  endereco        String?  @db.Text
  cargo           String   @db.VarChar(100)
  departamento    String   @db.VarChar(100)
  salario         Decimal? @db.Decimal(10, 2)
  dataAdmissao    DateTime
  dataDemissao    DateTime?
  ativo           Boolean  @default(true)
  criadoEm        DateTime @default(now())
  atualizadoEm    DateTime @updatedAt

  // Relacionamentos
  usuarioId       Int?     @unique
  usuario         Usuario? @relation(fields: [usuarioId], references: [id])
  registrosPonto  RegistroPonto[]

  @@map("funcionarios")
}

// Modelo de Registro de Ponto
model RegistroPonto {
  id              Int      @id @default(autoincrement())
  funcionarioId   Int
  dataHora        DateTime
  tipo            String   @db.VarChar(20) // entrada, saida, intervalo_inicio, intervalo_fim
  origem          String   @db.VarChar(20) // biometrico, manual, sistema
  localizacao     String?  @db.VarChar(255)
  observacoes     String?  @db.Text
  aprovado        Boolean  @default(false)
  aprovadoPor     Int?
  criadoEm        DateTime @default(now())

  // Relacionamentos
  funcionario     Funcionario @relation(fields: [funcionarioId], references: [id])

  @@map("registros_ponto")
}
