# 👥 MÓDULO FUNCIONÁRIOS DESLIGADOS - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável pela gestão de funcionários desligados, incluindo histórico de trabalho, motivos de desligamento, documentação e relatórios de turnover.

## 🎯 Funcionalidades
- Lista de funcionários desligados
- Histórico completo de trabalho
- Motivos e tipos de desligamento
- Documentação de desligamento
- Relatórios de turnover
- Análise de retenção
- Reativação de funcionários
- Exportação de dados
- Auditoria de desligamentos

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── funcionarios-desligados/
│           ├── page.tsx                    # Lista de desligados
│           ├── [id]/
│           │   ├── page.tsx                # Detalhes do desligado
│           │   └── historico/
│           │       └── page.tsx            # Histórico completo
│           ├── desligar/
│           │   └── [funcionarioId]/
│           │       └── page.tsx            # Processo de desligamento
│           ├── relatorios/
│           │   └── page.tsx                # Relatórios de turnover
│           └── components/
│               ├── termination-form.tsx    # Formulário de desligamento
│               ├── employee-history.tsx    # Histórico do funcionário
│               ├── turnover-chart.tsx      # Gráfico de turnover
│               └── reactivation-modal.tsx  # Modal de reativação
├── components/
│   └── funcionarios-desligados/
│       ├── terminated-employee-card.tsx   # Card de funcionário desligado
│       ├── termination-reason-badge.tsx   # Badge de motivo
│       ├── timeline-component.tsx         # Timeline de eventos
│       └── retention-metrics.tsx          # Métricas de retenção
└── api/
    └── funcionarios-desligados/
        ├── route.ts                       # API principal
        ├── [id]/
        │   ├── route.ts                   # Detalhes específicos
        │   └── reativar/
        │       └── route.ts               # Reativação
        ├── desligar/
        │   └── route.ts                   # Processo de desligamento
        └── relatorios/
            └── route.ts                   # Relatórios
```

## 🔧 Implementação Técnica

### 👥 Lista de Funcionários Desligados (page.tsx)
```typescript
// app/(dashboard)/funcionarios-desligados/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { UserX, Search, Filter, Download, TrendingDown, Calendar, Users, BarChart3 } from 'lucide-react';
import Link from 'next/link';
import { TerminatedEmployeeTable } from './components/terminated-employee-table';

export const metadata: Metadata = {
  title: 'Funcionários Desligados - RLPONTO',
  description: 'Gestão de funcionários desligados e análise de turnover',
};

interface FuncionariosDesligadosPageProps {
  searchParams: {
    search?: string;
    motivo?: string;
    periodo?: string;
    departamento?: string;
    page?: string;
  };
}

export default function FuncionariosDesligadosPage({ searchParams }: FuncionariosDesligadosPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <UserX className="h-8 w-8 text-red-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Funcionários Desligados</h1>
            <p className="text-gray-600">Gestão de funcionários desligados e análise de turnover</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href="/funcionarios-desligados/relatorios">
              <BarChart3 className="h-4 w-4 mr-2" />
              Relatórios
            </Link>
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Métricas de Turnover */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Desligados</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">47</div>
            <p className="text-xs text-muted-foreground">este ano</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Turnover</CardTitle>
            <TrendingDown className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">12.3%</div>
            <p className="text-xs text-muted-foreground">nos últimos 12 meses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tempo Médio</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">2.3</div>
            <p className="text-xs text-muted-foreground">anos na empresa</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reativações</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">3</div>
            <p className="text-xs text-muted-foreground">este ano</p>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Funcionário</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por nome..."
                  className="pl-10"
                  defaultValue={searchParams.search}
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Motivo</label>
              <Select defaultValue={searchParams.motivo}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os motivos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="demissao">Demissão</SelectItem>
                  <SelectItem value="pedido">Pedido de Demissão</SelectItem>
                  <SelectItem value="justa_causa">Justa Causa</SelectItem>
                  <SelectItem value="aposentadoria">Aposentadoria</SelectItem>
                  <SelectItem value="fim_contrato">Fim de Contrato</SelectItem>
                  <SelectItem value="outros">Outros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Período</label>
              <Select defaultValue={searchParams.periodo}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os períodos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ultimo_mes">Último mês</SelectItem>
                  <SelectItem value="ultimos_3_meses">Últimos 3 meses</SelectItem>
                  <SelectItem value="ultimos_6_meses">Últimos 6 meses</SelectItem>
                  <SelectItem value="ultimo_ano">Último ano</SelectItem>
                  <SelectItem value="personalizado">Personalizado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Departamento</label>
              <Select defaultValue={searchParams.departamento}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ti">Tecnologia da Informação</SelectItem>
                  <SelectItem value="rh">Recursos Humanos</SelectItem>
                  <SelectItem value="financeiro">Financeiro</SelectItem>
                  <SelectItem value="vendas">Vendas</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button className="w-full">
                <Filter className="h-4 w-4 mr-2" />
                Filtrar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Funcionários Desligados */}
      <Card>
        <CardContent className="p-0">
          <Suspense fallback={<TerminatedEmployeeTableSkeleton />}>
            <TerminatedEmployeeTable searchParams={searchParams} />
          </Suspense>
        </CardContent>
      </Card>

      {/* Análise Rápida */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Motivos de Desligamento</CardTitle>
            <CardDescription>Distribuição dos motivos nos últimos 12 meses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Pedido de Demissão</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                  </div>
                  <span className="text-sm font-medium">45%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Demissão</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-red-600 h-2 rounded-full" style={{ width: '30%' }}></div>
                  </div>
                  <span className="text-sm font-medium">30%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Fim de Contrato</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '15%' }}></div>
                  </div>
                  <span className="text-sm font-medium">15%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Outros</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-gray-600 h-2 rounded-full" style={{ width: '10%' }}></div>
                  </div>
                  <span className="text-sm font-medium">10%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Departamentos com Maior Turnover</CardTitle>
            <CardDescription>Taxa de turnover por departamento</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Vendas</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="destructive">18.5%</Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Marketing</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">15.2%</Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Tecnologia</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">12.1%</Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Recursos Humanos</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="default" className="bg-green-100 text-green-800">8.3%</Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Financeiro</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="default" className="bg-green-100 text-green-800">6.7%</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function TerminatedEmployeeTableSkeleton() {
  return (
    <div className="p-6 space-y-4">
      {[...Array(8)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}
```

### 📋 Formulário de Desligamento (termination-form.tsx)
```typescript
// app/(dashboard)/funcionarios-desligados/components/termination-form.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2, UserX, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';

const terminationSchema = z.object({
  funcionarioId: z.number(),
  dataDesligamento: z.date(),
  motivo: z.enum(['demissao', 'pedido', 'justa_causa', 'aposentadoria', 'fim_contrato', 'outros']),
  observacoes: z.string().optional(),
  documentosEntregues: z.array(z.string()),
  pendenciasFinanceiras: z.string().optional(),
  feedbackSaida: z.string().optional(),
  recomendacaoRecontratacao: z.boolean(),
});

type TerminationFormData = z.infer<typeof terminationSchema>;

interface TerminationFormProps {
  funcionarioId: number;
  funcionarioNome: string;
  onSuccess: () => void;
}

export function TerminationForm({ funcionarioId, funcionarioNome, onSuccess }: TerminationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<TerminationFormData>({
    resolver: zodResolver(terminationSchema),
    defaultValues: {
      funcionarioId,
      documentosEntregues: [],
      recomendacaoRecontratacao: false,
    },
  });

  const motivo = watch('motivo');
  const documentosEntregues = watch('documentosEntregues');

  const motivosDesligamento = [
    { value: 'demissao', label: 'Demissão sem Justa Causa' },
    { value: 'pedido', label: 'Pedido de Demissão' },
    { value: 'justa_causa', label: 'Demissão por Justa Causa' },
    { value: 'aposentadoria', label: 'Aposentadoria' },
    { value: 'fim_contrato', label: 'Fim de Contrato' },
    { value: 'outros', label: 'Outros' },
  ];

  const documentosObrigatorios = [
    'Termo de Rescisão',
    'TRCT (Termo de Rescisão do Contrato de Trabalho)',
    'Guias do Seguro Desemprego',
    'Chave de Conectividade Social',
    'Exame Demissional',
    'Carteira de Trabalho',
    'Equipamentos da Empresa',
    'Crachá/Cartão de Acesso',
  ];

  const handleDocumentToggle = (documento: string) => {
    const current = documentosEntregues || [];
    const updated = current.includes(documento)
      ? current.filter(d => d !== documento)
      : [...current, documento];
    setValue('documentosEntregues', updated);
  };

  const onSubmit = async (data: TerminationFormData) => {
    setIsSubmitting(true);
    setMessage(null);

    try {
      const response = await fetch('/api/funcionarios-desligados/desligar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          dataDesligamento: selectedDate,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao processar desligamento');
      }

      setMessage({ 
        type: 'success', 
        text: 'Funcionário desligado com sucesso!' 
      });

      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (error) {
      console.error('Erro ao desligar funcionário:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Erro ao processar desligamento' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Message Alert */}
      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Informações do Funcionário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <UserX className="h-5 w-5 text-red-600" />
            <span>Desligamento de Funcionário</span>
          </CardTitle>
          <CardDescription>
            Processando desligamento de: <strong>{funcionarioNome}</strong>
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Dados do Desligamento */}
      <Card>
        <CardHeader>
          <CardTitle>Dados do Desligamento</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="dataDesligamento">Data do Desligamento</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !selectedDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? (
                      format(selectedDate, 'dd/MM/yyyy', { locale: ptBR })
                    ) : (
                      'Selecione a data'
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => {
                      setSelectedDate(date);
                      setValue('dataDesligamento', date!);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.dataDesligamento && (
                <p className="text-sm text-red-600 mt-1">{errors.dataDesligamento.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="motivo">Motivo do Desligamento</Label>
              <Select onValueChange={(value) => setValue('motivo', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o motivo" />
                </SelectTrigger>
                <SelectContent>
                  {motivosDesligamento.map((motivo) => (
                    <SelectItem key={motivo.value} value={motivo.value}>
                      {motivo.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.motivo && (
                <p className="text-sm text-red-600 mt-1">{errors.motivo.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="observacoes">Observações</Label>
            <Textarea
              id="observacoes"
              {...register('observacoes')}
              placeholder="Observações sobre o desligamento..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Documentos Entregues */}
      <Card>
        <CardHeader>
          <CardTitle>Documentos Entregues</CardTitle>
          <CardDescription>
            Marque os documentos que foram entregues ao funcionário
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {documentosObrigatorios.map((documento) => (
              <div key={documento} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={documento}
                  checked={documentosEntregues?.includes(documento) || false}
                  onChange={() => handleDocumentToggle(documento)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor={documento} className="text-sm">
                  {documento}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Informações Adicionais */}
      <Card>
        <CardHeader>
          <CardTitle>Informações Adicionais</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="pendenciasFinanceiras">Pendências Financeiras</Label>
            <Textarea
              id="pendenciasFinanceiras"
              {...register('pendenciasFinanceiras')}
              placeholder="Descreva pendências financeiras, se houver..."
              rows={2}
            />
          </div>

          <div>
            <Label htmlFor="feedbackSaida">Feedback de Saída</Label>
            <Textarea
              id="feedbackSaida"
              {...register('feedbackSaida')}
              placeholder="Feedback do funcionário sobre a empresa..."
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="recomendacaoRecontratacao"
              {...register('recomendacaoRecontratacao')}
              className="rounded border-gray-300"
            />
            <Label htmlFor="recomendacaoRecontratacao">
              Recomendo para recontratação futura
            </Label>
          </div>
        </CardContent>
      </Card>

      {/* Aviso */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Atenção:</strong> Esta ação não pode ser desfeita. O funcionário será movido para a lista de desligados
          e não poderá mais acessar o sistema. Certifique-se de que todos os dados estão corretos.
        </AlertDescription>
      </Alert>

      {/* Botões */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={() => window.history.back()}>
          Cancelar
        </Button>
        <Button 
          type="submit" 
          disabled={isSubmitting}
          className="bg-red-600 hover:bg-red-700"
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <UserX className="mr-2 h-4 w-4" />
          Confirmar Desligamento
        </Button>
      </div>
    </form>
  );
}
```

## 🔌 API Routes

### 👥 API de Desligamento (desligar/route.ts)
```typescript
// app/api/funcionarios-desligados/desligar/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { terminationSchema } from '@/lib/validations/funcionarios';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !['admin', 'rh'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = terminationSchema.parse(body);

    // Verificar se funcionário existe e está ativo
    const funcionario = await prisma.funcionario.findFirst({
      where: {
        id: validatedData.funcionarioId,
        ativo: true,
      },
    });

    if (!funcionario) {
      return NextResponse.json(
        { error: 'Funcionário não encontrado ou já desligado' },
        { status: 404 }
      );
    }

    // Iniciar transação
    const result = await prisma.$transaction(async (tx) => {
      // Criar registro de desligamento
      const desligamento = await tx.funcionarioDesligado.create({
        data: {
          funcionarioId: validatedData.funcionarioId,
          dataDesligamento: validatedData.dataDesligamento,
          motivo: validatedData.motivo,
          observacoes: validatedData.observacoes,
          documentosEntregues: JSON.stringify(validatedData.documentosEntregues),
          pendenciasFinanceiras: validatedData.pendenciasFinanceiras,
          feedbackSaida: validatedData.feedbackSaida,
          recomendacaoRecontratacao: validatedData.recomendacaoRecontratacao,
          processadoPorId: parseInt(session.user.id),
          processadoEm: new Date(),
        },
      });

      // Desativar funcionário
      await tx.funcionario.update({
        where: { id: validatedData.funcionarioId },
        data: {
          ativo: false,
          dataDesligamento: validatedData.dataDesligamento,
          atualizadoEm: new Date(),
        },
      });

      // Bloquear acesso ao sistema (se tiver usuário)
      await tx.usuario.updateMany({
        where: { 
          funcionarioId: validatedData.funcionarioId,
          ativo: true,
        },
        data: {
          ativo: false,
          bloqueado: true,
          atualizadoEm: new Date(),
        },
      });

      // Finalizar registros de ponto em aberto
      await tx.registroPonto.updateMany({
        where: {
          funcionarioId: validatedData.funcionarioId,
          saida: null,
        },
        data: {
          observacoes: 'Registro finalizado automaticamente - funcionário desligado',
          atualizadoEm: new Date(),
        },
      });

      return desligamento;
    });

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        acao: 'funcionario_desligado',
        usuarioId: parseInt(session.user.id),
        detalhes: JSON.stringify({
          funcionarioId: validatedData.funcionarioId,
          funcionarioNome: funcionario.nome,
          motivo: validatedData.motivo,
          dataDesligamento: validatedData.dataDesligamento,
        }),
        timestamp: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      desligamento: result,
    });

  } catch (error) {
    console.error('Erro ao processar desligamento:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Schema do Banco de Dados

### 👥 Modelo Prisma
```prisma
model FuncionarioDesligado {
  id                        Int       @id @default(autoincrement())
  funcionarioId             Int       @map("funcionario_id")
  dataDesligamento          DateTime  @map("data_desligamento")
  motivo                    String    // 'demissao', 'pedido', 'justa_causa', etc.
  observacoes               String?
  documentosEntregues       String    // JSON array
  pendenciasFinanceiras     String?   @map("pendencias_financeiras")
  feedbackSaida             String?   @map("feedback_saida")
  recomendacaoRecontratacao Boolean   @default(false) @map("recomendacao_recontratacao")
  processadoPorId           Int       @map("processado_por_id")
  processadoEm              DateTime  @map("processado_em")
  reativadoEm               DateTime? @map("reativado_em")
  reativadoPorId            Int?      @map("reativado_por_id")

  // Relacionamentos
  funcionario               Funcionario @relation(fields: [funcionarioId], references: [id])
  processadoPor             Usuario     @relation("DesligamentoProcessador", fields: [processadoPorId], references: [id])
  reativadoPor              Usuario?    @relation("DesligamentoReativador", fields: [reativadoPorId], references: [id])

  @@map("funcionarios_desligados")
}

// Adicionar ao modelo Funcionario
model Funcionario {
  // ... campos existentes
  dataDesligamento          DateTime? @map("data_desligamento")
  desligamentos             FuncionarioDesligado[]
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Lista de funcionários desligados
- [ ] Processo de desligamento
- [ ] Histórico completo
- [ ] Motivos e documentação
- [ ] Relatórios de turnover
- [ ] Análise de retenção
- [ ] Reativação de funcionários
- [ ] Exportação de dados

### 🔧 Validações
- [ ] Verificação de funcionário ativo
- [ ] Validação de documentos
- [ ] Controle de permissões
- [ ] Logs de auditoria
- [ ] Finalização de registros

## 🚀 Próximos Passos
1. **Integração com folha de pagamento**
2. **Relatórios avançados de turnover**
3. **Dashboard executivo de retenção**
