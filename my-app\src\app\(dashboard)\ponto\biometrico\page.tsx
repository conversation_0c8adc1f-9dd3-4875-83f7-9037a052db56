import { Metadata } from 'next';
import { Suspense } from 'react';
import { BiometricScanner } from '@/components/ponto/biometric-scanner';
import { PontoStatus } from '@/components/ponto/ponto-status';
import { HistoricoRecente } from '@/components/ponto/historico-recente';
import { Fingerprint, Camera, Clock, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Ponto Biométrico - RLPONTO',
  description: 'Registro de ponto através de biometria',
};

export default function PontoBiometricoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <Fingerprint className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Ponto Biométrico</h1>
                  <p className="text-gray-600">Registre seu ponto usando biometria digital ou facial</p>
                </div>
              </div>
            </div>
          </div>

          {/* Status do Ponto */}
          <Suspense fallback={<PontoStatusSkeleton />}>
            <PontoStatus />
          </Suspense>

          {/* Scanner Biométrico */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Biometria Digital */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center space-x-2 mb-6">
                <Fingerprint className="h-6 w-6 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">Biometria Digital</h2>
              </div>
              <BiometricScanner type="fingerprint" />
            </div>

            {/* Reconhecimento Facial */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center space-x-2 mb-6">
                <Camera className="h-6 w-6 text-green-600" />
                <h2 className="text-xl font-semibold text-gray-900">Reconhecimento Facial</h2>
              </div>
              <BiometricScanner type="facial" />
            </div>
          </div>

          {/* Histórico Recente */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-2 mb-6">
              <Clock className="h-6 w-6 text-gray-600" />
              <h2 className="text-xl font-semibold text-gray-900">Registros Recentes</h2>
            </div>
            <Suspense fallback={<HistoricoSkeleton />}>
              <HistoricoRecente />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}

function PontoStatusSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="h-32 bg-gray-200 rounded-lg animate-pulse" />
    </div>
  );
}

function HistoricoSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
          <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3" />
          </div>
          <div className="w-20 h-8 bg-gray-200 rounded animate-pulse" />
        </div>
      ))}
    </div>
  );
}
