import { Metadata } from 'next';
import { Calendar, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Relatório por Período - RLPONTO',
  description: 'Gerar relatório consolidado por período',
};

export default function RelatorioPeriodoPage() {
  const reportConfig = {
    type: 'periodo',
    title: 'Relatório por Período',
    description: 'Gere um relatório consolidado de todos os funcionários em um período específico',
    color: 'green',
    fields: [
      {
        name: 'periodo',
        label: 'Período',
        type: 'daterange' as const,
        required: true,
        defaultValue: {
          start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          end: new Date()
        }
      },
      {
        name: 'departamento',
        label: 'Departamento',
        type: 'select' as const,
        required: false,
        placeholder: 'Todos os departamentos',
        options: [
          { value: 'todos', label: 'Todos os Departamentos' },
          { value: 'ti', label: 'Tecnologia da Informação' },
          { value: 'rh', label: 'Recursos Humanos' },
          { value: 'vendas', label: 'Vendas' },
          { value: 'producao', label: 'Produção' },
          { value: 'administracao', label: 'Administração' },
          { value: 'financeiro', label: 'Financeiro' }
        ]
      },
      {
        name: 'funcionarios',
        label: 'Funcionários',
        type: 'select' as const,
        required: false,
        placeholder: 'Todos os funcionários',
        options: [
          { value: 'todos', label: 'Todos os Funcionários' },
          { value: 'ativos', label: 'Apenas Ativos' },
          { value: 'inativos', label: 'Apenas Inativos' },
          { value: 'especificos', label: 'Funcionários Específicos' }
        ]
      },
      {
        name: 'incluirTotalizadores',
        label: 'Incluir Totalizadores',
        type: 'checkbox' as const,
        defaultValue: true,
        description: 'Adicionar resumos e totais por departamento'
      },
      {
        name: 'incluirGraficos',
        label: 'Incluir Gráficos',
        type: 'checkbox' as const,
        defaultValue: true,
        description: 'Adicionar gráficos comparativos e análises visuais'
      },
      {
        name: 'incluirDetalhamento',
        label: 'Detalhamento Individual',
        type: 'checkbox' as const,
        defaultValue: false,
        description: 'Incluir dados detalhados de cada funcionário'
      },
      {
        name: 'incluirHorasExtras',
        label: 'Análise de Horas Extras',
        type: 'checkbox' as const,
        defaultValue: true,
        description: 'Incluir seção específica de horas extras'
      },
      {
        name: 'incluirAbsenteismo',
        label: 'Análise de Absenteísmo',
        type: 'checkbox' as const,
        defaultValue: true,
        description: 'Incluir métricas de faltas e ausências'
      },
      {
        name: 'formato',
        label: 'Formato de Saída',
        type: 'radio' as const,
        required: true,
        defaultValue: 'PDF',
        options: [
          { value: 'PDF', label: 'PDF', description: 'Relatório executivo para apresentação' },
          { value: 'Excel', label: 'Excel', description: 'Planilha detalhada para análise' },
          { value: 'CSV', label: 'CSV', description: 'Dados brutos para importação' }
        ]
      }
    ],
    sections: [
      {
        title: 'Resumo Executivo',
        description: 'Visão geral do período com principais métricas',
        included: true
      },
      {
        title: 'Dados Consolidados',
        description: 'Totalizadores por departamento e função',
        included: true
      },
      {
        title: 'Análise de Frequência',
        description: 'Estatísticas de presença e pontualidade',
        included: true
      },
      {
        title: 'Horas Trabalhadas',
        description: 'Distribuição de horas normais e extras',
        included: true
      },
      {
        title: 'Indicadores de Performance',
        description: 'KPIs e métricas de produtividade',
        included: true,
        optional: true
      },
      {
        title: 'Comparativo Histórico',
        description: 'Comparação com períodos anteriores',
        included: false,
        optional: true
      },
      {
        title: 'Detalhamento por Funcionário',
        description: 'Dados individuais de cada colaborador',
        included: false,
        optional: true
      },
      {
        title: 'Gráficos e Análises',
        description: 'Visualizações e insights dos dados',
        included: true,
        optional: true
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/relatorios">
                <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <ArrowLeft className="h-4 w-4 mr-2 inline" />
                  Voltar
                </button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-600 rounded-lg">
                  <Calendar className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Relatório por Período</h1>
                  <p className="text-gray-600">Configure e gere relatório consolidado por período</p>
                </div>
              </div>
            </div>
          </div>

          {/* Informações do Relatório */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-green-900 mb-2">Sobre este Relatório</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
              <div>
                <strong>Ideal para:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Análises mensais e trimestrais</li>
                  <li>Relatórios para gestão</li>
                  <li>Comparativos entre departamentos</li>
                </ul>
              </div>
              <div>
                <strong>Inclui:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Dados consolidados de todos os funcionários</li>
                  <li>Métricas de frequência e pontualidade</li>
                  <li>Análises de horas extras e absenteísmo</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Formulário */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Configurações do Relatório</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Período
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <input
                    type="date"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    defaultValue={new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]}
                  />
                  <input
                    type="date"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    defaultValue={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Departamento
                </label>
                <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                  <option value="todos">Todos os Departamentos</option>
                  <option value="ti">Tecnologia da Informação</option>
                  <option value="rh">Recursos Humanos</option>
                  <option value="vendas">Vendas</option>
                  <option value="producao">Produção</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Formato de Saída
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="radio" name="formato" value="PDF" defaultChecked className="mr-2" />
                    PDF - Relatório executivo para apresentação
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="formato" value="Excel" className="mr-2" />
                    Excel - Planilha detalhada para análise
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="formato" value="CSV" className="mr-2" />
                    CSV - Dados brutos para importação
                  </label>
                </div>
              </div>

              <div className="pt-4">
                <button className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                  Gerar Relatório
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


