(()=>{var a={};a.id=772,a.ids=[772],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12798:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>p,metadata:()=>o});var d=c(37413),e=c(61120),f=c(34672),g=c(51465);let h=(0,c(26373).A)("hand",[["path",{d:"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2",key:"1fvzgz"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2",key:"1kc0my"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8",key:"10h0bg"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]]);var i=c(26919),j=c(65276),k=c(53148),l=c(75243),m=c(4536),n=c.n(m);let o={title:"Ponto Manual - RLPONTO",description:"Registro manual de ponto"};function p(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(n(),{href:"/ponto/biometrico",children:(0,d.jsxs)(l.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-orange-600 rounded-lg",children:(0,d.jsx)(h,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Ponto Manual"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Registre o ponto manualmente quando necess\xe1rio"})]})]})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(i.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Aten\xe7\xe3o"}),(0,d.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"O registro manual requer justificativa e pode necessitar aprova\xe7\xe3o do supervisor."})]})]})}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(j.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"Quando usar"}),(0,d.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Use apenas quando a biometria n\xe3o estiver dispon\xedvel ou em situa\xe7\xf5es excepcionais."})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(k.A,{className:"h-5 w-5 text-orange-600"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Registro Manual"})]})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(q,{}),children:(0,d.jsx)(f.ManualForm,{})})})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informa\xe7\xf5es Importantes"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Documenta\xe7\xe3o Necess\xe1ria"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,d.jsx)("li",{children:"• Justificativa detalhada do motivo"}),(0,d.jsx)("li",{children:"• Foto comprobat\xf3ria (opcional)"}),(0,d.jsx)("li",{children:"• Localiza\xe7\xe3o atual (se dispon\xedvel)"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Processo de Aprova\xe7\xe3o"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,d.jsx)("li",{children:"• Registro enviado para an\xe1lise"}),(0,d.jsx)("li",{children:"• Supervisor recebe notifica\xe7\xe3o"}),(0,d.jsx)("li",{children:"• Aprova\xe7\xe3o em at\xe9 24 horas"})]})]})]})]})]})})})}function q(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]},b))}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-24 bg-gray-200 rounded"})]}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32"})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26919:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34672:(a,b,c)=>{"use strict";c.d(b,{ManualForm:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ManualForm() from the server but ManualForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ponto\\manual-form.tsx","ManualForm")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},59099:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,71718)),Promise.resolve().then(c.bind(c,98316))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71718:(a,b,c)=>{"use strict";c.d(b,{ManualForm:()=>n});var d=c(60687),e=c(43210),f=c(42613),g=c(5336),h=c(93613),i=c(58869),j=c(10022),k=c(97992),l=c(41862),m=c(48730);function n(){let[a,b]=(0,e.useState)([]),[c,n]=(0,e.useState)({funcionarioId:"",tipo:"entrada",horario:"",data:"",justificativa:""}),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(""),[u,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(!1),y=async()=>{if(!navigator.geolocation)return void t("Geolocaliza\xe7\xe3o n\xe3o suportada pelo navegador");v(!0),navigator.geolocation.getCurrentPosition(async a=>{try{let{latitude:b,longitude:c}=a.coords,d=`Lat: ${b.toFixed(6)}, Lng: ${c.toFixed(6)}`;n(a=>({...a,localizacao:{latitude:b,longitude:c,endereco:d}}))}catch(a){console.error("Erro ao obter endere\xe7o:",a)}finally{v(!1)}},a=>{t("Erro ao obter localiza\xe7\xe3o: "+a.message),v(!1)},{enableHighAccuracy:!0,timeout:1e4})},z=async a=>{if(a.preventDefault(),!c.funcionarioId||!c.justificativa.trim())return void t("Funcion\xe1rio e justificativa s\xe3o obrigat\xf3rios");p(!0),t("");try{let a=await fetch("/api/ponto/manual",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...c,timestamp:new Date(`${c.data}T${c.horario}`).toISOString()})}),b=await a.json();a.ok?(r(!0),n({funcionarioId:"",tipo:"entrada",horario:new Date().toTimeString().slice(0,5),data:new Date().toISOString().split("T")[0],justificativa:""})):t(b.error||"Erro ao registrar ponto")}catch(a){t("Erro de conex\xe3o. Tente novamente.")}finally{p(!1)}},A=a.find(a=>a.id===c.funcionarioId);return q?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(g.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Registro Enviado com Sucesso!"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"O registro manual foi enviado para aprova\xe7\xe3o do supervisor."}),(0,d.jsx)(f.$n,{onClick:()=>r(!1),variant:"primary",children:"Fazer Novo Registro"})]}):(0,d.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[s&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(h.A,{className:"h-5 w-5 text-red-600"}),(0,d.jsx)("span",{className:"text-sm text-red-700",children:s})]})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 inline mr-2"}),"Funcion\xe1rio *"]}),(0,d.jsxs)("select",{value:c.funcionarioId,onChange:a=>n(b=>({...b,funcionarioId:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",style:{color:c.funcionarioId?"#111827":"#6B7280"},required:!0,children:[(0,d.jsx)("option",{value:"",style:{color:"#6B7280"},children:"Selecione um funcion\xe1rio"}),a.map(a=>(0,d.jsxs)("option",{value:a.id,children:[a.nome," - ",a.matricula]},a.id))]}),A&&(0,d.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-2 rounded",children:[(0,d.jsx)("strong",{children:"Cargo:"})," ",A.cargo]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Data *"}),(0,d.jsx)("input",{type:"date",value:c.data,onChange:a=>n(b=>({...b,data:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",style:{colorScheme:"light"},required:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Hor\xe1rio *"}),(0,d.jsx)("input",{type:"time",value:c.horario,onChange:a=>n(b=>({...b,horario:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",style:{colorScheme:"light"},required:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Tipo de Registro *"}),(0,d.jsxs)("select",{value:c.tipo,onChange:a=>n(b=>({...b,tipo:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",required:!0,children:[(0,d.jsx)("option",{value:"entrada",children:"Entrada"}),(0,d.jsx)("option",{value:"saida",children:"Sa\xedda"}),(0,d.jsx)("option",{value:"intervalo_inicio",children:"In\xedcio Intervalo"}),(0,d.jsx)("option",{value:"intervalo_fim",children:"Fim Intervalo"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 inline mr-2"}),"Justificativa *"]}),(0,d.jsx)("textarea",{value:c.justificativa,onChange:a=>n(b=>({...b,justificativa:a.target.value})),placeholder:"Descreva o motivo do registro manual (ex: problema na biometria, trabalho externo, etc.)",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 placeholder:text-gray-600",required:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 inline mr-2"}),"Localiza\xe7\xe3o (Opcional)"]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(f.$n,{type:"button",onClick:y,disabled:u,variant:"outline",className:"flex-shrink-0",children:[u?(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Capturar Localiza\xe7\xe3o"]}),c.localizacao&&(0,d.jsxs)("div",{className:"flex-1 text-sm text-gray-600 bg-green-50 p-2 rounded border",children:["\uD83D\uDCCD ",c.localizacao.endereco]})]})]}),(0,d.jsx)("div",{className:"flex space-x-4",children:(0,d.jsx)(f.$n,{type:"submit",disabled:o,variant:"primary",className:"flex-1",children:o?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Enviando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Registrar Ponto Manual"]})})})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93443:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,34672)),Promise.resolve().then(c.bind(c,58570))},94312:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["ponto",{children:["manual",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,12798)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\manual\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\ponto\\manual\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/ponto/manual/page",pathname:"/ponto/manual",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/ponto/manual/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,327,556,40,121],()=>b(b.s=94312));module.exports=c})();