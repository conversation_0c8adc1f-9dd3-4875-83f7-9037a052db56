(()=>{var a={};a.id=332,a.ids=[332],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7854:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(37413),e=c(12279),f=c(51465);let g=(0,c(26373).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var h=c(75243),i=c(4536),j=c.n(i);let k={title:"Novo Funcion\xe1rio - RLPONTO",description:"Cadastro de novo funcion\xe1rio no sistema"};function l(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(j(),{href:"/funcionarios",children:(0,d.jsxs)(h.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(g,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Novo Funcion\xe1rio"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Cadastre um novo funcion\xe1rio no sistema"})]})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsx)(e.FuncionarioWizard,{})})]})})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12279:(a,b,c)=>{"use strict";c.d(b,{FuncionarioWizard:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call FuncionarioWizard() from the server but FuncionarioWizard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\funcionario-wizard.tsx","FuncionarioWizard")},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38620:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["funcionarios",{children:["novo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,7854)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\novo\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\novo\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/funcionarios/novo/page",pathname:"/funcionarios/novo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/funcionarios/novo/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63726:(a,b,c)=>{"use strict";c.d(b,{FuncionarioWizard:()=>w});var d=c(60687),e=c(43210),f=c(16189),g=c(62688);let h=(0,g.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);function i({steps:a,currentStepIndex:b,completedSteps:c}){let e=new Set(c.map(a=>a.id));return(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Progresso do Cadastro"}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:[b+1," de ",a.length]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${(b+1)/a.length*100}%`}})})]}),(0,d.jsx)("div",{className:"flex items-center justify-between",children:a.map((c,f)=>{let g=e.has(c.id)||f<b,i=f===b;return(0,d.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center w-full",children:[f>0&&(0,d.jsx)("div",{className:`flex-1 h-1 ${g?"bg-blue-600":"bg-gray-200"}`}),(0,d.jsx)("div",{className:`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                    ${g?"bg-blue-600 border-blue-600 text-white":i?"bg-white border-blue-600 text-blue-600":"bg-white border-gray-300 text-gray-500"}
                  `,children:g?(0,d.jsx)(h,{className:"h-5 w-5"}):(0,d.jsx)("span",{className:"text-sm font-medium",children:f+1})}),f<a.length-1&&(0,d.jsx)("div",{className:`flex-1 h-1 ${f<b?"bg-blue-600":"bg-gray-200"}`})]}),(0,d.jsxs)("div",{className:"mt-3 text-center max-w-[120px]",children:[(0,d.jsx)("div",{className:`text-sm font-medium ${i?"text-blue-600":g?"text-gray-900":"text-gray-500"}`,children:c.title}),(0,d.jsx)("div",{className:`text-xs mt-1 ${i?"text-blue-500":g?"text-gray-600":"text-gray-500"}`,children:c.description})]})]},c.id)})})]})}var j=c(42613),k=c(58869);function l({data:a,onDataChange:b,onValidationChange:c}){let[f,g]=(0,e.useState)({nomeCompleto:a.nomeCompleto||"",cpf:a.cpf||"",rg:a.rg||"",email:a.email||"",telefone:a.telefone||"",celular:a.celular||"",cep:a.cep||"",logradouro:a.logradouro||"",numero:a.numero||"",complemento:a.complemento||"",bairro:a.bairro||"",cidade:a.cidade||"",uf:a.uf||""}),[h,i]=(0,e.useState)({}),l=(a,c)=>{let d={...f,[a]:c};g(d),b(d)},m=a=>{let b=a.replace(/\D/g,"");return b.length<=10?b.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):b.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3")},n=async a=>{let c=a.replace(/\D/g,"");if(8===c.length)try{let a=await fetch(`https://viacep.com.br/ws/${c}/json/`),d=await a.json();d.erro||(g(a=>({...a,logradouro:d.logradouro||"",bairro:d.bairro||"",cidade:d.localidade||"",uf:d.uf||""})),b({...f,logradouro:d.logradouro||"",bairro:d.bairro||"",cidade:d.localidade||"",uf:d.uf||""}))}catch(a){console.error("Erro ao buscar CEP:",a)}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-blue-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Pessoais"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Informe os dados pessoais do funcion\xe1rio"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nome Completo *"}),(0,d.jsx)(j.pd,{placeholder:"Digite o nome completo",value:f.nomeCompleto,onChange:a=>l("nomeCompleto",a.target.value),error:h.nomeCompleto})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CPF *"}),(0,d.jsx)(j.pd,{placeholder:"000.000.000-00",value:f.cpf.replace(/\D/g,"").replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4"),onChange:a=>l("cpf",a.target.value.replace(/\D/g,"")),error:h.cpf})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"RG"}),(0,d.jsx)(j.pd,{placeholder:"Digite o RG",value:f.rg,onChange:a=>l("rg",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,d.jsx)(j.pd,{type:"email",placeholder:"<EMAIL>",value:f.email,onChange:a=>l("email",a.target.value),error:h.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefone"}),(0,d.jsx)(j.pd,{placeholder:"(11) 1234-5678",value:m(f.telefone),onChange:a=>l("telefone",a.target.value.replace(/\D/g,""))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Celular"}),(0,d.jsx)(j.pd,{placeholder:"(11) 99999-9999",value:m(f.celular),onChange:a=>l("celular",a.target.value.replace(/\D/g,""))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CEP"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(j.pd,{placeholder:"00000-000",value:f.cep.replace(/\D/g,"").replace(/(\d{5})(\d{3})/,"$1-$2"),onChange:a=>l("cep",a.target.value.replace(/\D/g,""))}),(0,d.jsx)(j.$n,{type:"button",variant:"outline",onClick:()=>n(f.cep),disabled:8!==f.cep.replace(/\D/g,"").length,children:"Buscar"})]})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logradouro"}),(0,d.jsx)(j.pd,{placeholder:"Rua, Avenida, etc.",value:f.logradouro,onChange:a=>l("logradouro",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"N\xfamero"}),(0,d.jsx)(j.pd,{placeholder:"123",value:f.numero,onChange:a=>l("numero",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Complemento"}),(0,d.jsx)(j.pd,{placeholder:"Apto, Bloco, etc.",value:f.complemento,onChange:a=>l("complemento",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bairro"}),(0,d.jsx)(j.pd,{placeholder:"Nome do bairro",value:f.bairro,onChange:a=>l("bairro",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cidade"}),(0,d.jsx)(j.pd,{placeholder:"Nome da cidade",value:f.cidade,onChange:a=>l("cidade",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"UF"}),(0,d.jsxs)("select",{value:f.uf,onChange:a=>l("uf",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,d.jsx)("option",{value:"",children:"Selecione"}),(0,d.jsx)("option",{value:"SP",children:"S\xe3o Paulo"}),(0,d.jsx)("option",{value:"RJ",children:"Rio de Janeiro"}),(0,d.jsx)("option",{value:"MG",children:"Minas Gerais"}),(0,d.jsx)("option",{value:"RS",children:"Rio Grande do Sul"}),(0,d.jsx)("option",{value:"PR",children:"Paran\xe1"}),(0,d.jsx)("option",{value:"SC",children:"Santa Catarina"})]})]})]})]})}let m=(0,g.A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);function n({data:a,onDataChange:b,onValidationChange:c}){let[f,g]=(0,e.useState)({matricula:a.matricula||"",cargo:a.cargo||"",setor:a.setor||"",dataAdmissao:a.dataAdmissao||"",salario:a.salario||0,cargaHoraria:a.cargaHoraria||40,horarioEntrada:a.horarioEntrada||"",horarioSaida:a.horarioSaida||"",intervaloInicio:a.intervaloInicio||"",intervaloFim:a.intervaloFim||"",observacoes:a.observacoes||""}),[h,i]=(0,e.useState)({}),k=(a,c)=>{let d={...f,[a]:c};g(d);let e={...d};"salario"===a&&"string"==typeof c&&(e.salario=parseFloat(c)||0),b(e)};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,d.jsx)(m,{className:"h-8 w-8 text-green-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Profissionais"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Informe os dados profissionais do funcion\xe1rio"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Matr\xedcula *"}),(0,d.jsx)(j.pd,{placeholder:"Digite a matr\xedcula",value:f.matricula,onChange:a=>k("matricula",a.target.value),error:h.matricula})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Data de Admiss\xe3o *"}),(0,d.jsx)("input",{type:"date",value:f.dataAdmissao,onChange:a=>k("dataAdmissao",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${h.dataAdmissao?"border-red-500":"border-gray-300"}`}),h.dataAdmissao&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.dataAdmissao})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cargo *"}),(0,d.jsx)(j.pd,{placeholder:"Digite o cargo",value:f.cargo,onChange:a=>k("cargo",a.target.value),error:h.cargo})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Setor *"}),(0,d.jsxs)("select",{value:f.setor,onChange:a=>k("setor",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${h.setor?"border-red-500":"border-gray-300"}`,children:[(0,d.jsx)("option",{value:"",children:"Selecione o setor"}),(0,d.jsx)("option",{value:"administracao",children:"Administra\xe7\xe3o"}),(0,d.jsx)("option",{value:"producao",children:"Produ\xe7\xe3o"}),(0,d.jsx)("option",{value:"vendas",children:"Vendas"}),(0,d.jsx)("option",{value:"rh",children:"Recursos Humanos"}),(0,d.jsx)("option",{value:"ti",children:"Tecnologia"}),(0,d.jsx)("option",{value:"financeiro",children:"Financeiro"}),(0,d.jsx)("option",{value:"marketing",children:"Marketing"}),(0,d.jsx)("option",{value:"operacoes",children:"Opera\xe7\xf5es"})]}),h.setor&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.setor})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sal\xe1rio"}),(0,d.jsx)(j.pd,{placeholder:"R$ 0,00",value:f.salario.toString(),onChange:a=>k("salario",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Carga Hor\xe1ria Semanal *"}),(0,d.jsx)("input",{type:"number",placeholder:"40",value:f.cargaHoraria.toString(),onChange:a=>k("cargaHoraria",parseInt(a.target.value)||0),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${h.cargaHoraria?"border-red-500":"border-gray-300"}`,min:"1",max:"60"}),h.cargaHoraria&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.cargaHoraria})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Entrada *"}),(0,d.jsx)("input",{type:"time",value:f.horarioEntrada,onChange:a=>k("horarioEntrada",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${h.horarioEntrada?"border-red-500":"border-gray-300"}`}),h.horarioEntrada&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.horarioEntrada})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Sa\xedda *"}),(0,d.jsx)("input",{type:"time",value:f.horarioSaida,onChange:a=>k("horarioSaida",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${h.horarioSaida?"border-red-500":"border-gray-300"}`}),h.horarioSaida&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.horarioSaida})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"In\xedcio do Intervalo"}),(0,d.jsx)("input",{type:"time",value:f.intervaloInicio,onChange:a=>k("intervaloInicio",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fim do Intervalo"}),(0,d.jsx)("input",{type:"time",value:f.intervaloFim,onChange:a=>k("intervaloFim",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Observa\xe7\xf5es"}),(0,d.jsx)("textarea",{placeholder:"Observa\xe7\xf5es adicionais sobre o funcion\xe1rio...",value:f.observacoes,onChange:a=>k("observacoes",a.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),f.horarioEntrada&&f.horarioSaida&&(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Resumo dos Hor\xe1rios"}),(0,d.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Expediente:"})," ",f.horarioEntrada," \xe0s ",f.horarioSaida]}),f.intervaloInicio&&f.intervaloFim&&(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Intervalo:"})," ",f.intervaloInicio," \xe0s ",f.intervaloFim]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Carga Hor\xe1ria:"})," ",f.cargaHoraria,"h semanais"]})]})]})]})}var o=c(5336),p=c(97992),q=c(48730);function r({data:a,onValidationChange:b}){var c,e;let f=a=>{if(!a)return"";let b=a.replace(/\D/g,"");return b.length<=10?b.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):b.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3")};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,d.jsx)(o.A,{className:"h-8 w-8 text-green-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Confirma\xe7\xe3o dos Dados"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Revise todas as informa\xe7\xf5es antes de finalizar o cadastro"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(k.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Pessoais"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Nome Completo"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.nomeCompleto||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"CPF"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.cpf?a.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4"):"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"RG"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.rg||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.email||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Telefone"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.telefone?f(a.telefone):"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Celular"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.celular?f(a.celular):"-"})]})]}),(a.logradouro||a.cidade)&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Endere\xe7o"})]}),(0,d.jsx)("p",{className:"text-gray-900",children:[a.logradouro,a.numero,a.complemento,a.bairro,a.cidade,a.uf].filter(Boolean).join(", ")||"-"}),a.cep&&(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["CEP: ",a.cep]})]})]}),(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(m,{className:"h-5 w-5 text-green-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Profissionais"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Matr\xedcula"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.matricula||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Data de Admiss\xe3o"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.dataAdmissao?(c=a.dataAdmissao)?new Date(c).toLocaleDateString("pt-BR"):"":"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Cargo"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.cargo||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Setor"}),(0,d.jsx)("p",{className:"text-gray-900 capitalize",children:a.setor||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Sal\xe1rio"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.salario?(e=a.salario)?("string"==typeof e?parseFloat(e):e).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"":"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Carga Hor\xe1ria"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.cargaHoraria?`${a.cargaHoraria}h semanais`:"-"})]})]}),(a.horarioEntrada||a.horarioSaida)&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Hor\xe1rios de Trabalho"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Expediente"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.horarioEntrada&&a.horarioSaida?`${a.horarioEntrada} \xe0s ${a.horarioSaida}`:"-"})]}),a.intervaloInicio&&a.intervaloFim&&(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Intervalo"}),(0,d.jsx)("p",{className:"text-gray-900",children:`${a.intervaloInicio} \xe0s ${a.intervaloFim}`})]})]})]}),a.observacoes&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Observa\xe7\xf5es"}),(0,d.jsx)("p",{className:"text-gray-900 mt-1",children:a.observacoes})]})]})]}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(o.A,{className:"h-5 w-5 text-yellow-400"})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Confirme os dados antes de finalizar"}),(0,d.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,d.jsx)("p",{children:"Verifique se todas as informa\xe7\xf5es est\xe3o corretas. Ap\xf3s confirmar, o funcion\xe1rio ser\xe1 cadastrado no sistema e poder\xe1 come\xe7ar a utilizar o controle de ponto."})})]})]})})]})}var s=c(47033),t=c(14952),u=c(41862);let v=[{id:"pessoal",title:"Dados Pessoais",description:"Informa\xe7\xf5es b\xe1sicas do funcion\xe1rio"},{id:"profissional",title:"Dados Profissionais",description:"Cargo, setor e hor\xe1rios"},{id:"confirmacao",title:"Confirma\xe7\xe3o",description:"Revisar e confirmar dados"}];function w(){let a=(0,f.useRouter)(),[b,c]=(0,e.useState)(!1),[g,k]=(0,e.useState)(null),[m,o]=(0,e.useState)({}),p=function({steps:a,initialStep:b=0}){let[c,d]=(0,e.useState)(b),[f,g]=(0,e.useState)({}),[h,i]=(0,e.useState)(new Set),j=a[c],k=0===c,l=c===a.length-1,m=(0,e.useCallback)(()=>{l||d(a=>a+1)},[l]),n=(0,e.useCallback)(()=>{k||d(a=>a-1)},[k]),o=(0,e.useCallback)(b=>{b>=0&&b<a.length&&d(b)},[a.length]),p=(0,e.useCallback)((a,b)=>{g(c=>({...c,[a]:b}))},[]),q=(0,e.useCallback)(a=>{i(b=>new Set([...b,a]))},[]),r=(0,e.useCallback)(a=>f[a]??!1,[f]),s=(0,e.useCallback)(a=>h.has(a),[h]),t=r(j?.id),u=!k,v=(c+1)/a.length*100;return{currentStep:j,currentStepIndex:c,steps:a,isFirstStep:k,isLastStep:l,canGoNext:t,canGoPrevious:u,progress:v,goToNext:m,goToPrevious:n,goToStep:o,setStepValid:p,markStepCompleted:q,isStepValid:r,isStepCompleted:s}}({steps:v,initialStep:0}),q=a=>{o(b=>({...b,...a}))},w=async()=>{try{c(!0),k(null);let b=await fetch("/api/funcionarios",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)});if(!b.ok){let a=await b.json();throw Error(a.error||"Erro ao cadastrar funcion\xe1rio")}await b.json(),a.push("/funcionarios?success=funcionario-cadastrado")}catch(a){console.error("Erro ao cadastrar funcion\xe1rio:",a),k(a instanceof Error?a.message:"Erro desconhecido")}finally{c(!1)}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(i,{steps:p.steps,currentStepIndex:p.currentStepIndex,completedSteps:p.steps.slice(0,p.currentStepIndex)}),g&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"text-red-800",children:[(0,d.jsx)("strong",{children:"Erro:"})," ",g]})}),(0,d.jsx)("div",{className:"min-h-[400px]",children:(()=>{switch(p.currentStep.id){case"pessoal":return(0,d.jsx)(l,{data:m,onDataChange:q,onValidationChange:a=>p.setStepValid("pessoal",a)});case"profissional":return(0,d.jsx)(n,{data:m,onDataChange:q,onValidationChange:a=>p.setStepValid("profissional",a)});case"confirmacao":return(0,d.jsx)(r,{data:m,onValidationChange:a=>p.setStepValid("confirmacao",a)});default:return null}})()}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,d.jsx)("div",{children:!p.isFirstStep&&(0,d.jsxs)(j.$n,{variant:"outline",onClick:p.goToPrevious,disabled:b,children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-2"}),"Anterior"]})}),(0,d.jsx)("div",{className:"flex space-x-3",children:p.isLastStep?(0,d.jsx)(j.$n,{onClick:w,disabled:!p.canGoNext||b,variant:"primary",children:b?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Cadastrando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h,{className:"h-4 w-4 mr-2"}),"Cadastrar Funcion\xe1rio"]})}):(0,d.jsxs)(j.$n,{onClick:p.goToNext,disabled:!p.canGoNext||b,variant:"primary",children:["Pr\xf3ximo",(0,d.jsx)(t.A,{className:"h-4 w-4 ml-2"})]})})]})]})}},82851:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,12279)),Promise.resolve().then(c.bind(c,58570))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96923:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,63726)),Promise.resolve().then(c.bind(c,98316))},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,327,556,40,121],()=>b(b.s=38620));module.exports=c})();