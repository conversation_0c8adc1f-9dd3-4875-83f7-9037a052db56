'use client';

import { useEffect } from 'react';
import { CheckCircle, User, Briefcase, MapPin, Clock } from 'lucide-react';
import { FuncionarioData } from './funcionario-wizard';

interface StepConfirmacaoProps {
  data: Partial<FuncionarioData>;
  onValidationChange: (isValid: boolean) => void;
}

export function StepConfirmacao({ data, onValidationChange }: StepConfirmacaoProps) {
  useEffect(() => {
    // Validar se todos os dados obrigatórios estão preenchidos
    const isValid = !!(
      data.nomeCompleto &&
      data.cpf &&
      data.matricula &&
      data.cargo &&
      data.setor &&
      data.dataAdmissao &&
      data.horarioEntrada &&
      data.horarioSaida &&
      data.cargaHoraria
    );
    
    onValidationChange(isValid);
  }, [data, onValidationChange]);

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  const formatPhone = (phone: string) => {
    if (!phone) return '';
    const numbers = phone.replace(/\D/g, '');
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };

  const formatCurrency = (value: string | number) => {
    if (!value) return '';
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return numValue.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-green-100 rounded-full">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Confirmação dos Dados</h2>
        <p className="text-gray-600">Revise todas as informações antes de finalizar o cadastro</p>
      </div>

      <div className="space-y-6">
        {/* Dados Pessoais */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Dados Pessoais</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Nome Completo</span>
              <p className="text-gray-900">{data.nomeCompleto || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">CPF</span>
              <p className="text-gray-900">{data.cpf ? formatCPF(data.cpf) : '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">RG</span>
              <p className="text-gray-900">{data.rg || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Email</span>
              <p className="text-gray-900">{data.email || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Telefone</span>
              <p className="text-gray-900">{data.telefone ? formatPhone(data.telefone) : '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Celular</span>
              <p className="text-gray-900">{data.celular ? formatPhone(data.celular) : '-'}</p>
            </div>
          </div>

          {/* Endereço */}
          {(data.logradouro || data.cidade) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center mb-2">
                <MapPin className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-500">Endereço</span>
              </div>
              <p className="text-gray-900">
                {[
                  data.logradouro,
                  data.numero,
                  data.complemento,
                  data.bairro,
                  data.cidade,
                  data.uf
                ].filter(Boolean).join(', ') || '-'}
              </p>
              {data.cep && (
                <p className="text-sm text-gray-600">CEP: {data.cep}</p>
              )}
            </div>
          )}
        </div>

        {/* Dados Profissionais */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Briefcase className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Dados Profissionais</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Matrícula</span>
              <p className="text-gray-900">{data.matricula || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Data de Admissão</span>
              <p className="text-gray-900">{data.dataAdmissao ? formatDate(data.dataAdmissao) : '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Cargo</span>
              <p className="text-gray-900">{data.cargo || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Setor</span>
              <p className="text-gray-900 capitalize">{data.setor || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Salário</span>
              <p className="text-gray-900">{data.salario ? formatCurrency(data.salario) : '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Carga Horária</span>
              <p className="text-gray-900">{data.cargaHoraria ? `${data.cargaHoraria}h semanais` : '-'}</p>
            </div>
          </div>

          {/* Horários */}
          {(data.horarioEntrada || data.horarioSaida) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center mb-2">
                <Clock className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-500">Horários de Trabalho</span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium text-gray-500">Expediente</span>
                  <p className="text-gray-900">
                    {data.horarioEntrada && data.horarioSaida 
                      ? `${data.horarioEntrada} às ${data.horarioSaida}`
                      : '-'
                    }
                  </p>
                </div>
                {data.intervaloInicio && data.intervaloFim && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Intervalo</span>
                    <p className="text-gray-900">
                      {`${data.intervaloInicio} às ${data.intervaloFim}`}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Observações */}
          {data.observacoes && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <span className="text-sm font-medium text-gray-500">Observações</span>
              <p className="text-gray-900 mt-1">{data.observacoes}</p>
            </div>
          )}
        </div>
      </div>

      {/* Aviso */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircle className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Confirme os dados antes de finalizar
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Verifique se todas as informações estão corretas. Após confirmar, 
                o funcionário será cadastrado no sistema e poderá começar a utilizar 
                o controle de ponto.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
