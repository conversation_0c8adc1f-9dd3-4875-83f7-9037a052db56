# 🔑 Guia Co<PERSON>to de SSH - Sistema RLPONTO

## 📊 Status Atual da Conexão SSH

**Data da Configuração**: 26/07/2025  
**Status**: ✅ **TOTALMENTE CONFIGURADO E FUNCIONANDO**

### ✅ **Configuração Atual:**
- **Servidor**: ************ (Container LXC Ubuntu 22.04)
- **Usuário**: root
- **Chave SSH**: ed25519 (rl-ponto-next)
- **Alias**: rlponto-prod
- **Autenticação**: Sem senha (chave pública)

### 🎯 **Testes Realizados e Aprovados:**
```bash
✅ ssh rlponto-prod "whoami"           # Retorna: root
✅ ssh rlponto-prod "hostname"         # Retorna: RLPONTO
✅ ssh rlponto-prod "uptime"           # Funciona sem pedir senha
✅ ssh root@************ "date"        # Funciona com IP direto
```

## 🔧 Como Usar a Conexão SSH

### 1. **Conexão Simples**
```bash
# Conectar ao servidor (método recomendado)
ssh rlponto-prod

# Conectar com IP direto
ssh root@************
```

### 2. **Executar Comandos Remotos**
```bash
# Verificar status do servidor
ssh rlponto-prod "whoami && hostname && uptime"

# Verificar espaço em disco
ssh rlponto-prod "df -h /"

# Verificar memória
ssh rlponto-prod "free -h"

# Verificar processos
ssh rlponto-prod "ps aux | head -10"
```

### 3. **Transferir Arquivos**
```bash
# Copiar arquivo local para servidor
scp arquivo.txt rlponto-prod:/tmp/

# Copiar arquivo do servidor para local
scp rlponto-prod:/tmp/arquivo.txt ./

# Copiar diretório completo
scp -r diretorio/ rlponto-prod:/opt/
```

### 4. **Usar com Scripts de Deploy**
```bash
# Deploy automatizado (usa SSH configurado)
bash scripts/deploy.sh

# Setup completo do servidor
bash scripts/setup-complete.sh

# Backup e monitoramento
ssh rlponto-prod '/opt/rlponto/backup-and-monitor.sh monitor'
```

## 📁 Estrutura de Arquivos SSH

### **No Computador Local (Windows):**
```
C:\Users\<USER>\.ssh\
├── config                           # Configuração SSH
├── rl-ponto-next                    # Chave privada (419 bytes)
├── rl-ponto-next.pub                # Chave pública (111 bytes)
├── known_hosts                      # Hosts conhecidos
└── [outras chaves de outros projetos]
```

### **No Servidor (Ubuntu 22.04):**
```
/root/.ssh/
├── authorized_keys                  # Nossa chave pública instalada
└── known_hosts                     # Hosts conhecidos
```

## 🔍 Detalhes da Configuração

### **Arquivo ~/.ssh/config:**
```bash
# Sistema RLPONTO - Servidor de Producao
Host rlponto-prod
    HostName ************
    User root
    IdentityFile ~/.ssh/rl-ponto-next
    IdentitiesOnly yes
```

### **Chave SSH (ed25519):**
```
Tipo: ed25519 (algoritmo moderno e seguro)
Fingerprint: SHA256:CPu0onh9cZL0HC2hDc4ElF8nrKexk2w7yfMIvXsK+Zg
Comentário: rlponto-deploy@Richardson-PC
Criada em: 26/07/2025 15:13
```

### **Chave Pública Instalada no Servidor:**
```
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJfUOsuBalusNYBehe0HSJUwECRdZ/hC0O1CMndQtfPE rlponto-deploy@Richardson-PC
```

## 🛠️ Como Recriar a Configuração SSH (Se Necessário)

### **Cenário 1: Configuração Perdida/Corrompida**

#### Passo 1: Gerar Nova Chave SSH
```bash
# Gerar chave ed25519 específica para RLPONTO
ssh-keygen -t ed25519 -f "C:\Users\<USER>\.ssh\rl-ponto-next" -N "" -C "rlponto-deploy@Richardson-PC"
```

#### Passo 2: Criar Configuração SSH
```bash
# Adicionar ao arquivo ~/.ssh/config
echo "" >> "C:\Users\<USER>\.ssh\config"
echo "# Sistema RLPONTO - Servidor de Producao" >> "C:\Users\<USER>\.ssh\config"
echo "Host rlponto-prod" >> "C:\Users\<USER>\.ssh\config"
echo "    HostName ************" >> "C:\Users\<USER>\.ssh\config"
echo "    User root" >> "C:\Users\<USER>\.ssh\config"
echo "    IdentityFile ~/.ssh/rl-ponto-next" >> "C:\Users\<USER>\.ssh\config"
echo "    IdentitiesOnly yes" >> "C:\Users\<USER>\.ssh\config"
```

#### Passo 3: Instalar Chave no Servidor
```bash
# Conectar ao servidor com senha
ssh root@************
# Senha: @Ric6109

# No servidor, executar:
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Adicionar chave pública (substituir pela chave atual)
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJfUOsuBalusNYBehe0HSJUwECRdZ/hC0O1CMndQtfPE rlponto-deploy@Richardson-PC" >> ~/.ssh/authorized_keys

chmod 600 ~/.ssh/authorized_keys
exit
```

#### Passo 4: Testar Configuração
```bash
# Testar SSH sem senha
ssh rlponto-prod "echo 'SSH funcionando!' && whoami"
```

### **Cenário 2: Novo Computador/Usuário**

#### Opção A: Usar Script Automatizado
```bash
# Executar script que faz tudo automaticamente
bash scripts/setup-production.sh
```

#### Opção B: Configuração Manual
Seguir os passos do Cenário 1 acima.

### **Cenário 3: Servidor Novo/Reinstalado**

#### Se o servidor for reinstalado, você precisa:
1. **Manter a chave local** (não recriar)
2. **Reinstalar apenas no servidor**:
```bash
# Conectar com senha
ssh root@************

# Recriar estrutura SSH
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Reinstalar nossa chave pública
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJfUOsuBalusNYBehe0HSJUwECRdZ/hC0O1CMndQtfPE rlponto-deploy@Richardson-PC" > ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

## 🚨 Troubleshooting

### **Problema 1: SSH pede senha novamente**
```bash
# Verificar se chave existe
ls -la "C:\Users\<USER>\.ssh\rl-ponto-next*"

# Verificar configuração
cat "C:\Users\<USER>\.ssh\config" | grep -A 5 "rlponto-prod"

# Testar com chave específica
ssh -i "C:\Users\<USER>\.ssh\rl-ponto-next" root@************
```

### **Problema 2: "Host rlponto-prod not found"**
```bash
# Verificar se configuração existe
grep "rlponto-prod" "C:\Users\<USER>\.ssh\config"

# Se não existir, recriar configuração (ver Cenário 1)
```

### **Problema 3: "Permission denied (publickey)"**
```bash
# Verificar se chave está no servidor
ssh root@************ "cat ~/.ssh/authorized_keys"
# Senha: @Ric6109

# Se não estiver, reinstalar chave (ver Cenário 3)
```

### **Problema 4: Conexão lenta ou timeout**
```bash
# Verificar conectividade
ping ************

# Verificar porta SSH
Test-NetConnection -ComputerName ************ -Port 22

# Conectar com timeout maior
ssh -o ConnectTimeout=30 rlponto-prod
```

## 🔒 Segurança da Configuração SSH

### **Boas Práticas Implementadas:**
- ✅ **Chave específica** para o projeto (não reutiliza chaves)
- ✅ **Algoritmo moderno** (ed25519, mais seguro que RSA)
- ✅ **Sem senha na chave** (para automação, mas protegida por permissões)
- ✅ **IdentitiesOnly yes** (usa apenas a chave especificada)
- ✅ **Permissões corretas** (700 para diretórios, 600 para chaves)

### **Informações Sensíveis:**
- **Chave privada**: `C:\Users\<USER>\.ssh\rl-ponto-next`
- **Senha do servidor**: `@Ric6109` (apenas para emergências)
- **IP do servidor**: `************` (rede local)

### **Backup da Configuração:**
```bash
# Fazer backup das chaves SSH
copy "C:\Users\<USER>\.ssh\rl-ponto-next*" "backup_ssh_rlponto\"

# Fazer backup da configuração
copy "C:\Users\<USER>\.ssh\config" "backup_ssh_rlponto\config_backup"
```

## 📋 Comandos Úteis para Administração

### **Monitoramento do Servidor:**
```bash
# Status geral do sistema
ssh rlponto-prod "uptime && df -h && free -h"

# Verificar logs do sistema
ssh rlponto-prod "tail -f /var/log/syslog"

# Verificar processos da aplicação
ssh rlponto-prod "ps aux | grep -E '(node|npm|pm2)'"
```

### **Gerenciamento de Arquivos:**
```bash
# Listar arquivos da aplicação
ssh rlponto-prod "ls -la /opt/rlponto/"

# Verificar logs da aplicação
ssh rlponto-prod "tail -f /var/log/rlponto/combined.log"

# Backup manual
ssh rlponto-prod "/opt/rlponto/backup-and-monitor.sh backup"
```

### **Manutenção do Sistema:**
```bash
# Atualizar sistema
ssh rlponto-prod "apt update && apt upgrade -y"

# Reiniciar serviços
ssh rlponto-prod "systemctl restart nginx mysql"

# Verificar status dos serviços
ssh rlponto-prod "systemctl status nginx mysql"
```

## 🎯 Integração com Scripts do Projeto

### **Scripts que Usam SSH:**
1. **`scripts/setup-complete.sh`** - Configuração completa do servidor
2. **`scripts/deploy.sh`** - Deploy da aplicação
3. **`scripts/backup-and-monitor.sh`** - Backup e monitoramento

### **Como os Scripts Usam SSH:**
```bash
# Exemplo de uso nos scripts
ssh rlponto-prod "comando_no_servidor"
scp arquivo.tar.gz rlponto-prod:/tmp/
ssh rlponto-prod "cd /opt/rlponto && tar -xzf /tmp/arquivo.tar.gz"
```

## 📞 Suporte e Contatos

### **Em caso de problemas com SSH:**
1. **Verificar conectividade**: `ping ************`
2. **Testar SSH básico**: `ssh root@************` (com senha)
3. **Verificar configuração**: Seguir troubleshooting acima
4. **Recriar configuração**: Usar scripts automatizados

### **Informações para Suporte:**
- **Servidor**: ************ (Container LXC)
- **SO**: Ubuntu 22.04
- **Chave SSH**: ed25519 (rl-ponto-next)
- **Configuração**: Arquivo config com alias rlponto-prod

---

## 📊 Resumo Executivo

### ✅ **Status Atual:**
**SSH está 100% configurado e funcionando perfeitamente**

### 🔑 **Acesso Rápido:**
```bash
ssh rlponto-prod  # Conecta sem senha
```

### 🚀 **Próximos Passos:**
1. **Deploy**: `bash scripts/deploy.sh`
2. **Monitoramento**: `ssh rlponto-prod "comando"`
3. **Manutenção**: Usar comandos SSH conforme necessário

**Configuração SSH completa e pronta para uso em produção!** 🎉

---

**Documento criado em**: 26/07/2025  
**Última atualização**: 26/07/2025  
**Versão**: 1.0  
**Responsável**: Equipe DevOps RLPONTO
