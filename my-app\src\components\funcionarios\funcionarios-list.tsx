'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui';
// import { FuncionarioCard } from './funcionario-card';
import { Funcionario } from '@/types';
import { 
  Users, 
  Eye, 
  Edit, 
  Trash2, 
  MoreVertical,
  UserCheck,
  UserX
} from 'lucide-react';

interface FuncionariosListProps {
  searchParams: {
    search?: string;
    setor?: string;
    status?: string;
    page?: string;
  };
}

export function FuncionariosList({ searchParams }: FuncionariosListProps) {
  const [funcionarios, setFuncionarios] = useState<Funcionario[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchFuncionarios();
  }, [searchParams]);

  const fetchFuncionarios = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (searchParams.search) params.set('search', searchParams.search);
      if (searchParams.setor) params.set('setor', searchParams.setor);
      if (searchParams.status) params.set('status', searchParams.status);
      if (searchParams.page) params.set('page', searchParams.page);

      const response = await fetch(`/api/funcionarios?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Erro ao carregar funcionários');
      }

      const data = await response.json();
      setFuncionarios(data.funcionarios || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <FuncionariosListSkeleton />;
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <div className="text-red-500 mb-4">
          <UserX className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Erro ao carregar funcionários</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={fetchFuncionarios} variant="primary">
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (funcionarios.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <div className="text-gray-400 mb-4">
          <Users className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum funcionário encontrado</h3>
        <p className="text-gray-600 mb-4">
          {searchParams.search || searchParams.setor || searchParams.status
            ? 'Tente ajustar os filtros de busca.'
            : 'Comece cadastrando o primeiro funcionário.'}
        </p>
        <Link href="/funcionarios/novo">
          <Button variant="primary">
            Cadastrar Funcionário
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <UserCheck className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-gray-900">
              {funcionarios.length} funcionário{funcionarios.length !== 1 ? 's' : ''} encontrado{funcionarios.length !== 1 ? 's' : ''}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              Exportar Lista
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {funcionarios.map((funcionario) => (
            <div key={funcionario.id} className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{funcionario.nomeCompleto}</h3>
                <p className="text-sm text-gray-600">{funcionario.dadosProfissionais.cargo} - {funcionario.dadosProfissionais.setor}</p>
                <p className="text-xs text-gray-500">Matrícula: {funcionario.dadosProfissionais.matricula}</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  funcionario.status === 'ativo'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {funcionario.status}
                </span>
                <Link href={`/funcionarios/${funcionario.id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Paginação (implementar se necessário) */}
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Mostrando {funcionarios.length} de {funcionarios.length} funcionários
          </div>
          
          {/* Implementar paginação aqui se necessário */}
        </div>
      </div>
    </div>
  );
}

function FuncionariosListSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
              <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-1/5" />
              </div>
              <div className="w-20 h-8 bg-gray-200 rounded animate-pulse" />
              <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
