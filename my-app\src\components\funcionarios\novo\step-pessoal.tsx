'use client';

import { useEffect, useState } from 'react';
import { Input, Button } from '@/components/ui';
import { User, Mail, Phone, MapPin } from 'lucide-react';
import { FuncionarioData } from './funcionario-wizard';

interface StepPessoalProps {
  data: Partial<FuncionarioData>;
  onDataChange: (data: Partial<FuncionarioData>) => void;
  onValidationChange: (isValid: boolean) => void;
}

export function StepPessoal({ data, onDataChange, onValidationChange }: StepPessoalProps) {
  const [formData, setFormData] = useState({
    nomeCompleto: data.nomeCompleto || '',
    cpf: data.cpf || '',
    rg: data.rg || '',
    email: data.email || '',
    telefone: data.telefone || '',
    celular: data.celular || '',
    cep: data.cep || '',
    logradouro: data.logradouro || '',
    numero: data.numero || '',
    complemento: data.complemento || '',
    bairro: data.bairro || '',
    cidade: data.cidade || '',
    uf: data.uf || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.nomeCompleto.trim()) {
      newErrors.nomeCompleto = 'Nome completo é obrigatório';
    }

    if (!formData.cpf.trim()) {
      newErrors.cpf = 'CPF é obrigatório';
    } else if (!/^\d{11}$/.test(formData.cpf.replace(/\D/g, ''))) {
      newErrors.cpf = 'CPF deve ter 11 dígitos';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);
    onDataChange(newFormData);
  };

  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  const formatCEP = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{5})(\d{3})/, '$1-$2');
  };

  const buscarCEP = async (cep: string) => {
    const cepNumbers = cep.replace(/\D/g, '');
    if (cepNumbers.length === 8) {
      try {
        const response = await fetch(`https://viacep.com.br/ws/${cepNumbers}/json/`);
        const data = await response.json();
        
        if (!data.erro) {
          setFormData(prev => ({
            ...prev,
            logradouro: data.logradouro || '',
            bairro: data.bairro || '',
            cidade: data.localidade || '',
            uf: data.uf || '',
          }));
          
          onDataChange({
            ...formData,
            logradouro: data.logradouro || '',
            bairro: data.bairro || '',
            cidade: data.localidade || '',
            uf: data.uf || '',
          });
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
      }
    }
  };

  useEffect(() => {
    validateForm();
  }, [formData]);

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <User className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Dados Pessoais</h2>
        <p className="text-gray-600">Informe os dados pessoais do funcionário</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Nome Completo */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome Completo *
          </label>
          <Input
            placeholder="Digite o nome completo"
            value={formData.nomeCompleto}
            onChange={(e) => handleInputChange('nomeCompleto', e.target.value)}
            error={errors.nomeCompleto}
          />
        </div>

        {/* CPF */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CPF *
          </label>
          <Input
            placeholder="000.000.000-00"
            value={formatCPF(formData.cpf)}
            onChange={(e) => handleInputChange('cpf', e.target.value.replace(/\D/g, ''))}
            error={errors.cpf}
          />
        </div>

        {/* RG */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            RG
          </label>
          <Input
            placeholder="Digite o RG"
            value={formData.rg}
            onChange={(e) => handleInputChange('rg', e.target.value)}
          />
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email
          </label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
          />
        </div>

        {/* Telefone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Telefone
          </label>
          <Input
            placeholder="(11) 1234-5678"
            value={formatPhone(formData.telefone)}
            onChange={(e) => handleInputChange('telefone', e.target.value.replace(/\D/g, ''))}
          />
        </div>

        {/* Celular */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Celular
          </label>
          <Input
            placeholder="(11) 99999-9999"
            value={formatPhone(formData.celular)}
            onChange={(e) => handleInputChange('celular', e.target.value.replace(/\D/g, ''))}
          />
        </div>

        {/* CEP */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CEP
          </label>
          <div className="flex space-x-2">
            <Input
              placeholder="00000-000"
              value={formatCEP(formData.cep)}
              onChange={(e) => handleInputChange('cep', e.target.value.replace(/\D/g, ''))}
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => buscarCEP(formData.cep)}
              disabled={formData.cep.replace(/\D/g, '').length !== 8}
            >
              Buscar
            </Button>
          </div>
        </div>

        {/* Logradouro */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Logradouro
          </label>
          <Input
            placeholder="Rua, Avenida, etc."
            value={formData.logradouro}
            onChange={(e) => handleInputChange('logradouro', e.target.value)}
          />
        </div>

        {/* Número */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Número
          </label>
          <Input
            placeholder="123"
            value={formData.numero}
            onChange={(e) => handleInputChange('numero', e.target.value)}
          />
        </div>

        {/* Complemento */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Complemento
          </label>
          <Input
            placeholder="Apto, Bloco, etc."
            value={formData.complemento}
            onChange={(e) => handleInputChange('complemento', e.target.value)}
          />
        </div>

        {/* Bairro */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Bairro
          </label>
          <Input
            placeholder="Nome do bairro"
            value={formData.bairro}
            onChange={(e) => handleInputChange('bairro', e.target.value)}
          />
        </div>

        {/* Cidade */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cidade
          </label>
          <Input
            placeholder="Nome da cidade"
            value={formData.cidade}
            onChange={(e) => handleInputChange('cidade', e.target.value)}
          />
        </div>

        {/* UF */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            UF
          </label>
          <select
            value={formData.uf}
            onChange={(e) => handleInputChange('uf', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Selecione</option>
            <option value="SP">São Paulo</option>
            <option value="RJ">Rio de Janeiro</option>
            <option value="MG">Minas Gerais</option>
            <option value="RS">Rio Grande do Sul</option>
            <option value="PR">Paraná</option>
            <option value="SC">Santa Catarina</option>
            {/* Adicionar outros estados conforme necessário */}
          </select>
        </div>
      </div>
    </div>
  );
}
