# 📊 MÓDULO DASHBOARD - Período de Apuração

## 📋 Visão Geral
Dashboard principal para visualização e análise do período de apuração de ponto.

## 🎯 Funcionalidades
- Visão geral do período atual
- Métricas de frequência
- Gráficos de produtividade
- Alertas e inconsistências
- Resumo por funcionário
- Comparativo mensal
- Exportação de dados

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── periodo-apuracao/
│           ├── page.tsx                    # Dashboard principal
│           └── components/
│               ├── metrics-overview.tsx    # Métricas gerais
│               ├── frequency-chart.tsx     # Gráfico de frequência
│               ├── alerts-panel.tsx        # Painel de alertas
│               ├── employee-summary.tsx    # Resumo por funcionário
│               └── period-selector.tsx     # Seletor de período
├── components/
│   └── dashboard/
│       ├── stat-card.tsx                  # Card de estatística
│       ├── chart-container.tsx            # Container de gráficos
│       ├── data-table.tsx                 # Tabela de dados
│       └── export-button.tsx              # Botão de exportação
└── api/
    └── periodo-apuracao/
        ├── dashboard/
        │   └── route.ts                   # API do dashboard
        ├── metrics/
        │   └── route.ts                   # Métricas
        └── export/
            └── route.ts                   # Exportação
```

## 🔧 Implementação Técnica

### 📊 Dashboard Principal (page.tsx)
```typescript
// app/(dashboard)/periodo-apuracao/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { MetricsOverview } from './components/metrics-overview';
import { FrequencyChart } from './components/frequency-chart';
import { AlertsPanel } from './components/alerts-panel';
import { EmployeeSummary } from './components/employee-summary';
import { PeriodSelector } from './components/period-selector';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3, Calendar, Users, AlertTriangle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Dashboard - Período de Apuração',
  description: 'Visão geral do período de apuração de ponto',
};

interface DashboardPageProps {
  searchParams: {
    periodo?: string;
    ano?: string;
    mes?: string;
  };
}

export default function PeriodoApuracaoPage({ searchParams }: DashboardPageProps) {
  const currentPeriod = {
    ano: parseInt(searchParams.ano || new Date().getFullYear().toString()),
    mes: parseInt(searchParams.mes || (new Date().getMonth() + 1).toString()),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Período de Apuração</h1>
            <p className="text-gray-600">Dashboard de análise e controle de frequência</p>
          </div>
        </div>
        <PeriodSelector currentPeriod={currentPeriod} />
      </div>

      {/* Métricas Principais */}
      <Suspense fallback={<MetricsSkeleton />}>
        <MetricsOverview period={currentPeriod} />
      </Suspense>

      {/* Grid Principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Gráfico de Frequência */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                <span>Frequência Diária</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<ChartSkeleton />}>
                <FrequencyChart period={currentPeriod} />
              </Suspense>
            </CardContent>
          </Card>
        </div>

        {/* Alertas */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <span>Alertas</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<AlertsSkeleton />}>
                <AlertsPanel period={currentPeriod} />
              </Suspense>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Resumo por Funcionário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-green-600" />
            <span>Resumo por Funcionário</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<TableSkeleton />}>
            <EmployeeSummary period={currentPeriod} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

// Skeletons
function MetricsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-24 bg-gray-200 rounded-lg animate-pulse" />
      ))}
    </div>
  );
}

function ChartSkeleton() {
  return <div className="h-80 bg-gray-200 rounded animate-pulse" />;
}

function AlertsSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}

function TableSkeleton() {
  return (
    <div className="space-y-2">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}
```

### 📈 Métricas Gerais (metrics-overview.tsx)
```typescript
// app/(dashboard)/periodo-apuracao/components/metrics-overview.tsx
'use client';

import { useEffect, useState } from 'react';
import { StatCard } from '@/components/dashboard/stat-card';
import { Users, Clock, AlertTriangle, CheckCircle } from 'lucide-react';

interface MetricsData {
  totalFuncionarios: number;
  funcionariosAtivos: number;
  horasTrabalhadasTotal: number;
  horasEsperadasTotal: number;
  registrosInconsistentes: number;
  registrosAprovados: number;
  percentualFrequencia: number;
  percentualPontualidade: number;
}

interface MetricsOverviewProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function MetricsOverview({ period }: MetricsOverviewProps) {
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchMetrics();
  }, [period]);

  const fetchMetrics = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/periodo-apuracao/metrics?ano=${period.ano}&mes=${period.mes}`
      );
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      }
    } catch (error) {
      console.error('Erro ao buscar métricas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading || !metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  const formatHours = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title="Funcionários Ativos"
        value={metrics.funcionariosAtivos}
        subtitle={`de ${metrics.totalFuncionarios} total`}
        icon={Users}
        color="blue"
        trend={{
          value: ((metrics.funcionariosAtivos / metrics.totalFuncionarios) * 100),
          label: "% do total"
        }}
      />

      <StatCard
        title="Horas Trabalhadas"
        value={formatHours(metrics.horasTrabalhadasTotal)}
        subtitle={`de ${formatHours(metrics.horasEsperadasTotal)} esperadas`}
        icon={Clock}
        color="green"
        trend={{
          value: ((metrics.horasTrabalhadasTotal / metrics.horasEsperadasTotal) * 100),
          label: "% cumpridas"
        }}
      />

      <StatCard
        title="Frequência"
        value={`${metrics.percentualFrequencia.toFixed(1)}%`}
        subtitle="Presença no período"
        icon={CheckCircle}
        color={metrics.percentualFrequencia >= 95 ? "green" : metrics.percentualFrequencia >= 85 ? "yellow" : "red"}
        trend={{
          value: metrics.percentualFrequencia,
          label: "do esperado"
        }}
      />

      <StatCard
        title="Inconsistências"
        value={metrics.registrosInconsistentes}
        subtitle={`${metrics.registrosAprovados} aprovados`}
        icon={AlertTriangle}
        color={metrics.registrosInconsistentes > 0 ? "red" : "green"}
        trend={{
          value: metrics.registrosInconsistentes === 0 ? 100 : 
                 ((metrics.registrosAprovados / (metrics.registrosAprovados + metrics.registrosInconsistentes)) * 100),
          label: "% resolvidas"
        }}
      />
    </div>
  );
}
```

### 📊 Gráfico de Frequência (frequency-chart.tsx)
```typescript
// app/(dashboard)/periodo-apuracao/components/frequency-chart.tsx
'use client';

import { useEffect, useState } from 'react';
import { Line, LineChart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

interface FrequencyData {
  dia: string;
  presentes: number;
  ausentes: number;
  atrasados: number;
  horasExtras: number;
  percentualFrequencia: number;
}

interface FrequencyChartProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function FrequencyChart({ period }: FrequencyChartProps) {
  const [data, setData] = useState<FrequencyData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchFrequencyData();
  }, [period]);

  const fetchFrequencyData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/periodo-apuracao/frequency?ano=${period.ano}&mes=${period.mes}`
      );
      if (response.ok) {
        const data = await response.json();
        setData(data.frequencyData);
      }
    } catch (error) {
      console.error('Erro ao buscar dados de frequência:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div className="h-80 bg-gray-200 rounded animate-pulse" />;
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="dia" 
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => {
              const date = new Date(value);
              return `${date.getDate()}/${date.getMonth() + 1}`;
            }}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip 
            labelFormatter={(value) => {
              const date = new Date(value);
              return date.toLocaleDateString('pt-BR');
            }}
            formatter={(value, name) => {
              const labels = {
                presentes: 'Presentes',
                ausentes: 'Ausentes',
                atrasados: 'Atrasados',
                percentualFrequencia: 'Frequência (%)'
              };
              return [value, labels[name as keyof typeof labels] || name];
            }}
          />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="presentes" 
            stroke="#10b981" 
            strokeWidth={2}
            name="presentes"
          />
          <Line 
            type="monotone" 
            dataKey="ausentes" 
            stroke="#ef4444" 
            strokeWidth={2}
            name="ausentes"
          />
          <Line 
            type="monotone" 
            dataKey="atrasados" 
            stroke="#f59e0b" 
            strokeWidth={2}
            name="atrasados"
          />
          <Line 
            type="monotone" 
            dataKey="percentualFrequencia" 
            stroke="#3b82f6" 
            strokeWidth={2}
            strokeDasharray="5 5"
            name="percentualFrequencia"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
```

### 🚨 Painel de Alertas (alerts-panel.tsx)
```typescript
// app/(dashboard)/periodo-apuracao/components/alerts-panel.tsx
'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Clock, User, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Alert {
  id: number;
  tipo: 'ausencia' | 'atraso' | 'inconsistencia' | 'horas_extras';
  funcionario: {
    id: number;
    nomeCompleto: string;
    cargo: string;
  };
  descricao: string;
  data: string;
  prioridade: 'baixa' | 'media' | 'alta';
  resolvido: boolean;
}

interface AlertsPanelProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function AlertsPanel({ period }: AlertsPanelProps) {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchAlerts();
  }, [period]);

  const fetchAlerts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/periodo-apuracao/alerts?ano=${period.ano}&mes=${period.mes}`
      );
      if (response.ok) {
        const data = await response.json();
        setAlerts(data.alerts);
      }
    } catch (error) {
      console.error('Erro ao buscar alertas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPriorityColor = (prioridade: string) => {
    switch (prioridade) {
      case 'alta':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'media':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'baixa':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (tipo: string) => {
    switch (tipo) {
      case 'ausencia':
        return <User className="h-4 w-4" />;
      case 'atraso':
        return <Clock className="h-4 w-4" />;
      case 'inconsistencia':
        return <AlertTriangle className="h-4 w-4" />;
      case 'horas_extras':
        return <Calendar className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (tipo: string) => {
    switch (tipo) {
      case 'ausencia':
        return 'Ausência';
      case 'atraso':
        return 'Atraso';
      case 'inconsistencia':
        return 'Inconsistência';
      case 'horas_extras':
        return 'Horas Extras';
      default:
        return tipo;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
        ))}
      </div>
    );
  }

  const pendingAlerts = alerts.filter(alert => !alert.resolvido);

  if (pendingAlerts.length === 0) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 text-green-400 mx-auto mb-4" />
        <p className="text-green-600 font-medium">Nenhum alerta pendente</p>
        <p className="text-sm text-gray-500">Tudo está funcionando perfeitamente!</p>
      </div>
    );
  }

  return (
    <div className="space-y-3 max-h-96 overflow-y-auto">
      {pendingAlerts.slice(0, 10).map((alert) => (
        <div
          key={alert.id}
          className={cn(
            'p-3 rounded-lg border transition-colors hover:shadow-sm',
            getPriorityColor(alert.prioridade)
          )}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-2">
              {getTypeIcon(alert.tipo)}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <Badge variant="outline" className="text-xs">
                    {getTypeLabel(alert.tipo)}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {new Date(alert.data).toLocaleDateString('pt-BR')}
                  </span>
                </div>
                <p className="text-sm font-medium text-gray-900 truncate">
                  {alert.funcionario.nomeCompleto}
                </p>
                <p className="text-xs text-gray-600 mt-1">
                  {alert.descricao}
                </p>
              </div>
            </div>
          </div>
        </div>
      ))}

      {pendingAlerts.length > 10 && (
        <div className="text-center pt-2">
          <Button variant="outline" size="sm">
            Ver todos os alertas ({pendingAlerts.length})
          </Button>
        </div>
      )}
    </div>
  );
}
```

## 🔌 API Routes

### 📊 API de Métricas (metrics/route.ts)
```typescript
// app/api/periodo-apuracao/metrics/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const ano = parseInt(searchParams.get('ano') || new Date().getFullYear().toString());
    const mes = parseInt(searchParams.get('mes') || (new Date().getMonth() + 1).toString());

    // Período de apuração
    const startDate = new Date(ano, mes - 1, 1);
    const endDate = new Date(ano, mes, 0, 23, 59, 59);

    // Buscar métricas
    const [
      totalFuncionarios,
      funcionariosAtivos,
      registrosPonto,
      registrosInconsistentes,
      registrosAprovados
    ] = await Promise.all([
      prisma.funcionario.count(),
      prisma.funcionario.count({ where: { ativo: true } }),
      prisma.registroPonto.findMany({
        where: {
          timestamp: { gte: startDate, lte: endDate },
          funcionario: { ativo: true }
        },
        include: { funcionario: true }
      }),
      prisma.registroPonto.count({
        where: {
          timestamp: { gte: startDate, lte: endDate },
          aprovado: false,
          metodo: 'manual'
        }
      }),
      prisma.registroPonto.count({
        where: {
          timestamp: { gte: startDate, lte: endDate },
          aprovado: true,
          metodo: 'manual'
        }
      })
    ]);

    // Calcular horas trabalhadas
    const funcionariosComRegistros = registrosPonto.reduce((acc, registro) => {
      if (!acc[registro.funcionarioId]) {
        acc[registro.funcionarioId] = [];
      }
      acc[registro.funcionarioId].push(registro);
      return acc;
    }, {} as Record<number, any[]>);

    let horasTrabalhadasTotal = 0;
    let diasTrabalhados = 0;
    let diasEsperados = 0;

    Object.values(funcionariosComRegistros).forEach((registros) => {
      const diasUnicos = new Set(registros.map(r => 
        new Date(r.timestamp).toDateString()
      ));
      
      diasTrabalhados += diasUnicos.size;
      
      // Calcular horas por dia
      diasUnicos.forEach(dia => {
        const registrosDia = registros.filter(r => 
          new Date(r.timestamp).toDateString() === dia
        ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

        if (registrosDia.length >= 2) {
          const entrada = new Date(registrosDia[0].timestamp);
          const saida = new Date(registrosDia[registrosDia.length - 1].timestamp);
          const horasMinutos = (saida.getTime() - entrada.getTime()) / (1000 * 60);
          horasTrabalhadasTotal += horasMinutos;
        }
      });
    });

    // Dias úteis esperados no mês
    const diasUteis = calcularDiasUteis(startDate, endDate);
    diasEsperados = funcionariosAtivos * diasUteis;
    const horasEsperadasTotal = diasEsperados * 8 * 60; // 8 horas por dia em minutos

    // Calcular percentuais
    const percentualFrequencia = diasEsperados > 0 ? (diasTrabalhados / diasEsperados) * 100 : 0;
    const percentualPontualidade = calcularPontualidade(registrosPonto);

    return NextResponse.json({
      totalFuncionarios,
      funcionariosAtivos,
      horasTrabalhadasTotal: Math.round(horasTrabalhadasTotal),
      horasEsperadasTotal: Math.round(horasEsperadasTotal),
      registrosInconsistentes,
      registrosAprovados,
      percentualFrequencia: Math.round(percentualFrequencia * 100) / 100,
      percentualPontualidade: Math.round(percentualPontualidade * 100) / 100,
    });
  } catch (error) {
    console.error('Erro ao buscar métricas:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

function calcularDiasUteis(startDate: Date, endDate: Date): number {
  let count = 0;
  const current = new Date(startDate);
  
  while (current <= endDate) {
    const dayOfWeek = current.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Não é domingo nem sábado
      count++;
    }
    current.setDate(current.getDate() + 1);
  }
  
  return count;
}

function calcularPontualidade(registros: any[]): number {
  const registrosEntrada = registros.filter(r => r.tipo === 'entrada');
  if (registrosEntrada.length === 0) return 100;

  const pontuais = registrosEntrada.filter(r => {
    const hora = new Date(r.timestamp).getHours();
    const minutos = new Date(r.timestamp).getMinutes();
    const totalMinutos = hora * 60 + minutos;
    const horarioEsperado = 8 * 60; // 08:00
    return totalMinutos <= horarioEsperado + 15; // Tolerância de 15 minutos
  });

  return (pontuais.length / registrosEntrada.length) * 100;
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades do Dashboard
- [ ] Métricas gerais do período
- [ ] Gráfico de frequência diária
- [ ] Painel de alertas
- [ ] Resumo por funcionário
- [ ] Seletor de período
- [ ] Exportação de dados
- [ ] Comparativo mensal
- [ ] Filtros avançados

### 📊 Visualizações
- [ ] Cards de estatísticas
- [ ] Gráficos de linha
- [ ] Gráficos de barras
- [ ] Tabelas responsivas
- [ ] Indicadores de tendência
- [ ] Alertas visuais

## 🚀 Próximos Passos
1. **Classificar Horas** - Módulo de classificação
2. **Fechamento** - Processo de fechamento mensal
3. **Relatórios** - Relatórios detalhados do período
