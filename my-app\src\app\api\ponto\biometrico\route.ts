import { NextRequest, NextResponse } from 'next/server';

interface RegistroPontoRequest {
  funcionarioId: string;
  tipo: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim';
  biometricType: 'fingerprint' | 'facial';
  timestamp: string;
  deviceId?: string;
}

interface RegistroPonto {
  id: string;
  funcionarioId: string;
  tipo: string;
  metodo: string;
  horario: string;
  data: string;
  timestamp: Date;
  deviceId?: string;
  status: 'sucesso' | 'erro';
}

// Simulação de banco de dados em memória
const registrosPonto: RegistroPonto[] = [
  {
    id: '1',
    funcionarioId: 'EMP001',
    tipo: 'entrada',
    metodo: 'fingerprint',
    horario: '08:00:15',
    data: new Date().toLocaleDateString('pt-BR'),
    timestamp: new Date(),
    status: 'sucesso'
  },
  {
    id: '2',
    funcionarioId: 'EMP002',
    tipo: 'intervalo_inicio',
    metodo: 'facial',
    horario: '12:00:32',
    data: new Date().toLocaleDateString('pt-BR'),
    timestamp: new Date(),
    status: 'sucesso'
  }
];

export async function POST(request: NextRequest) {
  try {
    const body: RegistroPontoRequest = await request.json();
    
    // Validar dados de entrada
    if (!body.funcionarioId || !body.tipo || !body.biometricType || !body.timestamp) {
      return NextResponse.json(
        {
          success: false,
          error: 'Dados obrigatórios não fornecidos',
        },
        { status: 400 }
      );
    }

    // Validar funcionário (simulação)
    const funcionariosValidos = ['EMP001', 'EMP002', 'EMP003', 'EMP004'];
    if (!funcionariosValidos.includes(body.funcionarioId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Funcionário não encontrado',
        },
        { status: 404 }
      );
    }

    // Validar sequência lógica de registros
    const ultimoRegistro = registrosPonto
      .filter(r => r.funcionarioId === body.funcionarioId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

    const validacaoSequencia = validarSequenciaRegistro(ultimoRegistro?.tipo, body.tipo);
    if (!validacaoSequencia.valido) {
      return NextResponse.json(
        {
          success: false,
          error: validacaoSequencia.erro,
        },
        { status: 400 }
      );
    }

    // Criar novo registro
    const timestamp = new Date(body.timestamp);
    const novoRegistro: RegistroPonto = {
      id: Date.now().toString(),
      funcionarioId: body.funcionarioId,
      tipo: body.tipo,
      metodo: body.biometricType,
      horario: timestamp.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      data: timestamp.toLocaleDateString('pt-BR'),
      timestamp,
      deviceId: body.deviceId,
      status: 'sucesso'
    };

    // Salvar registro
    registrosPonto.push(novoRegistro);

    // Buscar dados do funcionário (simulação)
    const funcionario = getFuncionarioById(body.funcionarioId);

    return NextResponse.json({
      success: true,
      message: 'Ponto registrado com sucesso',
      registro: {
        id: novoRegistro.id,
        funcionario,
        tipo: getTipoLabel(body.tipo),
        horario: novoRegistro.horario,
        data: novoRegistro.data,
        metodo: getMetodoLabel(body.biometricType)
      }
    });

  } catch (error) {
    console.error('Erro ao registrar ponto:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const funcionarioId = searchParams.get('funcionarioId');
    const limit = parseInt(searchParams.get('limit') || '10');

    let registrosFiltrados = [...registrosPonto];

    // Filtrar por funcionário se especificado
    if (funcionarioId) {
      registrosFiltrados = registrosFiltrados.filter(
        r => r.funcionarioId === funcionarioId
      );
    }

    // Ordenar por timestamp (mais recente primeiro)
    registrosFiltrados.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Limitar resultados
    registrosFiltrados = registrosFiltrados.slice(0, limit);

    // Enriquecer com dados do funcionário
    const registrosEnriquecidos = registrosFiltrados.map(registro => ({
      id: registro.id,
      funcionario: getFuncionarioById(registro.funcionarioId),
      tipo: registro.tipo,
      tipoLabel: getTipoLabel(registro.tipo),
      metodo: registro.metodo,
      metodoLabel: getMetodoLabel(registro.metodo),
      horario: registro.horario,
      data: registro.data,
      status: registro.status
    }));

    return NextResponse.json({
      success: true,
      registros: registrosEnriquecidos,
      total: registrosFiltrados.length
    });

  } catch (error) {
    console.error('Erro ao buscar registros:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

// Funções auxiliares
function validarSequenciaRegistro(ultimoTipo: string | undefined, novoTipo: string) {
  // Regras de sequência lógica
  const sequenciasValidas: Record<string, string[]> = {
    undefined: ['entrada'], // Primeiro registro do dia deve ser entrada
    'entrada': ['intervalo_inicio', 'saida'],
    'intervalo_inicio': ['intervalo_fim'],
    'intervalo_fim': ['saida', 'intervalo_inicio'], // Pode ter múltiplos intervalos
    'saida': ['entrada'] // Próximo registro seria entrada do próximo dia
  };

  const tiposPermitidos = sequenciasValidas[ultimoTipo || 'undefined'] || [];
  
  if (!tiposPermitidos.includes(novoTipo)) {
    return {
      valido: false,
      erro: `Sequência inválida. Após ${getTipoLabel(ultimoTipo || '')}, esperado: ${tiposPermitidos.map(getTipoLabel).join(' ou ')}`
    };
  }

  return { valido: true };
}

function getFuncionarioById(id: string) {
  const funcionarios: Record<string, unknown> = {
    'EMP001': {
      nome: 'João Silva Santos',
      matricula: 'EMP001',
      cargo: 'Analista de Sistemas'
    },
    'EMP002': {
      nome: 'Maria Oliveira Costa',
      matricula: 'EMP002',
      cargo: 'Gerente de Vendas'
    },
    'EMP003': {
      nome: 'Carlos Roberto Lima',
      matricula: 'EMP003',
      cargo: 'Operador de Produção'
    },
    'EMP004': {
      nome: 'Ana Paula Silva',
      matricula: 'EMP004',
      cargo: 'Analista de RH'
    }
  };

  return funcionarios[id] || null;
}

function getTipoLabel(tipo: string) {
  const labels: Record<string, string> = {
    'entrada': 'Entrada',
    'saida': 'Saída',
    'intervalo_inicio': 'Início Intervalo',
    'intervalo_fim': 'Fim Intervalo'
  };

  return labels[tipo] || tipo;
}

function getMetodoLabel(metodo: string) {
  const labels: Record<string, string> = {
    'fingerprint': 'Biometria Digital',
    'facial': 'Reconhecimento Facial',
    'manual': 'Manual'
  };

  return labels[metodo] || metodo;
}
