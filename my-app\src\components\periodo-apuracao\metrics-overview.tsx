'use client';

import { useState, useEffect } from 'react';
import { 
  Users, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Timer,
  Calendar
} from 'lucide-react';

interface MetricsData {
  totalFuncionarios: number;
  funcionariosAtivos: number;
  horasTrabalhadasTotal: number;
  horasExtrasTotal: number;
  frequenciaMedia: number;
  alertasTotal: number;
  registrosPendentes: number;
  diasUteis: number;
  diasTrabalhados: number;
  absenteismo: number;
}

interface MetricsOverviewProps {
  period: {
    ano: number;
    mes: number;
  };
}

export function MetricsOverview({ period }: MetricsOverviewProps) {
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMetrics();
  }, [period]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      
      // Simulação de dados (substituir por API real)
      const mockData: MetricsData = {
        totalFuncionarios: 8,
        funcionariosAtivos: 7,
        horasTrabalhadasTotal: 1344, // 8 funcionários * 21 dias * 8h
        horasExtrasTotal: 45,
        frequenciaMedia: 96.5,
        alertasTotal: 3,
        registrosPendentes: 2,
        diasUteis: 22,
        diasTrabalhados: 21,
        absenteismo: 3.5
      };

      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMetrics(mockData);
    } catch (error) {
      console.error('Erro ao buscar métricas:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded" />
              <div className="w-16 h-4 bg-gray-200 rounded" />
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded mb-2" />
            <div className="w-24 h-4 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    );
  }

  const metricCards = [
    {
      title: 'Funcionários Ativos',
      value: metrics.funcionariosAtivos,
      total: metrics.totalFuncionarios,
      icon: Users,
      color: 'blue',
      trend: '+2.5%',
      description: 'funcionários trabalhando'
    },
    {
      title: 'Frequência Média',
      value: `${metrics.frequenciaMedia}%`,
      icon: CheckCircle,
      color: 'green',
      trend: '+1.2%',
      description: 'de presença no período'
    },
    {
      title: 'Horas Trabalhadas',
      value: `${metrics.horasTrabalhadasTotal}h`,
      icon: Clock,
      color: 'purple',
      trend: '+5.8%',
      description: 'total no período'
    },
    {
      title: 'Alertas Pendentes',
      value: metrics.alertasTotal,
      icon: AlertTriangle,
      color: 'red',
      trend: '-12%',
      description: 'inconsistências detectadas'
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-600',
      green: 'bg-green-100 text-green-600',
      purple: 'bg-purple-100 text-purple-600',
      red: 'bg-red-100 text-red-600',
      yellow: 'bg-yellow-100 text-yellow-600'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getTrendColor = (trend: string) => {
    if (trend.startsWith('+')) return 'text-green-600';
    if (trend.startsWith('-')) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Cards Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricCards.map((card, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 rounded-lg ${getColorClasses(card.color)}`}>
                <card.icon className="h-6 w-6" />
              </div>
              <span className={`text-sm font-medium ${getTrendColor(card.trend)}`}>
                {card.trend}
              </span>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-baseline space-x-2">
                <span className="text-2xl font-bold text-gray-900">
                  {card.value}
                </span>
                {card.total && (
                  <span className="text-sm text-gray-500">
                    / {card.total}
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600">{card.description}</p>
              <p className="text-xs font-medium text-gray-900">{card.title}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Métricas Secundárias */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 text-blue-600 rounded-lg">
              <Timer className="h-5 w-5" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Horas Extras</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total do período</span>
              <span className="text-lg font-bold text-gray-900">{metrics.horasExtrasTotal}h</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Média por funcionário</span>
              <span className="text-sm text-gray-900">
                {(metrics.horasExtrasTotal / metrics.funcionariosAtivos).toFixed(1)}h
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${Math.min((metrics.horasExtrasTotal / 100) * 100, 100)}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-100 text-green-600 rounded-lg">
              <Calendar className="h-5 w-5" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Dias Trabalhados</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Dias úteis</span>
              <span className="text-lg font-bold text-gray-900">{metrics.diasUteis}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Trabalhados</span>
              <span className="text-sm text-gray-900">{metrics.diasTrabalhados}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${(metrics.diasTrabalhados / metrics.diasUteis) * 100}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-yellow-100 text-yellow-600 rounded-lg">
              <XCircle className="h-5 w-5" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Absenteísmo</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Taxa do período</span>
              <span className="text-lg font-bold text-gray-900">{metrics.absenteismo}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Registros pendentes</span>
              <span className="text-sm text-gray-900">{metrics.registrosPendentes}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-yellow-600 h-2 rounded-full" 
                style={{ width: `${Math.min(metrics.absenteismo * 10, 100)}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
