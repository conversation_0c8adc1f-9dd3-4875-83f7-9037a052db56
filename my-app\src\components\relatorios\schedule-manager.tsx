'use client';

import { useState } from 'react';
import { Calendar, Mail, Settings, Plus, Trash2 } from 'lucide-react';

interface ScheduleConfig {
  name: string;
  reportType: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
  time: string;
  weekdays: number[];
  monthDay: number;
  cronExpression: string;
  recipients: string[];
  format: 'pdf' | 'excel' | 'csv';
  filters: Record<string, unknown>;
  active: boolean;
}

const reportTypes = [
  { value: 'frequencia', label: 'Relatório de Frequência' },
  { value: 'horas-extras', label: 'Relatório de Horas Extras' },
  { value: 'funcionario', label: 'Relatório por Funcionário' },
  { value: 'periodo', label: 'Relatório por Período' },
  { value: 'analitico', label: 'Relatório Analítico' },
  { value: 'dashboard', label: 'Dashboard Executivo' }
];

const weekdayNames = [
  'Domingo', 'Segunda', 'Ter<PERSON>', 'Quarta', 'Quinta', 'Sexta', 'Sábado'
];

export function ScheduleManager() {
  const [config, setConfig] = useState<ScheduleConfig>({
    name: '',
    reportType: '',
    frequency: 'weekly',
    time: '08:00',
    weekdays: [1], // Segunda-feira
    monthDay: 1,
    cronExpression: '0 8 * * 1',
    recipients: [''],
    format: 'pdf',
    filters: {},
    active: true
  });

  const [currentStep, setCurrentStep] = useState(1);

  const updateConfig = (field: keyof ScheduleConfig, value: unknown) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const addRecipient = () => {
    setConfig(prev => ({
      ...prev,
      recipients: [...prev.recipients, '']
    }));
  };

  const updateRecipient = (index: number, email: string) => {
    setConfig(prev => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => i === index ? email : r)
    }));
  };

  const removeRecipient = (index: number) => {
    setConfig(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  };

  const toggleWeekday = (day: number) => {
    setConfig(prev => ({
      ...prev,
      weekdays: prev.weekdays.includes(day)
        ? prev.weekdays.filter(d => d !== day)
        : [...prev.weekdays, day].sort()
    }));
  };

  const generateCronExpression = () => {
    const [hour, minute] = config.time.split(':');
    
    switch (config.frequency) {
      case 'daily':
        return `${minute} ${hour} * * *`;
      case 'weekly':
        return `${minute} ${hour} * * ${config.weekdays.join(',')}`;
      case 'monthly':
        return `${minute} ${hour} ${config.monthDay} * *`;
      default:
        return config.cronExpression;
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Informações Básicas</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome do Agendamento
          </label>
          <input
            type="text"
            value={config.name}
            onChange={(e) => updateConfig('name', e.target.value)}
            placeholder="Ex: Relatório Mensal de Frequência"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tipo de Relatório
          </label>
          <select
            value={config.reportType}
            onChange={(e) => updateConfig('reportType', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">Selecione um tipo</option>
            {reportTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Formato de Exportação
          </label>
          <select
            value={config.format}
            onChange={(e) => updateConfig('format', e.target.value as 'pdf' | 'excel' | 'csv')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="pdf">PDF</option>
            <option value="excel">Excel</option>
            <option value="csv">CSV</option>
          </select>
        </div>

        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.active}
              onChange={(e) => updateConfig('active', e.target.checked)}
              className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <span className="text-sm font-medium text-gray-700">Ativar agendamento</span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Configuração de Frequência</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Frequência
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[
              { value: 'daily', label: 'Diário', icon: <Calendar className="w-4 h-4" /> },
              { value: 'weekly', label: 'Semanal', icon: <Calendar className="w-4 h-4" /> },
              { value: 'monthly', label: 'Mensal', icon: <Calendar className="w-4 h-4" /> },
              { value: 'custom', label: 'Personalizado', icon: <Settings className="w-4 h-4" /> }
            ].map(freq => (
              <button
                key={freq.value}
                onClick={() => updateConfig('frequency', freq.value)}
                className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                  config.frequency === freq.value
                    ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  {freq.icon}
                  <span>{freq.label}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Horário de Execução
            </label>
            <input
              type="time"
              value={config.time}
              onChange={(e) => updateConfig('time', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {config.frequency === 'weekly' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Dias da Semana
              </label>
              <div className="grid grid-cols-7 gap-1">
                {weekdayNames.map((day, index) => (
                  <button
                    key={index}
                    onClick={() => toggleWeekday(index)}
                    className={`p-2 text-xs font-medium rounded transition-colors ${
                      config.weekdays.includes(index)
                        ? 'bg-indigo-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {day.slice(0, 3)}
                  </button>
                ))}
              </div>
            </div>
          )}

          {config.frequency === 'monthly' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Dia do Mês
              </label>
              <input
                type="number"
                min="1"
                max="31"
                value={config.monthDay}
                onChange={(e) => updateConfig('monthDay', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          )}

          {config.frequency === 'custom' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expressão Cron
              </label>
              <input
                type="text"
                value={config.cronExpression}
                onChange={(e) => updateConfig('cronExpression', e.target.value)}
                placeholder="0 8 * * 1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Formato: minuto hora dia mês dia-da-semana
              </p>
            </div>
          )}
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Preview da Configuração</h4>
          <p className="text-sm text-gray-600">
            Expressão Cron: <code className="bg-white px-2 py-1 rounded">{generateCronExpression()}</code>
          </p>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Destinatários de Email</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Endereços de Email
          </label>
          <div className="space-y-3">
            {config.recipients.map((email, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => updateRecipient(index, e.target.value)}
                  placeholder="<EMAIL>"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                {config.recipients.length > 1 && (
                  <button
                    onClick={() => removeRecipient(index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
          
          <button
            onClick={addRecipient}
            className="mt-3 flex items-center space-x-2 text-sm text-indigo-600 hover:text-indigo-800"
          >
            <Plus className="w-4 h-4" />
            <span>Adicionar destinatário</span>
          </button>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Configurações de Email</h4>
          <div className="text-sm text-blue-700 space-y-1">
            <p>• O relatório será anexado no formato selecionado</p>
            <p>• Assunto: [RLPONTO] {config.name}</p>
            <p>• Remetente: <EMAIL></p>
            <p>• Notificações de falha serão enviadas para administradores</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {[1, 2, 3].map(step => (
            <div key={step} className="flex items-center">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${currentStep >= step ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'}
              `}>
                {step}
              </div>
              {step < 3 && (
                <div className={`w-16 h-1 mx-2 ${currentStep > step ? 'bg-indigo-600' : 'bg-gray-200'}`} />
              )}
            </div>
          ))}
        </div>
        <div className="text-sm text-gray-500">
          Passo {currentStep} de 3
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-96">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <button
          onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
          disabled={currentStep === 1}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Anterior
        </button>
        
        <div className="flex items-center space-x-3">
          {currentStep < 3 ? (
            <button
              onClick={() => setCurrentStep(Math.min(3, currentStep + 1))}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Próximo
            </button>
          ) : (
            <button
              onClick={() => {
                console.log('Salvando agendamento:', config);
                alert('Agendamento criado com sucesso!');
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
            >
              Criar Agendamento
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

