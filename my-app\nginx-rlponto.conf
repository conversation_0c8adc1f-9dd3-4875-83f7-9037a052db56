# Configuração Nginx para Sistema RLPONTO
# Proxy Reverso para aplicação Next.js

server {
    listen 80;
    listen [::]:80;
    
    # Domínio do servidor
    server_name rlponto.local ************ localhost;
    
    # Logs específicos do projeto
    access_log /var/log/nginx/rlponto_access.log;
    error_log /var/log/nginx/rlponto_error.log;
    
    # Configurações de segurança
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Configurações de proxy para Next.js
    location / {
        # Proxy para aplicação Next.js (porta 3000)
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Configuração para arquivos estáticos do Next.js
    location /_next/static/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        
        # Cache para arquivos estáticos
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Configuração para imagens otimizadas do Next.js
    location /_next/image {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # Configuração para API routes do Next.js
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts específicos para API
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Configuração para WebSocket (Hot Reload do Next.js)
    location /_next/webpack-hmr {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
    
    # Configuração para arquivos de favicon
    location /favicon.ico {
        proxy_pass http://127.0.0.1:3000;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # Configuração para robots.txt
    location /robots.txt {
        proxy_pass http://127.0.0.1:3000;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # Configuração para sitemap.xml
    location /sitemap.xml {
        proxy_pass http://127.0.0.1:3000;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # Configurações de compressão
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Configurações de upload
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Rate limiting será configurado no nginx.conf principal
}
