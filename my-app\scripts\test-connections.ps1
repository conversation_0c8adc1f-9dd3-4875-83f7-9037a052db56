# PowerShell Script para Testar Conexões - Sistema RLPONTO

param(
    [string]$ServerIP = "************",
    [string]$Username = "root",
    [string]$Password = "@Ric6109"
)

Write-Host "🔍 TESTE DE CONEXÕES - SISTEMA RLPONTO" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue
Write-Host "Servidor: $ServerIP" -ForegroundColor Cyan
Write-Host "Usuário: $Username" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Blue
Write-Host ""

$testResults = @()

# 1. Teste de conectividade de rede
Write-Host "1. Testando conectividade de rede..." -ForegroundColor Yellow
try {
    $ping = Test-Connection -ComputerName $ServerIP -Count 4 -Quiet
    if ($ping) {
        Write-Host "✅ Conectividade de rede: OK" -ForegroundColor Green
        $testResults += "✅ Rede: OK"
    } else {
        Write-Host "❌ Servidor não responde ao ping" -ForegroundColor Red
        $testResults += "❌ Rede: FALHOU"
    }
} catch {
    Write-Host "❌ Erro no teste de ping: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ Rede: ERRO"
}

# 2. Teste de porta SSH (22)
Write-Host "2. Testando porta SSH (22)..." -ForegroundColor Yellow
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $connect = $tcpClient.BeginConnect($ServerIP, 22, $null, $null)
    $wait = $connect.AsyncWaitHandle.WaitOne(5000, $false)
    
    if ($wait) {
        $tcpClient.EndConnect($connect)
        Write-Host "✅ Porta SSH (22): Aberta" -ForegroundColor Green
        $testResults += "✅ SSH Port: OK"
    } else {
        Write-Host "❌ Porta SSH (22) não acessível" -ForegroundColor Red
        $testResults += "❌ SSH Port: FALHOU"
    }
    $tcpClient.Close()
} catch {
    Write-Host "❌ Erro no teste de porta SSH: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "❌ SSH Port: ERRO"
}

# 3. Verificar dependências locais
Write-Host "3. Verificando dependências locais..." -ForegroundColor Yellow

# Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js instalado: $nodeVersion" -ForegroundColor Green
        $testResults += "✅ Node.js: $nodeVersion"
    } else {
        Write-Host "❌ Node.js não encontrado" -ForegroundColor Red
        $testResults += "❌ Node.js: NÃO ENCONTRADO"
    }
} catch {
    Write-Host "❌ Node.js não encontrado" -ForegroundColor Red
    $testResults += "❌ Node.js: NÃO ENCONTRADO"
}

# NPM
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-Host "✅ NPM instalado: $npmVersion" -ForegroundColor Green
        $testResults += "✅ NPM: $npmVersion"
    } else {
        Write-Host "❌ NPM não encontrado" -ForegroundColor Red
        $testResults += "❌ NPM: NÃO ENCONTRADO"
    }
} catch {
    Write-Host "❌ NPM não encontrado" -ForegroundColor Red
    $testResults += "❌ NPM: NÃO ENCONTRADO"
}

# Git
try {
    $gitVersion = git --version 2>$null
    if ($gitVersion) {
        Write-Host "✅ Git instalado: $gitVersion" -ForegroundColor Green
        $testResults += "✅ Git: OK"
    } else {
        Write-Host "❌ Git não encontrado" -ForegroundColor Red
        $testResults += "❌ Git: NÃO ENCONTRADO"
    }
} catch {
    Write-Host "❌ Git não encontrado" -ForegroundColor Red
    $testResults += "❌ Git: NÃO ENCONTRADO"
}

# SSH Client
try {
    $sshTest = ssh -V 2>$null
    Write-Host "✅ SSH client disponível" -ForegroundColor Green
    $testResults += "✅ SSH Client: OK"
} catch {
    Write-Host "⚠️ SSH client pode não estar disponível no PATH" -ForegroundColor Yellow
    $testResults += "⚠️ SSH Client: VERIFICAR"
}

# 4. Verificar estrutura do projeto
Write-Host "4. Verificando estrutura do projeto..." -ForegroundColor Yellow

if (Test-Path "package.json") {
    Write-Host "✅ package.json encontrado" -ForegroundColor Green
    $testResults += "✅ package.json: OK"
} else {
    Write-Host "❌ package.json não encontrado" -ForegroundColor Red
    $testResults += "❌ package.json: NÃO ENCONTRADO"
}

if (Test-Path "scripts") {
    Write-Host "✅ Diretório scripts/ encontrado" -ForegroundColor Green
    $testResults += "✅ scripts/: OK"
} else {
    Write-Host "❌ Diretório scripts/ não encontrado" -ForegroundColor Red
    $testResults += "❌ scripts/: NÃO ENCONTRADO"
}

# Verificar scripts necessários
$requiredScripts = @(
    "scripts/setup-complete.sh",
    "scripts/setup-production.sh",
    "scripts/configure-server.sh",
    "scripts/deploy.sh",
    "scripts/backup-and-monitor.sh"
)

foreach ($script in $requiredScripts) {
    if (Test-Path $script) {
        Write-Host "✅ $script encontrado" -ForegroundColor Green
    } else {
        Write-Host "❌ $script não encontrado" -ForegroundColor Red
        $testResults += "❌ $script: NÃO ENCONTRADO"
    }
}

# 5. Teste de outras portas necessárias
Write-Host "5. Verificando outras portas..." -ForegroundColor Yellow
$ports = @(80, 3000, 3306)

foreach ($port in $ports) {
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $connect = $tcpClient.BeginConnect($ServerIP, $port, $null, $null)
        $wait = $connect.AsyncWaitHandle.WaitOne(2000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($connect)
            Write-Host "⚠️ Porta $port já está em uso (pode ser normal)" -ForegroundColor Yellow
        } else {
            Write-Host "✅ Porta $port está livre" -ForegroundColor Green
        }
        $tcpClient.Close()
    } catch {
        Write-Host "✅ Porta $port está livre" -ForegroundColor Green
    }
}

# Resumo final
Write-Host ""
Write-Host "📋 RESUMO DOS TESTES" -ForegroundColor Blue
Write-Host "===================" -ForegroundColor Blue
foreach ($result in $testResults) {
    Write-Host $result
}

Write-Host ""
Write-Host "🔑 TESTE DE SSH MANUAL" -ForegroundColor Blue
Write-Host "======================" -ForegroundColor Blue
Write-Host "Para testar SSH manualmente, execute:" -ForegroundColor Cyan
Write-Host "ssh $Username@$ServerIP" -ForegroundColor White
Write-Host "Senha: $Password" -ForegroundColor White
Write-Host ""

Write-Host "🚀 PRÓXIMOS PASSOS" -ForegroundColor Blue
Write-Host "==================" -ForegroundColor Blue
Write-Host "1. Se a conectividade está OK, execute no Git Bash:" -ForegroundColor Cyan
Write-Host "   bash scripts/setup-complete.sh" -ForegroundColor White
Write-Host ""
Write-Host "2. Ou configure SSH primeiro:" -ForegroundColor Cyan
Write-Host "   bash scripts/setup-production.sh" -ForegroundColor White
Write-Host ""
Write-Host "3. Para deploy:" -ForegroundColor Cyan
Write-Host "   bash scripts/deploy.sh" -ForegroundColor White
Write-Host ""
Write-Host "4. URL do sistema após deploy:" -ForegroundColor Cyan
Write-Host "   http://$ServerIP" -ForegroundColor White

Write-Host ""
Write-Host "✅ Teste de conexões concluído!" -ForegroundColor Green
