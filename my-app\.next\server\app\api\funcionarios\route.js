(()=>{var a={};a.id=86,a.ids=[86],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{},99903:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>c$,patchFetch:()=>cZ,routeModule:()=>cV,serverHooks:()=>cY,workAsyncStorage:()=>cW,workUnitAsyncStorage:()=>cX});var d={};c.r(d),c.d(d,{GET:()=>cT,POST:()=>cU});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190);function v(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}Object.freeze({status:"aborted"}),Symbol("zod_brand");class w extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let x={};function y(a){return a&&Object.assign(x,a),x}let z=/^[cC][^\s-]{8,}$/,A=/^[0-9a-z]+$/,B=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,C=/^[0-9a-vA-V]{20}$/,D=/^[A-Za-z0-9]{27}$/,E=/^[a-zA-Z0-9_-]{21}$/,F=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,G=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,H=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,I=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,J=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,K=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,L=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,M=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,N=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,O=/^[A-Za-z0-9_-]*$/,P=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,Q=/^\+(?:[0-9]){6,14}[0-9]$/,R="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",S=RegExp(`^${R}$`);function T(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}let U=/^\d+$/,V=/^-?\d+(?:\.\d+)?/i,W=/^[^A-Z]*$/,X=/^[^a-z]*$/;function Y(a,b){return"bigint"==typeof b?b.toString():b}function Z(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function $(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function _(a,b,c){Object.defineProperty(a,b,{get(){{let d=c();return a[b]=d,d}},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function aa(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function ab(...a){let b={};for(let c of a)Object.assign(b,Object.getOwnPropertyDescriptors(c));return Object.defineProperties({},b)}function ac(a){return JSON.stringify(a)}let ad="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function ae(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let af=Z(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function ag(a){if(!1===ae(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==ae(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}let ah=new Set(["string","number","symbol"]);function ai(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function aj(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function ak(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}let al={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function am(a,b=0){for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function an(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function ao(a){return"string"==typeof a?a:a?.message}function ap(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=ao(a.inst?._zod.def?.error?.(a))??ao(b?.error?.(a))??ao(c.customError?.(a))??ao(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function aq(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function ar(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}let as=v("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),at={number:"number",bigint:"bigint",object:"date"},au=v("$ZodCheckLessThan",(a,b)=>{as.init(a,b);let c=at[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.maximum:c.exclusiveMaximum)??1/0;b.value<d&&(b.inclusive?c.maximum=b.value:c.exclusiveMaximum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value<=b.value:d.value<b.value)||d.issues.push({origin:c,code:"too_big",maximum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),av=v("$ZodCheckGreaterThan",(a,b)=>{as.init(a,b);let c=at[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.minimum:c.exclusiveMinimum)??-1/0;b.value>d&&(b.inclusive?c.minimum=b.value:c.exclusiveMinimum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value>=b.value:d.value>b.value)||d.issues.push({origin:c,code:"too_small",minimum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),aw=v("$ZodCheckMultipleOf",(a,b)=>{as.init(a,b),a._zod.onattach.push(a=>{var c;(c=a._zod.bag).multipleOf??(c.multipleOf=b.value)}),a._zod.check=c=>{if(typeof c.value!=typeof b.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof c.value?c.value%b.value===BigInt(0):0===function(a,b){let c=(a.toString().split(".")[1]||"").length,d=(b.toString().split(".")[1]||"").length,e=c>d?c:d;return Number.parseInt(a.toFixed(e).replace(".",""))%Number.parseInt(b.toFixed(e).replace(".",""))/10**e}(c.value,b.value))||c.issues.push({origin:typeof c.value,code:"not_multiple_of",divisor:b.value,input:c.value,inst:a,continue:!b.abort})}}),ax=v("$ZodCheckNumberFormat",(a,b)=>{as.init(a,b),b.format=b.format||"float64";let c=b.format?.includes("int"),d=c?"int":"number",[e,f]=al[b.format];a._zod.onattach.push(a=>{let d=a._zod.bag;d.format=b.format,d.minimum=e,d.maximum=f,c&&(d.pattern=U)}),a._zod.check=g=>{let h=g.value;if(c){if(!Number.isInteger(h))return void g.issues.push({expected:d,format:b.format,code:"invalid_type",input:h,inst:a});if(!Number.isSafeInteger(h))return void(h>0?g.issues.push({input:h,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}):g.issues.push({input:h,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}))}h<e&&g.issues.push({origin:"number",input:h,code:"too_small",minimum:e,inclusive:!0,inst:a,continue:!b.abort}),h>f&&g.issues.push({origin:"number",input:h,code:"too_big",maximum:f,inst:a})}}),ay=v("$ZodCheckMaxLength",(a,b)=>{var c;as.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=aq(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),az=v("$ZodCheckMinLength",(a,b)=>{var c;as.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=aq(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),aA=v("$ZodCheckLengthEquals",(a,b)=>{var c;as.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=aq(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),aB=v("$ZodCheckStringFormat",(a,b)=>{var c,d;as.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),aC=v("$ZodCheckRegex",(a,b)=>{aB.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),aD=v("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=W),aB.init(a,b)}),aE=v("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=X),aB.init(a,b)}),aF=v("$ZodCheckIncludes",(a,b)=>{as.init(a,b);let c=ai(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),aG=v("$ZodCheckStartsWith",(a,b)=>{as.init(a,b);let c=RegExp(`^${ai(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),aH=v("$ZodCheckEndsWith",(a,b)=>{as.init(a,b);let c=RegExp(`.*${ai(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}}),aI=v("$ZodCheckOverwrite",(a,b)=>{as.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});class aJ{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}let aK=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),a.message=JSON.stringify(b,Y,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},aL=v("$ZodError",aK),aM=v("$ZodError",aK,{Parent:Error}),aN=a=>(b,c,d)=>{let e=d?{...d,async:!1}:{async:!1},f=b._zod.run({value:c,issues:[]},e);if(f instanceof Promise)throw new w;return f.issues.length?{success:!1,error:new(a??aL)(f.issues.map(a=>ap(a,e,y())))}:{success:!0,data:f.value}},aO=aN(aM),aP=a=>async(b,c,d)=>{let e=d?Object.assign(d,{async:!0}):{async:!0},f=b._zod.run({value:c,issues:[]},e);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new a(f.issues.map(a=>ap(a,e,y())))}:{success:!0,data:f.value}},aQ=aP(aM),aR={major:4,minor:0,patch:10},aS=v("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=aR;let d=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&d.unshift(a),d))for(let c of b._zod.onattach)c(a);if(0===d.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let d,e=am(a);for(let f of b){if(f._zod.def.when){if(!f._zod.def.when(a))continue}else if(e)continue;let b=a.issues.length,g=f._zod.check(a);if(g instanceof Promise&&c?.async===!1)throw new w;if(d||g instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await g,a.issues.length!==b&&(e||(e=am(a,b)))});else{if(a.issues.length===b)continue;e||(e=am(a,b))}}return d?d.then(()=>a):a};a._zod.run=(c,e)=>{let f=a._zod.parse(c,e);if(f instanceof Promise){if(!1===e.async)throw new w;return f.then(a=>b(a,d,e))}return b(f,d,e)}}a["~standard"]={validate:b=>{try{let c=aO(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return aQ(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),aT=v("$ZodString",(a,b)=>{aS.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??(a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)})(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),aU=v("$ZodStringFormat",(a,b)=>{aB.init(a,b),aT.init(a,b)}),aV=v("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=G),aU.init(a,b)}),aW=v("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=H(a))}else b.pattern??(b.pattern=H());aU.init(a,b)}),aX=v("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=I),aU.init(a,b)}),aY=v("$ZodURL",(a,b)=>{aU.init(a,b),a._zod.check=c=>{try{let d=c.value.trim(),e=new URL(d);b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:P.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),b.normalize?c.value=e.href:c.value=d;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),aZ=v("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),aU.init(a,b)}),a$=v("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=E),aU.init(a,b)}),a_=v("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=z),aU.init(a,b)}),a0=v("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=A),aU.init(a,b)}),a1=v("$ZodULID",(a,b)=>{b.pattern??(b.pattern=B),aU.init(a,b)}),a2=v("$ZodXID",(a,b)=>{b.pattern??(b.pattern=C),aU.init(a,b)}),a3=v("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=D),aU.init(a,b)}),a4=v("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=function(a){let b=T({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-]\\d{2}:\\d{2})");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${R}T(?:${d})$`)}(b)),aU.init(a,b)}),a5=v("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=S),aU.init(a,b)}),a6=v("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=RegExp(`^${T(b)}$`)),aU.init(a,b)}),a7=v("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=F),aU.init(a,b)}),a8=v("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=J),aU.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),a9=v("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=K),aU.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),ba=v("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=L),aU.init(a,b)}),bb=v("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=M),aU.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function bc(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let bd=v("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=N),aU.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{bc(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}}),be=v("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=O),aU.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{!function(a){if(!O.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return bc(b.padEnd(4*Math.ceil(b.length/4),"="))}(c.value)&&c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),bf=v("$ZodE164",(a,b)=>{b.pattern??(b.pattern=Q),aU.init(a,b)}),bg=v("$ZodJWT",(a,b)=>{aU.init(a,b),a._zod.check=c=>{!function(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}(c.value,b.alg)&&c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),bh=v("$ZodNumber",(a,b)=>{aS.init(a,b),a._zod.pattern=a._zod.bag.pattern??V,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=Number(c.value)}catch(a){}let e=c.value;if("number"==typeof e&&!Number.isNaN(e)&&Number.isFinite(e))return c;let f="number"==typeof e?Number.isNaN(e)?"NaN":Number.isFinite(e)?void 0:"Infinity":void 0;return c.issues.push({expected:"number",code:"invalid_type",input:e,inst:a,...f?{received:f}:{}}),c}}),bi=v("$ZodNumber",(a,b)=>{ax.init(a,b),bh.init(a,b)}),bj=v("$ZodUnknown",(a,b)=>{aS.init(a,b),a._zod.parse=a=>a}),bk=v("$ZodNever",(a,b)=>{aS.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)});function bl(a,b,c){a.issues.length&&b.issues.push(...an(c,a.issues)),b.value[c]=a.value}let bm=v("$ZodArray",(a,b)=>{aS.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>bl(b,c,a))):bl(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function bn(a,b,c,d){a.issues.length&&b.issues.push(...an(c,a.issues)),void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}let bo=v("$ZodObject",(a,b)=>{let c,d;aS.init(a,b);let e=Z(()=>{let a=Object.keys(b.shape);for(let c of a)if(!(b.shape[c]instanceof aS))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=function(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}(b.shape);return{shape:b.shape,keys:a,keySet:new Set(a),numKeys:a.length,optionalKeys:new Set(c)}});_(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let f=!x.jitless,g=f&&af.value,h=b.catchall;a._zod.parse=(i,j)=>{d??(d=e.value);let k=i.value;if(!ae(k))return i.issues.push({expected:"object",code:"invalid_type",input:k,inst:a}),i;let l=[];if(f&&g&&j?.async===!1&&!0!==j.jitless)c||(c=(a=>{let b=new aJ(["shape","payload","ctx"]),c=e.value,d=a=>{let b=ac(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let f=Object.create(null),g=0;for(let a of c.keys)f[a]=`key_${g++}`;for(let a of(b.write("const newResult = {}"),c.keys)){let c=f[a],e=ac(a);b.write(`const ${c} = ${d(a)};`),b.write(`
        if (${c}.issues.length) {
          payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${e}, ...iss.path] : [${e}]
          })));
        }
        
        if (${c}.value === undefined) {
          if (${e} in input) {
            newResult[${e}] = undefined;
          }
        } else {
          newResult[${e}] = ${c}.value;
        }
      `)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),i=c(i,j);else{i.value={};let a=d.shape;for(let b of d.keys){let c=a[b]._zod.run({value:k[b],issues:[]},j);c instanceof Promise?l.push(c.then(a=>bn(a,i,b,k))):bn(c,i,b,k)}}if(!h)return l.length?Promise.all(l).then(()=>i):i;let m=[],n=d.keySet,o=h._zod,p=o.def.type;for(let a of Object.keys(k)){if(n.has(a))continue;if("never"===p){m.push(a);continue}let b=o.run({value:k[a],issues:[]},j);b instanceof Promise?l.push(b.then(b=>bn(b,i,a,k))):bn(b,i,a,k)}return(m.length&&i.issues.push({code:"unrecognized_keys",keys:m,input:k,inst:a}),l.length)?Promise.all(l).then(()=>i):i}});function bp(a,b,c,d){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;let e=a.filter(a=>!am(a));return 1===e.length?(b.value=e[0].value,e[0]):(b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>ap(a,d,y())))}),b)}let bq=v("$ZodUnion",(a,b)=>{aS.init(a,b),_(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),_(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),_(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),_(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>$(a.source)).join("|")})$`)}}),a._zod.parse=(c,d)=>{let e=!1,f=[];for(let a of b.options){let b=a._zod.run({value:c.value,issues:[]},d);if(b instanceof Promise)f.push(b),e=!0;else{if(0===b.issues.length)return b;f.push(b)}}return e?Promise.all(f).then(b=>bp(b,c,a,d)):bp(f,c,a,d)}}),br=v("$ZodIntersection",(a,b)=>{aS.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>bs(a,b,c)):bs(a,e,f)}});function bs(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),am(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(ag(b)&&ag(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let bt=v("$ZodEnum",(a,b)=>{aS.init(a,b);let c=function(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}(b.entries),d=new Set(c);a._zod.values=d,a._zod.pattern=RegExp(`^(${c.filter(a=>ah.has(typeof a)).map(a=>"string"==typeof a?ai(a):a.toString()).join("|")})$`),a._zod.parse=(b,e)=>{let f=b.value;return d.has(f)||b.issues.push({code:"invalid_value",values:c,input:f,inst:a}),b}}),bu=v("$ZodTransform",(a,b)=>{aS.init(a,b),a._zod.parse=(a,c)=>{let d=b.transform(a.value,a);if(c.async)return(d instanceof Promise?d:Promise.resolve(d)).then(b=>(a.value=b,a));if(d instanceof Promise)throw new w;return a.value=d,a}}),bv=v("$ZodOptional",(a,b)=>{aS.init(a,b),a._zod.optin="optional",a._zod.optout="optional",_(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),_(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${$(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>"optional"===b.innerType._zod.optin?b.innerType._zod.run(a,c):void 0===a.value?a:b.innerType._zod.run(a,c)}),bw=v("$ZodNullable",(a,b)=>{aS.init(a,b),_(a._zod,"optin",()=>b.innerType._zod.optin),_(a._zod,"optout",()=>b.innerType._zod.optout),_(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${$(a.source)}|null)$`):void 0}),_(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),bx=v("$ZodDefault",(a,b)=>{aS.init(a,b),a._zod.optin="optional",_(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>by(a,b)):by(d,b)}});function by(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let bz=v("$ZodPrefault",(a,b)=>{aS.init(a,b),a._zod.optin="optional",_(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>(void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),bA=v("$ZodNonOptional",(a,b)=>{aS.init(a,b),_(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>bB(b,a)):bB(e,a)}});function bB(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let bC=v("$ZodCatch",(a,b)=>{aS.init(a,b),_(a._zod,"optin",()=>b.innerType._zod.optin),_(a._zod,"optout",()=>b.innerType._zod.optout),_(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(d=>(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>ap(a,c,y()))},input:a.value}),a.issues=[]),a)):(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>ap(a,c,y()))},input:a.value}),a.issues=[]),a)}}),bD=v("$ZodPipe",(a,b)=>{aS.init(a,b),_(a._zod,"values",()=>b.in._zod.values),_(a._zod,"optin",()=>b.in._zod.optin),_(a._zod,"optout",()=>b.out._zod.optout),_(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>bE(a,b,c)):bE(d,b,c)}});function bE(a,b,c){return a.issues.length?a:b.out._zod.run({value:a.value,issues:a.issues},c)}let bF=v("$ZodReadonly",(a,b)=>{aS.init(a,b),_(a._zod,"propValues",()=>b.innerType._zod.propValues),_(a._zod,"values",()=>b.innerType._zod.values),_(a._zod,"optin",()=>b.innerType._zod.optin),_(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(bG):bG(d)}});function bG(a){return a.value=Object.freeze(a.value),a}let bH=v("$ZodCustom",(a,b)=>{as.init(a,b),aS.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>bI(b,c,d,a));bI(e,c,d,a)}});function bI(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(ar(a))}}Symbol("ZodOutput"),Symbol("ZodInput");class bJ{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};delete c.id;let d={...c,...this._map.get(a)};return Object.keys(d).length?d:void 0}return this._map.get(a)}has(a){return this._map.has(a)}}let bK=new bJ;function bL(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...ak(b)})}function bM(a,b){return new au({check:"less_than",...ak(b),value:a,inclusive:!1})}function bN(a,b){return new au({check:"less_than",...ak(b),value:a,inclusive:!0})}function bO(a,b){return new av({check:"greater_than",...ak(b),value:a,inclusive:!1})}function bP(a,b){return new av({check:"greater_than",...ak(b),value:a,inclusive:!0})}function bQ(a,b){return new aw({check:"multiple_of",...ak(b),value:a})}function bR(a,b){return new ay({check:"max_length",...ak(b),maximum:a})}function bS(a,b){return new az({check:"min_length",...ak(b),minimum:a})}function bT(a,b){return new aA({check:"length_equals",...ak(b),length:a})}function bU(a){return new aI({check:"overwrite",tx:a})}let bV=v("ZodISODateTime",(a,b)=>{a4.init(a,b),b7.init(a,b)}),bW=v("ZodISODate",(a,b)=>{a5.init(a,b),b7.init(a,b)}),bX=v("ZodISOTime",(a,b)=>{a6.init(a,b),b7.init(a,b)}),bY=v("ZodISODuration",(a,b)=>{a7.init(a,b),b7.init(a,b)}),bZ=(a,b)=>{aL.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>(function(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d})(a,b)},flatten:{value:b=>(function(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}})(a,b)},addIssue:{value:b=>{a.issues.push(b),a.message=JSON.stringify(a.issues,Y,2)}},addIssues:{value:b=>{a.issues.push(...b),a.message=JSON.stringify(a.issues,Y,2)}},isEmpty:{get:()=>0===a.issues.length}})};v("ZodError",bZ);let b$=v("ZodError",bZ,{Parent:Error}),b_=(a,b,c,d)=>{let e=c?Object.assign(c,{async:!1}):{async:!1},f=a._zod.run({value:b,issues:[]},e);if(f instanceof Promise)throw new w;if(f.issues.length){let a=new(d?.Err??b$)(f.issues.map(a=>ap(a,e,y())));throw ad(a,d?.callee),a}return f.value},b0=async(a,b,c,d)=>{let e=c?Object.assign(c,{async:!0}):{async:!0},f=a._zod.run({value:b,issues:[]},e);if(f instanceof Promise&&(f=await f),f.issues.length){let a=new(d?.Err??b$)(f.issues.map(a=>ap(a,e,y())));throw ad(a,d?.callee),a}return f.value},b1=aN(b$),b2=aP(b$),b3=v("ZodType",(a,b)=>(aS.init(a,b),a.def=b,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>aj(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>b_(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>b1(a,b,c),a.parseAsync=async(b,c)=>b0(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>b2(a,b,c),a.spa=a.safeParseAsync,a.refine=(b,c)=>a.check(function(a,b={}){return new cQ({type:"custom",check:"custom",fn:a,...ak(b)})}(b,c)),a.superRefine=b=>a.check(function(a){let b=function(a){let b=new as({check:"custom"});return b._zod.check=a,b}(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(ar(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(ar(a)))},a(c.value,c)));return b}(b)),a.overwrite=b=>a.check(bU(b)),a.optional=()=>cG(a),a.nullable=()=>cI(a),a.nullish=()=>cG(cI(a)),a.nonoptional=b=>new cL({type:"nonoptional",innerType:a,...ak(b)}),a.array=()=>(function(a,b){return new cy({type:"array",element:a,...ak(void 0)})})(a),a.or=b=>new cB({type:"union",options:[a,b],...ak(void 0)}),a.and=b=>new cC({type:"intersection",left:a,right:b}),a.transform=b=>cO(a,new cE({type:"transform",transform:b})),a.default=b=>(function(a,b){return new cJ({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.prefault=b=>(function(a,b){return new cK({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.catch=b=>(function(a,b){return new cM({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})})(a,b),a.pipe=b=>cO(a,b),a.readonly=()=>new cP({type:"readonly",innerType:a}),a.describe=b=>{let c=a.clone();return bK.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>bK.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return bK.get(a);let c=a.clone();return bK.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),b4=v("_ZodString",(a,b)=>{aT.init(a,b),b3.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(function(a,b){return new aC({check:"string_format",format:"regex",...ak(b),pattern:a})}(...b)),a.includes=(...b)=>a.check(function(a,b){return new aF({check:"string_format",format:"includes",...ak(b),includes:a})}(...b)),a.startsWith=(...b)=>a.check(function(a,b){return new aG({check:"string_format",format:"starts_with",...ak(b),prefix:a})}(...b)),a.endsWith=(...b)=>a.check(function(a,b){return new aH({check:"string_format",format:"ends_with",...ak(b),suffix:a})}(...b)),a.min=(...b)=>a.check(bS(...b)),a.max=(...b)=>a.check(bR(...b)),a.length=(...b)=>a.check(bT(...b)),a.nonempty=(...b)=>a.check(bS(1,...b)),a.lowercase=b=>a.check(new aD({check:"string_format",format:"lowercase",...ak(b)})),a.uppercase=b=>a.check(new aE({check:"string_format",format:"uppercase",...ak(b)})),a.trim=()=>a.check(bU(a=>a.trim())),a.normalize=(...b)=>a.check(function(a){return bU(b=>b.normalize(a))}(...b)),a.toLowerCase=()=>a.check(bU(a=>a.toLowerCase())),a.toUpperCase=()=>a.check(bU(a=>a.toUpperCase()))}),b5=v("ZodString",(a,b)=>{aT.init(a,b),b4.init(a,b),a.email=b=>a.check(new b8({type:"string",format:"email",check:"string_format",abort:!1,...ak(b)})),a.url=b=>a.check(new cb({type:"string",format:"url",check:"string_format",abort:!1,...ak(b)})),a.jwt=b=>a.check(new cq({type:"string",format:"jwt",check:"string_format",abort:!1,...ak(b)})),a.emoji=b=>a.check(new cc({type:"string",format:"emoji",check:"string_format",abort:!1,...ak(b)})),a.guid=b=>a.check(bL(b9,b)),a.uuid=b=>a.check(new ca({type:"string",format:"uuid",check:"string_format",abort:!1,...ak(b)})),a.uuidv4=b=>a.check(new ca({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...ak(b)})),a.uuidv6=b=>a.check(new ca({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...ak(b)})),a.uuidv7=b=>a.check(new ca({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...ak(b)})),a.nanoid=b=>a.check(new cd({type:"string",format:"nanoid",check:"string_format",abort:!1,...ak(b)})),a.guid=b=>a.check(bL(b9,b)),a.cuid=b=>a.check(new ce({type:"string",format:"cuid",check:"string_format",abort:!1,...ak(b)})),a.cuid2=b=>a.check(new cf({type:"string",format:"cuid2",check:"string_format",abort:!1,...ak(b)})),a.ulid=b=>a.check(new cg({type:"string",format:"ulid",check:"string_format",abort:!1,...ak(b)})),a.base64=b=>a.check(new cn({type:"string",format:"base64",check:"string_format",abort:!1,...ak(b)})),a.base64url=b=>a.check(new co({type:"string",format:"base64url",check:"string_format",abort:!1,...ak(b)})),a.xid=b=>a.check(new ch({type:"string",format:"xid",check:"string_format",abort:!1,...ak(b)})),a.ksuid=b=>a.check(new ci({type:"string",format:"ksuid",check:"string_format",abort:!1,...ak(b)})),a.ipv4=b=>a.check(new cj({type:"string",format:"ipv4",check:"string_format",abort:!1,...ak(b)})),a.ipv6=b=>a.check(new ck({type:"string",format:"ipv6",check:"string_format",abort:!1,...ak(b)})),a.cidrv4=b=>a.check(new cl({type:"string",format:"cidrv4",check:"string_format",abort:!1,...ak(b)})),a.cidrv6=b=>a.check(new cm({type:"string",format:"cidrv6",check:"string_format",abort:!1,...ak(b)})),a.e164=b=>a.check(new cp({type:"string",format:"e164",check:"string_format",abort:!1,...ak(b)})),a.datetime=b=>a.check(new bV({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...ak(b)})),a.date=b=>a.check(new bW({type:"string",format:"date",check:"string_format",...ak(b)})),a.time=b=>a.check(new bX({type:"string",format:"time",check:"string_format",precision:null,...ak(b)})),a.duration=b=>a.check(new bY({type:"string",format:"duration",check:"string_format",...ak(b)}))});function b6(a){return new b5({type:"string",...ak(a)})}let b7=v("ZodStringFormat",(a,b)=>{aU.init(a,b),b4.init(a,b)}),b8=v("ZodEmail",(a,b)=>{aX.init(a,b),b7.init(a,b)}),b9=v("ZodGUID",(a,b)=>{aV.init(a,b),b7.init(a,b)}),ca=v("ZodUUID",(a,b)=>{aW.init(a,b),b7.init(a,b)}),cb=v("ZodURL",(a,b)=>{aY.init(a,b),b7.init(a,b)}),cc=v("ZodEmoji",(a,b)=>{aZ.init(a,b),b7.init(a,b)}),cd=v("ZodNanoID",(a,b)=>{a$.init(a,b),b7.init(a,b)}),ce=v("ZodCUID",(a,b)=>{a_.init(a,b),b7.init(a,b)}),cf=v("ZodCUID2",(a,b)=>{a0.init(a,b),b7.init(a,b)}),cg=v("ZodULID",(a,b)=>{a1.init(a,b),b7.init(a,b)}),ch=v("ZodXID",(a,b)=>{a2.init(a,b),b7.init(a,b)}),ci=v("ZodKSUID",(a,b)=>{a3.init(a,b),b7.init(a,b)}),cj=v("ZodIPv4",(a,b)=>{a8.init(a,b),b7.init(a,b)}),ck=v("ZodIPv6",(a,b)=>{a9.init(a,b),b7.init(a,b)}),cl=v("ZodCIDRv4",(a,b)=>{ba.init(a,b),b7.init(a,b)}),cm=v("ZodCIDRv6",(a,b)=>{bb.init(a,b),b7.init(a,b)}),cn=v("ZodBase64",(a,b)=>{bd.init(a,b),b7.init(a,b)}),co=v("ZodBase64URL",(a,b)=>{be.init(a,b),b7.init(a,b)}),cp=v("ZodE164",(a,b)=>{bf.init(a,b),b7.init(a,b)}),cq=v("ZodJWT",(a,b)=>{bg.init(a,b),b7.init(a,b)}),cr=v("ZodNumber",(a,b)=>{bh.init(a,b),b3.init(a,b),a.gt=(b,c)=>a.check(bO(b,c)),a.gte=(b,c)=>a.check(bP(b,c)),a.min=(b,c)=>a.check(bP(b,c)),a.lt=(b,c)=>a.check(bM(b,c)),a.lte=(b,c)=>a.check(bN(b,c)),a.max=(b,c)=>a.check(bN(b,c)),a.int=b=>a.check(cu(b)),a.safe=b=>a.check(cu(b)),a.positive=b=>a.check(bO(0,b)),a.nonnegative=b=>a.check(bP(0,b)),a.negative=b=>a.check(bM(0,b)),a.nonpositive=b=>a.check(bN(0,b)),a.multipleOf=(b,c)=>a.check(bQ(b,c)),a.step=(b,c)=>a.check(bQ(b,c)),a.finite=()=>a;let c=a._zod.bag;a.minValue=Math.max(c.minimum??-1/0,c.exclusiveMinimum??-1/0)??null,a.maxValue=Math.min(c.maximum??1/0,c.exclusiveMaximum??1/0)??null,a.isInt=(c.format??"").includes("int")||Number.isSafeInteger(c.multipleOf??.5),a.isFinite=!0,a.format=c.format??null});function cs(a){return new cr({type:"number",checks:[],...ak(a)})}let ct=v("ZodNumberFormat",(a,b)=>{bi.init(a,b),cr.init(a,b)});function cu(a){return new ct({type:"number",check:"number_format",abort:!1,format:"safeint",...ak(a)})}let cv=v("ZodUnknown",(a,b)=>{bj.init(a,b),b3.init(a,b)});function cw(){return new cv({type:"unknown"})}let cx=v("ZodNever",(a,b)=>{bk.init(a,b),b3.init(a,b)}),cy=v("ZodArray",(a,b)=>{bm.init(a,b),b3.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(bS(b,c)),a.nonempty=b=>a.check(bS(1,b)),a.max=(b,c)=>a.check(bR(b,c)),a.length=(b,c)=>a.check(bT(b,c)),a.unwrap=()=>a.element}),cz=v("ZodObject",(a,b)=>{bo.init(a,b),b3.init(a,b),_(a,"shape",()=>b.shape),a.keyof=()=>(function(a,b){return new cD({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...ak(void 0)})})(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:cw()}),a.loose=()=>a.clone({...a._zod.def,catchall:cw()}),a.strict=()=>a.clone({...a._zod.def,catchall:new cx({type:"never",...ak(void 0)})}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>(function(a,b){if(!ag(b))throw Error("Invalid input to extend: expected a plain object");let c=ab(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b};return aa(this,"shape",c),c},checks:[]});return aj(a,c)})(a,b),a.merge=b=>(function(a,b){let c=ab(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return aa(this,"shape",c),c},get catchall(){return b._zod.def.catchall},checks:[]});return aj(a,c)})(a,b),a.pick=b=>(function(a,b){let c=a._zod.def,d=ab(a._zod.def,{get shape(){let a={};for(let d in b){if(!(d in c.shape))throw Error(`Unrecognized key: "${d}"`);b[d]&&(a[d]=c.shape[d])}return aa(this,"shape",a),a},checks:[]});return aj(a,d)})(a,b),a.omit=b=>(function(a,b){let c=a._zod.def,d=ab(a._zod.def,{get shape(){let d={...a._zod.def.shape};for(let a in b){if(!(a in c.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete d[a]}return aa(this,"shape",d),d},checks:[]});return aj(a,d)})(a,b),a.partial=(...b)=>(function(a,b,c){let d=ab(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return aa(this,"shape",e),e},checks:[]});return aj(b,d)})(cF,a,b[0]),a.required=(...b)=>(function(a,b,c){let d=ab(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return aa(this,"shape",e),e},checks:[]});return aj(b,d)})(cL,a,b[0])});function cA(a,b){return new cz({type:"object",get shape(){return aa(this,"shape",{...a}),this.shape},...ak(b)})}let cB=v("ZodUnion",(a,b)=>{bq.init(a,b),b3.init(a,b),a.options=b.options}),cC=v("ZodIntersection",(a,b)=>{br.init(a,b),b3.init(a,b)}),cD=v("ZodEnum",(a,b)=>{bt.init(a,b),b3.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new cD({...b,checks:[],...ak(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new cD({...b,checks:[],...ak(d),entries:e})}}),cE=v("ZodTransform",(a,b)=>{bu.init(a,b),b3.init(a,b),a._zod.parse=(c,d)=>{c.addIssue=d=>{"string"==typeof d?c.issues.push(ar(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),c.issues.push(ar(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}}),cF=v("ZodOptional",(a,b)=>{bv.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType});function cG(a){return new cF({type:"optional",innerType:a})}let cH=v("ZodNullable",(a,b)=>{bw.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType});function cI(a){return new cH({type:"nullable",innerType:a})}let cJ=v("ZodDefault",(a,b)=>{bx.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap}),cK=v("ZodPrefault",(a,b)=>{bz.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType}),cL=v("ZodNonOptional",(a,b)=>{bA.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType}),cM=v("ZodCatch",(a,b)=>{bC.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap}),cN=v("ZodPipe",(a,b)=>{bD.init(a,b),b3.init(a,b),a.in=b.in,a.out=b.out});function cO(a,b){return new cN({type:"pipe",in:a,out:b})}let cP=v("ZodReadonly",(a,b)=>{bF.init(a,b),b3.init(a,b),a.unwrap=()=>a._zod.def.innerType}),cQ=v("ZodCustom",(a,b)=>{bH.init(a,b),b3.init(a,b)}),cR=cA({nomeCompleto:b6().min(2,"Nome deve ter pelo menos 2 caracteres"),cpf:b6().regex(/^\d{11}$/,"CPF deve ter 11 d\xedgitos"),rg:b6().optional(),email:b6().email("Email inv\xe1lido").optional(),telefone:b6().optional(),celular:b6().optional(),endereco:cA({cep:b6().optional(),logradouro:b6().optional(),numero:b6().optional(),complemento:b6().optional(),bairro:b6().optional(),cidade:b6().optional(),uf:b6().optional()}).optional(),dadosProfissionais:cA({matricula:b6().min(1,"Matr\xedcula \xe9 obrigat\xf3ria"),cargo:b6().min(1,"Cargo \xe9 obrigat\xf3rio"),setor:b6().min(1,"Setor \xe9 obrigat\xf3rio"),dataAdmissao:b6().transform(a=>new Date(a)),salario:cs().optional(),cargaHoraria:cs().min(1,"Carga hor\xe1ria \xe9 obrigat\xf3ria"),horarioTrabalho:cA({entrada:b6(),saida:b6(),intervaloInicio:b6().optional(),intervaloFim:b6().optional()})}),observacoes:b6().optional()}),cS=[{id:"1",nomeCompleto:"Jo\xe3o Silva Santos",cpf:"12345678901",rg:"123456789",email:"<EMAIL>",telefone:"1133334444",celular:"11999887766",endereco:{cep:"01234567",logradouro:"Rua das Flores",numero:"123",bairro:"Centro",cidade:"S\xe3o Paulo",uf:"SP"},dadosProfissionais:{matricula:"EMP001",cargo:"Analista de Sistemas",setor:"Tecnologia",dataAdmissao:new Date("2023-01-15"),salario:5500,cargaHoraria:40,horarioTrabalho:{entrada:"08:00",saida:"17:00",intervaloInicio:"12:00",intervaloFim:"13:00"}},foto:"/avatars/joao.jpg",biometria:{cadastrada:!0,dataUltimoCadastro:new Date("2023-01-16"),templates:10},status:"ativo",observacoes:"Funcion\xe1rio exemplar",createdAt:new Date("2023-01-15"),updatedAt:new Date("2023-01-15")},{id:"2",nomeCompleto:"Maria Oliveira Costa",cpf:"98765432109",rg:"987654321",email:"<EMAIL>",telefone:"1144445555",celular:"11888776655",endereco:{cep:"09876543",logradouro:"Av. Principal",numero:"456",bairro:"Jardim",cidade:"S\xe3o Paulo",uf:"SP"},dadosProfissionais:{matricula:"EMP002",cargo:"Gerente de Vendas",setor:"Vendas",dataAdmissao:new Date("2022-06-10"),salario:7500,cargaHoraria:44,horarioTrabalho:{entrada:"07:30",saida:"16:30",intervaloInicio:"11:30",intervaloFim:"12:30"}},foto:"/avatars/maria.jpg",biometria:{cadastrada:!0,dataUltimoCadastro:new Date("2022-06-11"),templates:8},status:"ativo",observacoes:"L\xedder de equipe",createdAt:new Date("2022-06-10"),updatedAt:new Date("2022-06-10")},{id:"3",nomeCompleto:"Carlos Roberto Lima",cpf:"11122233344",rg:"111222333",email:"<EMAIL>",telefone:"1155556666",celular:"11777665544",endereco:{cep:"12345678",logradouro:"Rua do Com\xe9rcio",numero:"789",bairro:"Vila Nova",cidade:"S\xe3o Paulo",uf:"SP"},dadosProfissionais:{matricula:"EMP003",cargo:"Operador de Produ\xe7\xe3o",setor:"Produ\xe7\xe3o",dataAdmissao:new Date("2021-03-20"),salario:3200,cargaHoraria:40,horarioTrabalho:{entrada:"06:00",saida:"15:00",intervaloInicio:"10:00",intervaloFim:"10:15"}},foto:"/avatars/carlos.jpg",biometria:{cadastrada:!1,templates:0},status:"ativo",observacoes:"Funcion\xe1rio dedicado",createdAt:new Date("2021-03-20"),updatedAt:new Date("2021-03-20")}];async function cT(a){try{let{searchParams:b}=new URL(a.url),c=b.get("search"),d=b.get("setor"),e=b.get("status"),f=parseInt(b.get("page")||"1"),g=parseInt(b.get("limit")||"10"),h=[...cS];if(c){let a=c.toLowerCase();h=h.filter(b=>b.nomeCompleto.toLowerCase().includes(a)||b.cpf.includes(c)||b.dadosProfissionais.matricula.toLowerCase().includes(a))}d&&(h=h.filter(a=>a.dadosProfissionais.setor.toLowerCase()===d.toLowerCase())),e&&(h=h.filter(a=>a.status===e));let i=(f-1)*g,j=h.slice(i,i+g);return u.NextResponse.json({success:!0,funcionarios:j,pagination:{page:f,limit:g,total:h.length,totalPages:Math.ceil(h.length/g)}})}catch(a){return console.error("Erro ao buscar funcion\xe1rios:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function cU(a){try{let b=await a.json(),c=cR.safeParse(b);if(!c.success)return u.NextResponse.json({success:!1,error:"Dados inv\xe1lidos",details:c.error.issues},{status:400});let d={id:Date.now().toString(),...c.data,foto:void 0,biometria:{cadastrada:!1,templates:0},status:"ativo",createdAt:new Date,updatedAt:new Date};return cS.push(d),u.NextResponse.json({success:!0,funcionario:d,message:"Funcion\xe1rio cadastrado com sucesso"})}catch(a){return console.error("Erro ao criar funcion\xe1rio:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}let cV=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/funcionarios/route",pathname:"/api/funcionarios",filename:"route",bundlePath:"app/api/funcionarios/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\funcionarios\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:cW,workUnitAsyncStorage:cX,serverHooks:cY}=cV;function cZ(){return(0,g.patchFetch)({workAsyncStorage:cW,workUnitAsyncStorage:cX})}async function c$(a,b,c){var d;let e="/api/funcionarios/route";"/index"===e&&(e="/");let g=await cV.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||cV.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===cV.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,i.getTracer)(),K=J.getActiveScopeSpan(),L={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>cV.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},M=new k.NodeNextRequest(a),N=new k.NodeNextResponse(b),O=l.NextRequestAdapter.fromNodeNextRequest(M,(0,l.signalFromNodeResponse)(b));try{let d=async c=>cV.handle(O,L).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=L.renderOpts.fetchMetrics;let i=L.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=L.renderOpts.collectedTags;if(!E)return await (0,o.I)(M,N,e,L.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==L.renderOpts.collectedRevalidate&&!(L.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&L.renderOpts.collectedRevalidate,d=void 0===L.renderOpts.collectedExpire||L.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:L.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await cV.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},l=await cV.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(M,N,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};K?await g(K):await J.withPropagatedContext(a.headers,()=>J.trace(m.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},g))}catch(b){if(K||b instanceof s.NoFallbackError||await cV.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(M,N,new Response(null,{status:500})),null}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,55],()=>b(b.s=99903));module.exports=c})();