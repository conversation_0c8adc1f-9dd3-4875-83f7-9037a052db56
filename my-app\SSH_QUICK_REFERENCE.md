# 🚀 SSH Quick Reference - Sistema RLPONTO

## ⚡ Comandos Rápidos

### **Conectar ao Servidor:**
```bash
ssh rlponto-prod                    # <PERSON><PERSON><PERSON><PERSON> recomendado (alias)
ssh root@************              # <PERSON><PERSON><PERSON><PERSON> direto (IP)
```

### **Comandos <PERSON>:**
```bash
# Status do servidor
ssh rlponto-prod "whoami && hostname && uptime"

# Espaço em disco
ssh rlponto-prod "df -h /"

# Memória
ssh rlponto-prod "free -h"

# Processos da aplicação
ssh rlponto-prod "pm2 status"

# Logs da aplicação
ssh rlponto-prod "pm2 logs rlponto --lines 20"
```

### **Transferir Arquivos:**
```bash
# Local → Servidor
scp arquivo.txt rlponto-prod:/tmp/

# Servidor → Local
scp rlponto-prod:/tmp/arquivo.txt ./
```

### **Scripts de Deploy:**
```bash
bash scripts/deploy.sh             # Deploy da aplicação
bash scripts/setup-complete.sh     # Setup completo do servidor
```

## 🔧 Configuração Atual

### **Status:** ✅ **FUNCIONANDO PERFEITAMENTE**
- **Servidor**: ************ (Ubuntu 22.04)
- **Usuário**: root
- **Chave**: ed25519 (rl-ponto-next)
- **Alias**: rlponto-prod
- **Autenticação**: Sem senha

### **Arquivos Importantes:**
```
C:\Users\<USER>\.ssh\
├── rl-ponto-next          # Chave privada
├── rl-ponto-next.pub      # Chave pública
└── config                 # Configuração SSH
```

## 🚨 Troubleshooting Rápido

### **SSH pede senha:**
```bash
# Verificar chave
ls -la "C:\Users\<USER>\.ssh\rl-ponto-next*"

# Testar com chave específica
ssh -i "C:\Users\<USER>\.ssh\rl-ponto-next" root@************
```

### **Alias não funciona:**
```bash
# Verificar configuração
grep "rlponto-prod" "C:\Users\<USER>\.ssh\config"
```

### **Recriar configuração (se necessário):**
```bash
bash scripts/setup-production.sh
```

## 📞 Emergência

### **Acesso com senha (se SSH falhar):**
```bash
ssh root@************
# Senha: @Ric6109
```

### **Verificar conectividade:**
```bash
ping ************
Test-NetConnection -ComputerName ************ -Port 22
```

---

## 🎯 Status: ✅ SSH CONFIGURADO E FUNCIONANDO

**Última verificação**: 26/07/2025  
**Próximo passo**: `bash scripts/deploy.sh` 🚀
