import { Metadata } from 'next';
import { Suspense } from 'react';
import { ManualForm } from '@/components/ponto/manual-form';
import { Hand, AlertTriangle, Clock, ArrowLeft, Info } from 'lucide-react';
import { But<PERSON> } from '@/components/ui';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Ponto Manual - RLPONTO',
  description: 'Registro manual de ponto',
};

export default function PontoManualPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/ponto/biometrico">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-600 rounded-lg">
                  <Hand className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Ponto Manual</h1>
                  <p className="text-gray-600">Registre o ponto manualmente quando necessário</p>
                </div>
              </div>
            </div>
          </div>

          {/* Avisos Importantes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">Atenção</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    O registro manual requer justificativa e pode necessitar aprovação do supervisor.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-blue-800">Quando usar</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    Use apenas quando a biometria não estiver disponível ou em situações excepcionais.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Formulário Principal */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-orange-600" />
                <h2 className="text-xl font-semibold text-gray-900">Registro Manual</h2>
              </div>
            </div>
            <div className="p-6">
              <Suspense fallback={<FormSkeleton />}>
                <ManualForm />
              </Suspense>
            </div>
          </div>

          {/* Informações Adicionais */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Informações Importantes</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Documentação Necessária</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Justificativa detalhada do motivo</li>
                  <li>• Foto comprobatória (opcional)</li>
                  <li>• Localização atual (se disponível)</li>
                </ul>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Processo de Aprovação</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Registro enviado para análise</li>
                  <li>• Supervisor recebe notificação</li>
                  <li>• Aprovação em até 24 horas</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function FormSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/4" />
            <div className="h-10 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded w-1/4" />
        <div className="h-24 bg-gray-200 rounded" />
      </div>
      <div className="h-10 bg-gray-200 rounded w-32" />
    </div>
  );
}
