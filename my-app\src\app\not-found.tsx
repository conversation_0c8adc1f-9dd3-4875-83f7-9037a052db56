'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui';
import {
  AlertTriangle,
  Home,
  ArrowLeft,
  Search,
  Clock,
  Users,
  FileText,
  TrendingUp
} from 'lucide-react';

export default function NotFound() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const quickLinks = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      description: 'Voltar ao painel principal'
    },
    {
      name: 'Funcionários',
      href: '/funcionarios',
      icon: Users,
      description: 'Gerenciar funcionários'
    },
    {
      name: 'Ponto Biométrico',
      href: '/ponto/biometrico',
      icon: Clock,
      description: 'Registrar ponto'
    },
    {
      name: 'Relató<PERSON>s',
      href: '/relatorios',
      icon: FileText,
      description: 'Visualizar relatórios'
    },
    {
      name: 'Estatísticas',
      href: '/estatisticas',
      icon: TrendingUp,
      description: '<PERSON><PERSON><PERSON><PERSON> e KPIs'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
            <AlertTriangle className="h-12 w-12 text-red-600" />
          </div>
          
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Página não encontrada
          </h2>
          <p className="text-gray-600 mb-8">
            A página que você está procurando não existe ou foi movida.
          </p>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Informações de Debug */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              Informações de Debug:
            </h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>URL solicitada:</strong> {mounted && typeof window !== 'undefined' ? window.location.pathname : 'N/A'}</p>
              <p><strong>Timestamp:</strong> {mounted ? new Date().toLocaleString('pt-BR') : 'Carregando...'}</p>
              <p><strong>Sistema:</strong> RLPONTO v1.0</p>
            </div>
          </div>

          {/* Ações Rápidas */}
          <div className="space-y-6">
            <div className="flex space-x-4">
              <Link href="/dashboard" className="flex-1">
                <Button variant="primary" className="w-full">
                  <Home className="h-4 w-4 mr-2" />
                  Ir para Dashboard
                </Button>
              </Link>
              
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                className="flex-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
            </div>

            {/* Links Rápidos */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Acesso Rápido
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {quickLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="group relative rounded-lg border border-gray-300 bg-white px-6 py-4 shadow-sm hover:border-blue-500 hover:shadow-md transition-all duration-200"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <link.icon className="h-6 w-6 text-blue-600 group-hover:text-blue-700" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium text-gray-900 group-hover:text-blue-700">
                          {link.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {link.description}
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Busca */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center space-x-3 text-sm text-gray-600">
                <Search className="h-5 w-5" />
                <span>
                  Não encontrou o que procura? 
                  <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-1">
                    Explore o dashboard
                  </Link>
                </span>
              </div>
            </div>

            {/* Informações de Contato */}
            <div className="border-t border-gray-200 pt-6">
              <div className="text-center text-sm text-gray-500">
                <p>
                  Se o problema persistir, entre em contato com o suporte técnico.
                </p>
                <p className="mt-1">
                  <strong>Sistema RLPONTO</strong> - Controle de Ponto Eletrônico
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

