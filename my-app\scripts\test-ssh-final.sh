#!/bin/bash

# 🔑 Teste Final de SSH - Sistema RLPONTO
# Testa conexão SSH com senha e prepara configuração sem senha

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
SERVER_IP="************"
SERVER_USER="root"
SERVER_PASS="@Ric6109"

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

echo -e "${BLUE}"
echo "================================================================="
echo "🔑 TESTE FINAL DE SSH - SISTEMA RLPONTO"
echo "================================================================="
echo "Servidor: ${SERVER_IP}"
echo "Usuário: ${SERVER_USER}"
echo "================================================================="
echo -e "${NC}"

# 1. Teste de conectividade básica
log "1. Testando conectividade básica..."
if ping -c 2 ${SERVER_IP} >/dev/null 2>&1; then
    success "Servidor responde ao ping"
else
    error "Servidor não responde ao ping"
    exit 1
fi

# 2. Teste de porta SSH
log "2. Testando porta SSH (22)..."
if nc -z -w5 ${SERVER_IP} 22 >/dev/null 2>&1; then
    success "Porta SSH (22) está aberta"
else
    error "Porta SSH (22) não está acessível"
    exit 1
fi

# 3. Teste de SSH com senha (método interativo)
log "3. Testando SSH com senha..."
echo -e "${YELLOW}"
echo "================================================================="
echo "TESTE MANUAL DE SSH NECESSÁRIO"
echo "================================================================="
echo "Execute o comando abaixo e digite a senha quando solicitado:"
echo ""
echo "ssh ${SERVER_USER}@${SERVER_IP} \"echo 'SSH OK' && hostname && whoami && uptime\""
echo ""
echo "Senha: ${SERVER_PASS}"
echo ""
echo "Se a conexão funcionar, você verá:"
echo "- SSH OK"
echo "- Nome do servidor"
echo "- Usuário (root)"
echo "- Uptime do sistema"
echo "================================================================="
echo -e "${NC}"

read -p "Pressione ENTER para continuar após testar SSH manualmente..."

# 4. Verificar se chave SSH já existe
log "4. Verificando chaves SSH existentes..."
SSH_KEY_PATH="$HOME/.ssh/rl-ponto-next"

if [ -f "${SSH_KEY_PATH}" ]; then
    warning "Chave SSH já existe: ${SSH_KEY_PATH}"
    echo "Chave pública:"
    cat "${SSH_KEY_PATH}.pub" 2>/dev/null || echo "Arquivo .pub não encontrado"
else
    log "Chave SSH não existe ainda. Será criada pelo setup."
fi

# 5. Verificar configuração SSH
log "5. Verificando configuração SSH..."
SSH_CONFIG="$HOME/.ssh/config"

if [ -f "${SSH_CONFIG}" ]; then
    if grep -q "Host rlponto-prod" "${SSH_CONFIG}"; then
        warning "Configuração SSH já existe para rlponto-prod"
        echo "Configuração atual:"
        grep -A 10 "Host rlponto-prod" "${SSH_CONFIG}"
    else
        log "Arquivo config existe, mas sem configuração para rlponto-prod"
    fi
else
    log "Arquivo ~/.ssh/config não existe. Será criado pelo setup."
fi

# 6. Teste de ferramentas necessárias
log "6. Verificando ferramentas necessárias..."

# ssh-keygen
if command -v ssh-keygen >/dev/null 2>&1; then
    success "ssh-keygen disponível"
else
    error "ssh-keygen não encontrado"
fi

# ssh-copy-id
if command -v ssh-copy-id >/dev/null 2>&1; then
    success "ssh-copy-id disponível"
else
    warning "ssh-copy-id não encontrado (usaremos método manual)"
fi

# 7. Informações do sistema local
log "7. Informações do sistema local..."
echo "Sistema operacional: $(uname -s 2>/dev/null || echo 'Windows')"
echo "Usuário atual: $(whoami)"
echo "Diretório home: ${HOME}"
echo "Diretório SSH: ${HOME}/.ssh"

# Verificar diretório .ssh
if [ -d "${HOME}/.ssh" ]; then
    success "Diretório ~/.ssh existe"
    echo "Permissões: $(ls -ld ${HOME}/.ssh | awk '{print $1}')"
else
    warning "Diretório ~/.ssh não existe. Será criado."
fi

echo -e "${BLUE}"
echo "================================================================="
echo "📋 RESUMO DO TESTE SSH"
echo "================================================================="
echo "✅ Conectividade: OK"
echo "✅ Porta SSH: Aberta"
echo "⚠️ SSH com senha: Teste manual necessário"
echo "📝 Chave SSH: $([ -f "${SSH_KEY_PATH}" ] && echo 'Existe' || echo 'Será criada')"
echo "📝 Config SSH: $([ -f "${SSH_CONFIG}" ] && echo 'Existe' || echo 'Será criado')"
echo ""
echo "🚀 PRÓXIMOS PASSOS:"
echo "1. Se SSH manual funcionou, execute:"
echo "   ./scripts/setup-production.sh"
echo ""
echo "2. Ou execute setup completo:"
echo "   ./scripts/setup-complete.sh"
echo ""
echo "3. Após configurar SSH sem senha, teste:"
echo "   ssh rlponto-prod"
echo "================================================================="
echo -e "${NC}"

log "✅ Teste final de SSH concluído!"
