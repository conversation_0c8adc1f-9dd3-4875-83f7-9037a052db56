'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui';
import { 
  Fingerprint, 
  Camera, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  User,
  Clock
} from 'lucide-react';

interface BiometricScannerProps {
  type: 'fingerprint' | 'facial';
}

type ScanStatus = 'idle' | 'scanning' | 'success' | 'error' | 'not_found';

interface ScanResult {
  funcionario?: {
    nome: string;
    matricula: string;
    cargo: string;
  };
  tipoRegistro: string;
  horario: string;
  message: string;
}

export function BiometricScanner({ type }: BiometricScannerProps) {
  const [status, setStatus] = useState<ScanStatus>('idle');
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [progress, setProgress] = useState(0);

  const isFingerprint = type === 'fingerprint';
  const Icon = isFingerprint ? Fingerprint : Camera;
  const title = isFingerprint ? 'Biometria Digital' : 'Reconhecimento Facial';
  const instruction = isFingerprint 
    ? 'Posicione seu dedo no leitor biométrico'
    : 'Posicione seu rosto na câmera';

  const startScan = async () => {
    setStatus('scanning');
    setProgress(0);
    setScanResult(null);

    // Simular progresso do scan
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);

    try {
      // Simular processo de escaneamento
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simular resultado (usar timestamp para determinismo)
      const isSuccess = Date.now() % 5 !== 0; // 80% sucesso, 20% erro
      
      if (isSuccess) {
        const mockResult: ScanResult = {
          funcionario: {
            nome: 'João Silva Santos',
            matricula: 'EMP001',
            cargo: 'Analista de Sistemas'
          },
          tipoRegistro: 'Entrada',
          horario: new Date().toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
          }),
          message: 'Ponto registrado com sucesso!'
        };

        setScanResult(mockResult);
        setStatus('success');

        // Registrar ponto via API
        await registerPonto(mockResult);
      } else {
        // Simular diferentes tipos de erro
        const errorTypes = ['not_found', 'error'];
        const errorType = errorTypes[Date.now() % 2] as ScanStatus;
        
        setStatus(errorType);
        setScanResult({
          tipoRegistro: '',
          horario: '',
          message: errorType === 'not_found' 
            ? 'Biometria não encontrada. Verifique o cadastro.'
            : 'Erro na leitura. Tente novamente.'
        });
      }
    } catch (error) {
      setStatus('error');
      setScanResult({
        tipoRegistro: '',
        horario: '',
        message: 'Erro de conexão. Tente novamente.'
      });
    }

    clearInterval(progressInterval);
    setProgress(100);
  };

  const registerPonto = async (result: ScanResult) => {
    try {
      const response = await fetch('/api/ponto/biometrico', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          funcionarioId: result.funcionario?.matricula,
          tipo: result.tipoRegistro.toLowerCase(),
          biometricType: type,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao registrar ponto');
      }

      console.log('Ponto registrado com sucesso');
    } catch (error) {
      console.error('Erro ao registrar ponto:', error);
    }
  };

  const resetScan = () => {
    setStatus('idle');
    setScanResult(null);
    setProgress(0);
  };

  const getStatusColor = () => {
    switch (status) {
      case 'scanning':
        return 'border-blue-300 bg-blue-50';
      case 'success':
        return 'border-green-300 bg-green-50';
      case 'error':
      case 'not_found':
        return 'border-red-300 bg-red-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'scanning':
        return <Loader2 className="h-12 w-12 text-blue-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-600" />;
      case 'error':
      case 'not_found':
        return <XCircle className="h-12 w-12 text-red-600" />;
      default:
        return <Icon className="h-12 w-12 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'scanning':
        return 'Escaneando...';
      case 'success':
        return scanResult?.message || 'Sucesso!';
      case 'error':
      case 'not_found':
        return scanResult?.message || 'Erro na leitura';
      default:
        return instruction;
    }
  };

  return (
    <div className="space-y-6">
      {/* Área de Scan */}
      <div className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${getStatusColor()}`}>
        <div className="space-y-4">
          {getStatusIcon()}
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {getStatusMessage()}
            </h3>
            
            {status === 'scanning' && (
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}
          </div>
        </div>

        {/* Resultado do Scan */}
        {scanResult && status === 'success' && scanResult.funcionario && (
          <div className="mt-6 p-4 bg-white rounded-lg border border-green-200">
            <div className="flex items-center space-x-3 mb-3">
              <User className="h-5 w-5 text-green-600" />
              <div className="text-left">
                <div className="font-medium text-gray-900">{scanResult.funcionario.nome}</div>
                <div className="text-sm text-gray-600">
                  {scanResult.funcionario.matricula} • {scanResult.funcionario.cargo}
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">Tipo:</span>
                <span className="font-medium text-gray-900">{scanResult.tipoRegistro}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">Horário:</span>
                <span className="font-medium text-gray-900">{scanResult.horario}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Botões de Ação */}
      <div className="flex space-x-3">
        {status === 'idle' && (
          <Button 
            onClick={startScan}
            className="flex-1"
            variant="primary"
          >
            <Icon className="h-4 w-4 mr-2" />
            Iniciar {title}
          </Button>
        )}

        {status === 'scanning' && (
          <Button 
            onClick={resetScan}
            className="flex-1"
            variant="outline"
          >
            Cancelar
          </Button>
        )}

        {(status === 'success' || status === 'error' || status === 'not_found') && (
          <>
            <Button 
              onClick={resetScan}
              className="flex-1"
              variant="outline"
            >
              Novo Scan
            </Button>
            {status === 'success' && (
              <Button 
                onClick={startScan}
                className="flex-1"
                variant="primary"
              >
                Registrar Novamente
              </Button>
            )}
          </>
        )}
      </div>

      {/* Instruções */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div className="text-sm text-gray-700">
            <p className="font-medium mb-1">Instruções:</p>
            <ul className="space-y-1 text-xs">
              {isFingerprint ? (
                <>
                  <li>• Posicione o dedo firmemente no sensor</li>
                  <li>• Mantenha o dedo imóvel durante o scan</li>
                  <li>• Certifique-se de que o dedo esteja limpo</li>
                </>
              ) : (
                <>
                  <li>• Posicione o rosto centralizado na câmera</li>
                  <li>• Mantenha-se imóvel durante o scan</li>
                  <li>• Certifique-se de ter boa iluminação</li>
                </>
              )}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

