'use client';

import { useState } from 'react';
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Clock, 
  Users, 
  Target,
  Calendar,
  AlertTriangle,
  Download,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui';

interface AnalyticsTemplate {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: 'dashboard' | 'kpi' | 'comparativo' | 'tendencia';
  complexity: 'simples' | 'medio' | 'avancado';
  estimatedTime: string;
  features: string[];
  color: string;
  bgColor: string;
}

export function AnalyticsTemplates() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const templates: AnalyticsTemplate[] = [
    {
      id: 'dashboard-executivo',
      title: 'Dashboard Executivo',
      description: 'Visão geral com principais KPIs e métricas',
      icon: BarChart3,
      category: 'dashboard',
      complexity: 'medio',
      estimatedTime: '2-3 min',
      features: ['KPIs principais', 'Gráficos interativos', 'Alertas', 'Resumo executivo'],
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'analise-frequencia',
      title: 'Análise de Frequência',
      description: 'Análise detalhada de presença e pontualidade',
      icon: Clock,
      category: 'kpi',
      complexity: 'simples',
      estimatedTime: '1-2 min',
      features: ['Taxa de presença', 'Atrasos', 'Padrões', 'Tendências'],
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      id: 'horas-extras-analise',
      title: 'Análise de Horas Extras',
      description: 'Distribuição e custos de horas extras',
      icon: TrendingUp,
      category: 'kpi',
      complexity: 'medio',
      estimatedTime: '2-3 min',
      features: ['Distribuição por funcionário', 'Custos', 'Tendências', 'Alertas'],
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      id: 'absenteismo-dashboard',
      title: 'Dashboard de Absenteísmo',
      description: 'Análise completa de ausências e impactos',
      icon: AlertTriangle,
      category: 'dashboard',
      complexity: 'avancado',
      estimatedTime: '3-4 min',
      features: ['Taxa de absenteísmo', 'Motivos', 'Impacto financeiro', 'Previsões'],
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      id: 'comparativo-departamentos',
      title: 'Comparativo entre Departamentos',
      description: 'Análise comparativa de performance por setor',
      icon: PieChart,
      category: 'comparativo',
      complexity: 'medio',
      estimatedTime: '2-3 min',
      features: ['Comparação por setor', 'Rankings', 'Benchmarks', 'Insights'],
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    },
    {
      id: 'tendencias-historicas',
      title: 'Tendências Históricas',
      description: 'Análise de tendências e projeções futuras',
      icon: Calendar,
      category: 'tendencia',
      complexity: 'avancado',
      estimatedTime: '3-5 min',
      features: ['Análise temporal', 'Projeções', 'Sazonalidade', 'Previsões'],
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      id: 'performance-individual',
      title: 'Performance Individual',
      description: 'Análise detalhada de performance por funcionário',
      icon: Users,
      category: 'kpi',
      complexity: 'medio',
      estimatedTime: '2-3 min',
      features: ['Ranking individual', 'Métricas pessoais', 'Comparativos', 'Evolução'],
      color: 'text-teal-600',
      bgColor: 'bg-teal-50'
    },
    {
      id: 'metas-objetivos',
      title: 'Metas e Objetivos',
      description: 'Acompanhamento de metas e indicadores',
      icon: Target,
      category: 'kpi',
      complexity: 'simples',
      estimatedTime: '1-2 min',
      features: ['Progresso de metas', 'Indicadores', 'Alertas', 'Ações'],
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50'
    }
  ];

  const categories = [
    { id: 'dashboard', label: 'Dashboards', count: templates.filter(t => t.category === 'dashboard').length },
    { id: 'kpi', label: 'KPIs', count: templates.filter(t => t.category === 'kpi').length },
    { id: 'comparativo', label: 'Comparativos', count: templates.filter(t => t.category === 'comparativo').length },
    { id: 'tendencia', label: 'Tendências', count: templates.filter(t => t.category === 'tendencia').length }
  ];

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simples':
        return 'bg-green-100 text-green-800';
      case 'medio':
        return 'bg-yellow-100 text-yellow-800';
      case 'avancado':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getComplexityLabel = (complexity: string) => {
    switch (complexity) {
      case 'simples':
        return 'Simples';
      case 'medio':
        return 'Médio';
      case 'avancado':
        return 'Avançado';
      default:
        return 'N/A';
    }
  };

  const handleGenerate = (templateId: string) => {
    console.log('Gerando relatório analítico:', templateId);
    // Simular geração
    alert(`Gerando ${templates.find(t => t.id === templateId)?.title}...`);
  };

  const handlePreview = (templateId: string) => {
    setSelectedTemplate(templateId);
    console.log('Preview do template:', templateId);
  };

  return (
    <div className="space-y-6">
      {/* Categorias */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <div
            key={category.id}
            className="px-3 py-2 bg-gray-100 rounded-lg text-sm"
          >
            <span className="font-medium text-gray-900">{category.label}</span>
            <span className="ml-2 px-2 py-0.5 bg-gray-200 text-gray-600 rounded-full text-xs">
              {category.count}
            </span>
          </div>
        ))}
      </div>

      {/* Grid de Templates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {templates.map((template) => (
          <div
            key={template.id}
            className={`p-4 border-2 rounded-lg transition-all cursor-pointer ${
              selectedTemplate === template.id
                ? `border-purple-300 ${template.bgColor}`
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
            }`}
            onClick={() => setSelectedTemplate(template.id)}
          >
            <div className="flex items-start space-x-3 mb-3">
              <div className={`p-2 ${template.bgColor} rounded-lg`}>
                <template.icon className={`h-5 w-5 ${template.color}`} />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-gray-900">{template.title}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${getComplexityColor(template.complexity)}`}>
                    {getComplexityLabel(template.complexity)}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>⏱️ {template.estimatedTime}</span>
                  <span className="capitalize">{template.category}</span>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="flex flex-wrap gap-1 mb-3">
              {template.features.slice(0, 3).map((feature, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                >
                  {feature}
                </span>
              ))}
              {template.features.length > 3 && (
                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                  +{template.features.length - 3}
                </span>
              )}
            </div>

            {/* Ações */}
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  handlePreview(template.id);
                }}
                className="flex-1"
              >
                <Eye className="h-3 w-3 mr-1" />
                Preview
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleGenerate(template.id);
                }}
                className="flex-1"
              >
                <Download className="h-3 w-3 mr-1" />
                Gerar
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Template Selecionado */}
      {selectedTemplate && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h4 className="font-medium text-purple-900 mb-2">
            Template Selecionado: {templates.find(t => t.id === selectedTemplate)?.title}
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm text-purple-700">
            <div>
              <strong>Recursos inclusos:</strong>
              <ul className="list-disc list-inside mt-1">
                {templates.find(t => t.id === selectedTemplate)?.features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>
            <div>
              <strong>Configurações:</strong>
              <ul className="list-disc list-inside mt-1">
                <li>Período personalizável</li>
                <li>Filtros por departamento</li>
                <li>Exportação em múltiplos formatos</li>
                <li>Agendamento automático</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
