# 📋 ANATOMIA COMPLETA DO SISTEMA RLPONTO-WEB

## 🎯 VISÃO GERAL DO SISTEMA

**RLPONTO-WEB** é um sistema completo de controle de ponto eletrônico desenvolvido em Python/Flask com integração biométrica avançada, gestão empresarial multi-cliente e relatórios analíticos.

### 🏗️ ARQUITETURA GERAL
- **Backend**: Flask (Python 3.x) com arquitetura modular em blueprints
- **Frontend**: HTML5/CSS3/JavaScript com Bootstrap 5.1.3 responsivo
- **Banco de Dados**: MySQL 8.0+ com views otimizadas
- **Biometria**: Sistema universal multi-fabricante com bridge local
- **Autenticação**: Sistema próprio com níveis de acesso e 2FA
- **Deploy**: Servidor Linux com Gunicorn/Nginx

---

## 📁 ESTRUTURA DE ARQUIVOS

### 🔧 ARQUIVOS PRINCIPAIS
```
var/www/controle-ponto/
├── app.py                          # Aplicação Flask principal
├── requirements.txt                # Dependências Python
├── .env                           # Variáveis de ambiente
├── universal_biometric_service.py  # Serviço biométrico universal
└── biometria_service.py           # Bridge biométrico legacy
```

### 📦 MÓDULOS FUNCIONAIS (Blueprints)
```
├── app_funcionarios.py            # Gestão de funcionários
├── app_relatorios.py              # Sistema de relatórios
├── app_registro_ponto.py          # Registro de ponto
├── app_configuracoes.py           # Configurações do sistema
├── app_empresa_principal.py       # Gestão multi-empresa
├── app_ponto_admin.py             # Administração de pontos
├── app_status.py                  # Dashboard e status
├── app_controle_periodo.py        # Controle de períodos
└── app_quality_control.py         # Controle de qualidade
```

### 🛠️ UTILITÁRIOS E SERVIÇOS
```
utils/
├── database.py                    # Gerenciador de banco de dados
├── auth.py                       # Sistema de autenticação
├── config.py                     # Configurações centralizadas
├── helpers.py                    # Funções auxiliares
└── audit_logger.py               # Sistema de auditoria
```

### 🎨 FRONTEND
```
templates/
├── base.html                     # Template base responsivo
├── login.html                    # Página de login
├── index.html                    # Dashboard principal
├── funcionarios/                 # Templates de funcionários
├── relatorios/                   # Templates de relatórios
├── configuracoes/                # Templates de configuração
├── empresa_principal/            # Templates multi-empresa
├── registro_ponto/               # Templates de registro
├── status/                       # Templates de status
└── quality_control/              # Templates de qualidade

static/
├── css/                          # Estilos CSS
├── js/                           # JavaScript
├── images/                       # Imagens e avatars
└── fotos_funcionarios/           # Fotos dos funcionários
```

---

## 🗄️ ESTRUTURA DO BANCO DE DADOS

### 👥 TABELAS PRINCIPAIS

#### **funcionarios**
```sql
- id (PK)
- nome_completo
- cpf (UNIQUE)
- matricula_empresa
- setor, cargo, empresa
- data_nascimento, telefone, email
- endereco_completo
- foto_url
- ativo (BOOLEAN)
- data_cadastro, data_atualizacao
```

#### **registros_ponto**
```sql
- id (PK)
- funcionario_id (FK)
- data_hora (DATETIME)
- tipo_registro (entrada_manha, saida_almoco, entrada_tarde, saida)
- metodo_registro (biometrico, manual)
- qualidade_biometria (0-100)
- observacoes
- ip_origem
- criado_por (FK usuarios)
- criado_em
```

#### **biometria_funcionarios**
```sql
- id (PK)
- funcionario_id (FK)
- template_biometrico (LONGTEXT)
- dedo_cadastrado (1-10)
- qualidade_template (0-100)
- data_cadastro
- ativo (BOOLEAN)
```

#### **empresas**
```sql
- id (PK)
- nome_empresa
- cnpj (UNIQUE)
- endereco_completo
- telefone, email
- is_principal (BOOLEAN)
- configuracoes_jornada (JSON)
- ativo (BOOLEAN)
```

#### **usuarios**
```sql
- id (PK)
- usuario (UNIQUE)
- senha_hash
- nome
- email
- nivel_acesso (admin, usuario, readonly)
- ultimo_login
- tentativas_login
- ativo (BOOLEAN)
```

### 📊 VIEWS OTIMIZADAS

#### **vw_relatorio_pontos**
```sql
-- View principal para relatórios
-- Combina registros_ponto + funcionarios + empresas
-- Calcula status de pontualidade automaticamente
-- Formata datas e horários para exibição
```

#### **vw_estatisticas_pontos**
```sql
-- View para estatísticas diárias
-- Agrupa dados por data
-- Calcula métricas de presença e pontualidade
```

---

## 🔐 SISTEMA DE AUTENTICAÇÃO

### 🎭 NÍVEIS DE ACESSO
1. **admin**: Acesso total ao sistema
2. **usuario**: Acesso limitado (sem configurações)
3. **readonly**: Apenas visualização
4. **status**: Acesso apenas ao dashboard de status

### 🛡️ RECURSOS DE SEGURANÇA
- Hash de senhas com Argon2/BCrypt
- Controle de tentativas de login
- Logs de auditoria completos
- Sessões com timeout configurável
- Validação de IP de origem
- Força troca de senha no primeiro login

---

## 🔬 SISTEMA BIOMÉTRICO

### 🏗️ ARQUITETURA BIOMÉTRICA

#### **Universal Biometric Framework**
```python
# universal_biometric_service.py
- Auto-detecção de dispositivos via Windows Biometric Framework
- Suporte multi-fabricante (SecuGen, Suprema, Nitgen, etc.)
- API REST padronizada
- Sistema de registro permanente de dispositivos
- Fallback inteligente para simulação
```

#### **Bridge Local (Windows)**
```python
# biometric_bridge_service.py
- Serviço local Windows para comunicação com hardware
- WebSocket para comunicação em tempo real
- Integração com SDKs nativos dos fabricantes
- Detecção automática via WMI/PowerShell
```

### 🔌 API BIOMÉTRICA
```
GET  /devices/discover     # Descobrir dispositivos
POST /capture             # Capturar impressão digital
POST /verify              # Verificar template
GET  /status              # Status do serviço
```

---

## 📊 SISTEMA DE RELATÓRIOS

### 📈 TIPOS DE RELATÓRIOS

#### **1. Relatório de Pontos**
- Filtros: Data, funcionário, tipo de registro, método
- Inclui funcionários ausentes automaticamente
- Cálculo de pontualidade em tempo real
- Exportação PDF/Excel/CSV

#### **2. Estatísticas Gerais**
- Gráficos de registros diários
- Métricas de pontualidade
- Comparativo biométrico vs manual
- Análise de tendências

#### **3. Relatório Individual**
- Histórico completo do funcionário
- Cálculo de horas trabalhadas
- Análise de padrões de pontualidade
- Resumo estatístico personalizado

#### **4. Relatórios Empresariais**
- Visão consolidada multi-empresa
- Relatórios de alocação
- Análise de jornadas de trabalho
- Relatórios financeiros

### 🎨 GERAÇÃO DE RELATÓRIOS
```python
# Tecnologias utilizadas:
- WeasyPrint: Geração de PDF a partir de HTML/CSS
- Pandas: Manipulação de dados
- Chart.js: Gráficos interativos
- Bootstrap Tables: Tabelas responsivas
```

---

## ⚙️ SISTEMA DE CONFIGURAÇÕES

### 🏢 CONFIGURAÇÕES EMPRESARIAIS
```json
{
  "jornada_trabalho": {
    "entrada_manha": "08:00",
    "saida_almoco": "12:00", 
    "entrada_tarde": "13:00",
    "saida": "17:00"
  },
  "tolerancia_atraso": 10,
  "dias_trabalho": ["seg", "ter", "qua", "qui", "sex"],
  "feriados_personalizados": []
}
```

### 🔧 CONFIGURAÇÕES DO SISTEMA
- Configuração de dispositivos biométricos
- Parâmetros de qualidade biométrica
- Configurações de rede e conectividade
- Configurações de backup automático
- Parâmetros de relatórios

### 🎛️ CONFIGURAÇÕES DE USUÁRIO
- Perfis de acesso personalizados
- Configurações de interface
- Preferências de relatórios
- Configurações de notificações

---

## 🎨 INTERFACE DO USUÁRIO

### 📱 DESIGN RESPONSIVO
- **Framework**: Bootstrap 5.1.3
- **Breakpoints**: Desktop (>1024px), Tablet (769-1024px), Mobile (≤768px)
- **Componentes**: Cards, modais, tabelas responsivas
- **Ícones**: Font Awesome 6.0
- **Tipografia**: Segoe UI, Tahoma, Geneva, Verdana

### 🧭 NAVEGAÇÃO PRINCIPAL
```
Sidebar Moderna:
├── 👥 Funcionários
│   ├── Listar Funcionários
│   ├── Novo Funcionário
│   └── Funcionários Desligados
├── ⏰ Registro de Ponto
│   ├── Registro Biométrico
│   ├── Registro Manual
│   └── Administração de Pontos
├── 📊 Relatórios
│   ├── Relatório de Pontos
│   ├── Estatísticas
│   └── Relatórios Personalizados
├── 🏢 Empresa Principal
│   ├── Clientes
│   ├── Alocações
│   ├── Jornadas
│   └── Relatórios Empresariais
├── ⚙️ Configurações
│   ├── Sistema
│   ├── Biometria
│   ├── Empresas
│   └── Usuários
└── 📈 Status
    ├── Dashboard
    ├── Quality Control
    └── Logs do Sistema
```

### 🎭 COMPONENTES VISUAIS
- **Cards Modernos**: Com gradientes e sombras
- **Tabelas Interativas**: Ordenação, filtros, paginação
- **Modais Responsivos**: Para formulários e detalhes
- **Gráficos Dinâmicos**: Chart.js integrado
- **Formulários Inteligentes**: Validação em tempo real
- **Feedback Visual**: Toasts, alerts, loading states

---

## 🔄 FLUXOS PRINCIPAIS

### 👤 CADASTRO DE FUNCIONÁRIO
1. Formulário com validação CPF/dados pessoais
2. Upload de foto com redimensionamento automático
3. Cadastro de biometria (opcional)
4. Alocação em empresa/setor
5. Configuração de jornada de trabalho

### ⏰ REGISTRO DE PONTO
1. **Biométrico**: Captura → Verificação → Registro
2. **Manual**: Seleção funcionário → Tipo registro → Confirmação
3. Validação de horários e regras de negócio
4. Log de auditoria automático

### 📊 GERAÇÃO DE RELATÓRIOS
1. Seleção de filtros e parâmetros
2. Processamento de dados com views otimizadas
3. Geração de gráficos e estatísticas
4. Exportação em múltiplos formatos
5. Cache de resultados para performance

---

## 🚀 TECNOLOGIAS E DEPENDÊNCIAS

### 🐍 BACKEND (Python)
```
Flask==2.3.3              # Framework web
PyMySQL==1.1.0            # Conector MySQL
Flask-Login==0.6.2        # Autenticação
Pillow==10.0.1            # Processamento de imagens
weasyprint==60.2          # Geração de PDF
python-dotenv==1.0.0      # Variáveis de ambiente
argon2-cffi==23.1.0       # Hash de senhas
pyotp==2.9.0              # 2FA
```

### 🎨 FRONTEND
```
Bootstrap 5.1.3           # Framework CSS
Font Awesome 6.0          # Ícones
Chart.js                  # Gráficos
jQuery 3.6.0              # Manipulação DOM
```

### 🗄️ BANCO DE DADOS
```
MySQL 8.0+               # Banco principal
Views otimizadas          # Performance
Índices estratégicos      # Consultas rápidas
```

### 🔧 INFRAESTRUTURA
```
Gunicorn                  # Servidor WSGI
Nginx                     # Proxy reverso
Linux Ubuntu/CentOS      # Sistema operacional
SSL/TLS                   # Segurança
```

---

## 📈 MÉTRICAS E MONITORAMENTO

### 📊 DASHBOARD DE STATUS
- Status dos serviços em tempo real
- Métricas de performance do banco
- Estatísticas de uso do sistema
- Logs de erro e auditoria
- Monitoramento de dispositivos biométricos

### 🔍 QUALITY CONTROL
- Validação de dados de ponto
- Detecção de inconsistências
- Relatórios de qualidade
- Backup e restore automático
- Verificação de integridade

---

## 🎯 CARACTERÍSTICAS TÉCNICAS AVANÇADAS

### ⚡ PERFORMANCE
- Views materializadas para relatórios
- Cache de consultas frequentes
- Paginação otimizada
- Lazy loading de imagens
- Compressão de assets

### 🛡️ SEGURANÇA
- Validação de entrada rigorosa
- Proteção contra SQL injection
- CSRF protection
- Rate limiting
- Logs de auditoria completos

### 📱 RESPONSIVIDADE
- Mobile-first design
- Touch-friendly interfaces
- Breakpoints otimizados
- Modais adaptativos
- Tabelas responsivas

### 🔄 ESCALABILIDADE
- Arquitetura modular
- Blueprints independentes
- API REST padronizada
- Configuração via ambiente
- Deploy containerizado

---

## 🎨 PADRÕES DE DESIGN

### 🎭 IDENTIDADE VISUAL
- **Cores Primárias**: #4fbdba (teal), #667eea (azul)
- **Gradientes**: Modernos com transições suaves
- **Sombras**: Sutis para profundidade
- **Bordas**: Arredondadas (8px-12px)
- **Espaçamento**: Grid de 8px

### 📐 LAYOUT
- **Sidebar**: 280px (desktop), colapsível
- **Containers**: Max-width responsivo
- **Cards**: Padding 20px, border-radius 10px
- **Botões**: Height 40px, padding 12px 24px
- **Formulários**: Labels acima, inputs full-width

---

## 🔮 ARQUITETURA PARA MIGRAÇÃO NEXT.JS

### 🎯 ESTRUTURA RECOMENDADA
```
next-rlponto/
├── pages/                    # Páginas Next.js
│   ├── api/                 # API Routes
│   ├── funcionarios/        # Páginas de funcionários
│   ├── relatorios/          # Páginas de relatórios
│   └── configuracoes/       # Páginas de configuração
├── components/              # Componentes React
│   ├── layout/             # Layout components
│   ├── forms/              # Form components
│   ├── tables/             # Table components
│   └── charts/             # Chart components
├── lib/                    # Utilitários
│   ├── database.js         # Database connection
│   ├── auth.js             # Authentication
│   └── biometric.js        # Biometric integration
├── styles/                 # Estilos CSS/SCSS
├── public/                 # Assets estáticos
└── prisma/                 # Schema do banco (Prisma ORM)
```

### 🔧 STACK TECNOLÓGICA NEXT.JS
```
Next.js 14+                 # Framework React
TypeScript                 # Tipagem estática
Prisma ORM                  # ORM para banco de dados
NextAuth.js                 # Autenticação
Tailwind CSS                # Framework CSS
Recharts                    # Gráficos React
React Hook Form             # Formulários
Zod                         # Validação de schemas
```

---

---

## 🔍 DETALHAMENTO DOS MÓDULOS PRINCIPAIS

### 👥 MÓDULO FUNCIONÁRIOS (app_funcionarios.py)

#### **Funcionalidades Principais**
- **CRUD Completo**: Create, Read, Update, Delete de funcionários
- **Validação Avançada**: CPF, email, telefone com máscaras
- **Upload de Fotos**: Redimensionamento automático, múltiplos formatos
- **Gestão de EPIs**: Controle de equipamentos de proteção
- **Histórico Completo**: Auditoria de todas as alterações

#### **Rotas Principais**
```python
GET  /funcionarios/                    # Listar com paginação
GET  /funcionarios/cadastrar          # Formulário de cadastro
POST /funcionarios/cadastrar          # Processar cadastro
GET  /funcionarios/<id>               # Detalhes do funcionário
GET  /funcionarios/<id>/editar        # Formulário de edição
POST /funcionarios/<id>/editar        # Processar edição
POST /funcionarios/<id>/apagar        # Exclusão lógica
GET  /funcionarios/<id>/foto          # Servir foto do funcionário
```

#### **Validações Implementadas**
- CPF: Algoritmo oficial com dígitos verificadores
- Email: Regex RFC 5322 compliant
- Telefone: Formatos brasileiros (fixo/celular)
- Data de nascimento: Idade mínima 16 anos
- Matrícula: Única por empresa

#### **Integração com Biometria**
- Cadastro de até 2 dedos por funcionário
- Validação de qualidade do template (>60%)
- Backup automático de templates
- Sincronização com dispositivos

### ⏰ MÓDULO REGISTRO DE PONTO (app_registro_ponto.py)

#### **Tipos de Registro**
1. **entrada_manha**: Entrada do turno matutino
2. **saida_almoco**: Saída para intervalo
3. **entrada_tarde**: Retorno do intervalo
4. **saida**: Saída final do expediente

#### **Métodos de Registro**
1. **Biométrico**: Via leitor de impressão digital
2. **Manual**: Inserção manual por administrador

#### **Validações de Negócio**
- Sequência lógica de registros (entrada → saída → entrada → saída)
- Intervalo mínimo entre registros (15 minutos)
- Validação de horário de trabalho
- Detecção de registros duplicados
- Cálculo automático de pontualidade

#### **API de Registro**
```python
POST /api/attendance/register         # Registro automático
POST /registro-ponto/manual          # Registro manual
GET  /registro-ponto/horarios        # Obter horários do funcionário
POST /registro-ponto/justificar      # Justificar ausência/atraso
```

### 📊 MÓDULO RELATÓRIOS (app_relatorios.py)

#### **Relatórios Disponíveis**

##### **1. Relatório de Pontos Geral**
- Filtros: Data, funcionário, setor, tipo de registro
- Inclui funcionários ausentes automaticamente
- Cálculo de horas trabalhadas
- Status de pontualidade em tempo real
- Exportação: PDF, Excel, CSV

##### **2. Relatório Individual**
- Histórico completo do funcionário
- Gráfico de pontualidade mensal
- Cálculo de banco de horas
- Análise de padrões de comportamento
- Resumo estatístico personalizado

##### **3. Estatísticas Gerais**
- Dashboard com métricas em tempo real
- Gráficos de tendência (últimos 30 dias)
- Comparativo departamental
- Análise de produtividade
- Alertas de inconsistências

##### **4. Relatórios Empresariais**
- Visão consolidada multi-empresa
- Relatórios de alocação de funcionários
- Análise de custos por projeto
- Relatórios de compliance trabalhista

#### **Tecnologias de Geração**
```python
# PDF Generation
weasyprint                    # HTML/CSS para PDF
reportlab                     # PDF programático

# Excel Generation
openpyxl                      # Manipulação de planilhas
pandas                        # Análise de dados

# Gráficos
matplotlib                    # Gráficos estáticos
plotly                        # Gráficos interativos
chart.js                      # Gráficos web
```

### 🏢 MÓDULO EMPRESA PRINCIPAL (app_empresa_principal.py)

#### **Gestão Multi-Empresa**
- **Empresa Principal**: Matriz que gerencia clientes
- **Empresas Cliente**: Empresas terceirizadas
- **Contratos**: Gestão de contratos de prestação de serviços
- **Alocações**: Funcionários alocados em clientes

#### **Funcionalidades Principais**

##### **1. Gestão de Clientes**
```python
GET  /empresa-principal/clientes              # Listar clientes
POST /empresa-principal/clientes/novo         # Cadastrar cliente
GET  /empresa-principal/clientes/<id>         # Detalhes do cliente
PUT  /empresa-principal/clientes/<id>         # Editar cliente
```

##### **2. Alocação de Funcionários**
```python
GET  /empresa-principal/alocacoes             # Listar alocações
POST /empresa-principal/alocacoes/nova        # Nova alocação
PUT  /empresa-principal/alocacoes/<id>        # Editar alocação
DELETE /empresa-principal/alocacoes/<id>      # Finalizar alocação
```

##### **3. Jornadas de Trabalho**
```python
GET  /empresa-principal/jornadas              # Listar jornadas
POST /empresa-principal/jornadas/nova         # Criar jornada
PUT  /empresa-principal/jornadas/<id>         # Editar jornada
```

#### **Estrutura de Dados**
```sql
-- Tabela empresa_clientes
empresa_principal_id          # FK para empresa matriz
empresa_cliente_id            # FK para empresa cliente
data_inicio_contrato         # Início do contrato
data_fim_contrato            # Fim do contrato (opcional)
valor_hora_contrato          # Valor/hora do contrato
status_contrato              # ativo, suspenso, finalizado

-- Tabela funcionario_alocacoes
funcionario_id               # FK para funcionário
empresa_cliente_id           # FK para empresa cliente
jornada_trabalho_id          # FK para jornada
data_inicio_alocacao         # Início da alocação
data_fim_alocacao            # Fim da alocação (opcional)
valor_hora_funcionario       # Valor/hora do funcionário
observacoes                  # Observações da alocação
```

### ⚙️ MÓDULO CONFIGURAÇÕES (app_configuracoes.py)

#### **Tipos de Configuração**

##### **1. Configurações do Sistema**
```json
{
  "sistema": {
    "nome_empresa": "RLPONTO Tecnologia",
    "timeout_sessao": 3600,
    "backup_automatico": true,
    "logs_detalhados": true
  }
}
```

##### **2. Configurações de Biometria**
```json
{
  "biometria": {
    "qualidade_minima": 60,
    "tentativas_maximas": 3,
    "timeout_captura": 30,
    "dispositivos_permitidos": ["ZK4500", "SecuGen", "Suprema"]
  }
}
```

##### **3. Configurações de Jornada**
```json
{
  "jornada_padrao": {
    "entrada_manha": "08:00",
    "saida_almoco": "12:00",
    "entrada_tarde": "13:00",
    "saida": "17:00",
    "tolerancia_atraso": 10,
    "tolerancia_saida_antecipada": 10
  }
}
```

##### **4. Configurações de Relatórios**
```json
{
  "relatorios": {
    "registros_por_pagina": 50,
    "formato_data": "dd/mm/yyyy",
    "incluir_ausentes": true,
    "calcular_horas_extras": true
  }
}
```

### 📈 MÓDULO STATUS (app_status.py)

#### **Dashboard de Monitoramento**
- **Status dos Serviços**: Flask, MySQL, Biometria
- **Métricas em Tempo Real**: Registros/hora, usuários ativos
- **Alertas**: Falhas de sistema, dispositivos offline
- **Performance**: Tempo de resposta, uso de memória

#### **Quality Control**
- **Validação de Dados**: Inconsistências nos registros
- **Backup Status**: Status dos backups automáticos
- **Integridade**: Verificação de integridade do banco
- **Logs de Erro**: Monitoramento de erros do sistema

#### **Métricas Coletadas**
```python
# Métricas de Sistema
cpu_usage                    # Uso de CPU
memory_usage                 # Uso de memória
disk_usage                   # Uso de disco
network_latency              # Latência de rede

# Métricas de Aplicação
active_sessions              # Sessões ativas
requests_per_minute          # Requisições por minuto
database_connections         # Conexões ativas no banco
biometric_devices_online     # Dispositivos biométricos online

# Métricas de Negócio
daily_registrations          # Registros diários
employee_attendance_rate     # Taxa de presença
biometric_success_rate       # Taxa de sucesso biométrico
```

---

## 🔧 CONFIGURAÇÕES AVANÇADAS

### 🌐 CONFIGURAÇÕES DE REDE
```python
# config.py
FLASK_HOST = '0.0.0.0'              # Bind em todas as interfaces
FLASK_PORT = 5000                   # Porta padrão
CORS_ORIGINS = ['*']                # CORS liberado (dev only)
RATE_LIMIT = '1000 per hour'        # Rate limiting
```

### 🗄️ CONFIGURAÇÕES DE BANCO
```python
# database.py
DB_POOL_SIZE = 10                   # Pool de conexões
DB_TIMEOUT = 30                     # Timeout de conexão
DB_CHARSET = 'utf8mb4'              # Charset UTF-8
DB_AUTOCOMMIT = False               # Transações manuais
```

### 🔐 CONFIGURAÇÕES DE SEGURANÇA
```python
# auth.py
PASSWORD_MIN_LENGTH = 8             # Tamanho mínimo da senha
PASSWORD_REQUIRE_SPECIAL = True     # Caracteres especiais obrigatórios
SESSION_TIMEOUT = 3600              # Timeout de sessão (1 hora)
MAX_LOGIN_ATTEMPTS = 5              # Tentativas máximas de login
LOCKOUT_DURATION = 900              # Bloqueio por 15 minutos
```

---

## 📱 COMPONENTES DE INTERFACE

### 🎨 COMPONENTES REUTILIZÁVEIS

#### **DataTable Component**
```javascript
// Tabela com paginação, ordenação e filtros
class DataTable {
  constructor(options) {
    this.container = options.container;
    this.data = options.data;
    this.columns = options.columns;
    this.pagination = options.pagination || true;
    this.sorting = options.sorting || true;
    this.filtering = options.filtering || true;
  }

  render() {
    // Renderização da tabela
  }

  sort(column, direction) {
    // Ordenação
  }

  filter(criteria) {
    // Filtros
  }

  paginate(page, size) {
    // Paginação
  }
}
```

#### **Modal Component**
```javascript
// Modal responsivo e acessível
class Modal {
  constructor(options) {
    this.title = options.title;
    this.content = options.content;
    this.size = options.size || 'medium';
    this.closable = options.closable !== false;
  }

  show() {
    // Exibir modal
  }

  hide() {
    // Ocultar modal
  }

  setContent(content) {
    // Atualizar conteúdo
  }
}
```

#### **Form Validator**
```javascript
// Validação de formulários em tempo real
class FormValidator {
  constructor(form) {
    this.form = form;
    this.rules = {};
    this.errors = {};
  }

  addRule(field, rule) {
    // Adicionar regra de validação
  }

  validate() {
    // Validar formulário
  }

  showErrors() {
    // Exibir erros
  }
}
```

### 📊 COMPONENTES DE GRÁFICOS

#### **Chart Components**
```javascript
// Gráficos com Chart.js
class LineChart {
  constructor(canvas, data, options) {
    this.chart = new Chart(canvas, {
      type: 'line',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        ...options
      }
    });
  }

  updateData(newData) {
    this.chart.data = newData;
    this.chart.update();
  }
}

class BarChart {
  // Similar ao LineChart
}

class PieChart {
  // Similar ao LineChart
}
```

---

## 🔄 FLUXOS DE DADOS DETALHADOS

### 📊 FLUXO DE GERAÇÃO DE RELATÓRIOS

```mermaid
graph TD
    A[Usuário seleciona filtros] --> B[Validação de parâmetros]
    B --> C[Construção da query SQL]
    C --> D[Execução no banco de dados]
    D --> E[Processamento dos dados]
    E --> F[Cálculo de estatísticas]
    F --> G[Geração do template]
    G --> H[Renderização final]
    H --> I[Cache do resultado]
    I --> J[Entrega ao usuário]
```

### 🔐 FLUXO DE AUTENTICAÇÃO

```mermaid
graph TD
    A[Login do usuário] --> B[Validação de credenciais]
    B --> C{Credenciais válidas?}
    C -->|Não| D[Incrementar tentativas]
    D --> E{Máximo de tentativas?}
    E -->|Sim| F[Bloquear usuário]
    E -->|Não| G[Retornar erro]
    C -->|Sim| H[Criar sessão]
    H --> I[Definir nível de acesso]
    I --> J[Redirecionar para dashboard]
```

### 🔬 FLUXO BIOMÉTRICO

```mermaid
graph TD
    A[Solicitação de captura] --> B[Verificar dispositivo]
    B --> C{Dispositivo online?}
    C -->|Não| D[Erro: Dispositivo offline]
    C -->|Sim| E[Capturar impressão]
    E --> F[Validar qualidade]
    F --> G{Qualidade OK?}
    G -->|Não| H[Solicitar nova captura]
    G -->|Sim| I[Comparar com banco]
    I --> J{Match encontrado?}
    J -->|Não| K[Erro: Não identificado]
    J -->|Sim| L[Registrar ponto]
    L --> M[Retornar sucesso]
```

---

---

## 🌐 APIS E INTEGRAÇÕES

### 🔌 API REST PRINCIPAL

#### **Endpoints de Autenticação**
```http
POST /api/auth/login              # Login do usuário
POST /api/auth/logout             # Logout do usuário
POST /api/auth/refresh            # Renovar token
GET  /api/auth/me                 # Dados do usuário logado
POST /api/auth/change-password    # Alterar senha
```

#### **Endpoints de Funcionários**
```http
GET    /api/funcionarios                    # Listar funcionários
POST   /api/funcionarios                    # Criar funcionário
GET    /api/funcionarios/{id}               # Obter funcionário
PUT    /api/funcionarios/{id}               # Atualizar funcionário
DELETE /api/funcionarios/{id}               # Excluir funcionário
POST   /api/funcionarios/{id}/foto          # Upload de foto
GET    /api/funcionarios/{id}/biometria     # Dados biométricos
POST   /api/funcionarios/{id}/biometria     # Cadastrar biometria
```

#### **Endpoints de Registro de Ponto**
```http
POST /api/ponto/registrar                   # Registrar ponto
GET  /api/ponto/funcionario/{id}            # Registros do funcionário
GET  /api/ponto/hoje                        # Registros de hoje
POST /api/ponto/justificar                  # Justificar ausência
GET  /api/ponto/horarios/{funcionario_id}   # Horários do funcionário
```

#### **Endpoints de Relatórios**
```http
GET  /api/relatorios/pontos                 # Relatório de pontos
GET  /api/relatorios/estatisticas           # Estatísticas gerais
GET  /api/relatorios/funcionario/{id}       # Relatório individual
POST /api/relatorios/exportar               # Exportar relatório
GET  /api/relatorios/dashboard              # Dados do dashboard
```

#### **Endpoints de Biometria**
```http
GET  /api/biometric/devices                 # Listar dispositivos
POST /api/biometric/capture                 # Capturar impressão
POST /api/biometric/verify                  # Verificar impressão
GET  /api/biometric/status                  # Status do serviço
POST /api/biometric/register                # Registrar template
```

### 🔗 INTEGRAÇÕES EXTERNAS

#### **Integração com Sistemas de RH**
```python
# Exemplo de integração com sistemas externos
class HRSystemIntegration:
    def __init__(self, api_url, api_key):
        self.api_url = api_url
        self.api_key = api_key

    def sync_employees(self):
        """Sincronizar funcionários com sistema de RH"""
        employees = self.fetch_employees()
        for employee in employees:
            self.update_local_employee(employee)

    def send_attendance_data(self, date_range):
        """Enviar dados de ponto para sistema de RH"""
        attendance_data = self.get_attendance_data(date_range)
        return self.post_to_hr_system(attendance_data)
```

#### **Integração com Sistemas de Folha de Pagamento**
```python
class PayrollIntegration:
    def export_timesheet(self, employee_id, period):
        """Exportar folha de ponto para sistema de folha"""
        timesheet = self.generate_timesheet(employee_id, period)
        return self.format_for_payroll(timesheet)

    def calculate_overtime(self, employee_id, period):
        """Calcular horas extras"""
        worked_hours = self.get_worked_hours(employee_id, period)
        standard_hours = self.get_standard_hours(employee_id)
        return max(0, worked_hours - standard_hours)
```

#### **Integração com Sistemas de Controle de Acesso**
```python
class AccessControlIntegration:
    def sync_biometric_templates(self):
        """Sincronizar templates biométricos com catracas"""
        templates = self.get_all_templates()
        for template in templates:
            self.send_to_access_control(template)

    def receive_access_events(self):
        """Receber eventos de acesso das catracas"""
        events = self.fetch_access_events()
        for event in events:
            self.process_access_event(event)
```

### 📡 WEBHOOKS E NOTIFICAÇÕES

#### **Sistema de Webhooks**
```python
class WebhookManager:
    def __init__(self):
        self.subscribers = {}

    def register_webhook(self, event_type, url, secret):
        """Registrar webhook para evento específico"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []

        self.subscribers[event_type].append({
            'url': url,
            'secret': secret,
            'active': True
        })

    def trigger_webhook(self, event_type, data):
        """Disparar webhook para evento"""
        if event_type in self.subscribers:
            for subscriber in self.subscribers[event_type]:
                if subscriber['active']:
                    self.send_webhook(subscriber, data)
```

#### **Eventos Disponíveis**
```python
WEBHOOK_EVENTS = {
    'employee.created': 'Funcionário criado',
    'employee.updated': 'Funcionário atualizado',
    'employee.deleted': 'Funcionário excluído',
    'attendance.registered': 'Ponto registrado',
    'attendance.late': 'Atraso detectado',
    'attendance.absent': 'Ausência detectada',
    'biometric.enrolled': 'Biometria cadastrada',
    'biometric.failed': 'Falha na biometria',
    'system.error': 'Erro do sistema',
    'backup.completed': 'Backup concluído'
}
```

---

## 🔒 SEGURANÇA E COMPLIANCE

### 🛡️ MEDIDAS DE SEGURANÇA IMPLEMENTADAS

#### **Autenticação e Autorização**
- **Hash de Senhas**: Argon2id (recomendação OWASP)
- **Controle de Sessão**: Tokens JWT com refresh
- **Rate Limiting**: Proteção contra ataques de força bruta
- **2FA Opcional**: TOTP (Google Authenticator)
- **Níveis de Acesso**: RBAC (Role-Based Access Control)

#### **Proteção de Dados**
```python
# Criptografia de dados sensíveis
from cryptography.fernet import Fernet

class DataEncryption:
    def __init__(self, key):
        self.cipher = Fernet(key)

    def encrypt_biometric_template(self, template):
        """Criptografar template biométrico"""
        return self.cipher.encrypt(template.encode())

    def decrypt_biometric_template(self, encrypted_template):
        """Descriptografar template biométrico"""
        return self.cipher.decrypt(encrypted_template).decode()
```

#### **Auditoria e Logs**
```python
# Sistema de auditoria completo
class AuditLogger:
    def log_action(self, user_id, action, resource, details):
        """Registrar ação do usuário"""
        audit_entry = {
            'timestamp': datetime.now(),
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'details': details,
            'ip_address': self.get_client_ip(),
            'user_agent': self.get_user_agent()
        }
        self.save_audit_entry(audit_entry)
```

### 📋 COMPLIANCE TRABALHISTA

#### **Lei Geral de Proteção de Dados (LGPD)**
- **Consentimento**: Termo de consentimento para coleta de biometria
- **Minimização**: Coleta apenas de dados necessários
- **Transparência**: Política de privacidade clara
- **Direito ao Esquecimento**: Exclusão de dados pessoais
- **Portabilidade**: Exportação de dados em formato padrão

#### **Consolidação das Leis do Trabalho (CLT)**
- **Registro de Ponto**: Conforme Portaria 671/2021
- **Jornada de Trabalho**: Controle de 8h diárias / 44h semanais
- **Intervalos**: Controle de intervalos obrigatórios
- **Horas Extras**: Cálculo automático conforme legislação
- **Banco de Horas**: Gestão de compensação de horas

#### **Normas Regulamentadoras (NRs)**
- **NR-12**: Controle de acesso a máquinas perigosas
- **NR-17**: Ergonomia no uso de dispositivos biométricos
- **NR-35**: Controle de acesso a trabalhos em altura

---

## 📊 ANALYTICS E BUSINESS INTELLIGENCE

### 📈 MÉTRICAS DE NEGÓCIO

#### **KPIs Principais**
```python
class BusinessMetrics:
    def attendance_rate(self, period):
        """Taxa de presença no período"""
        total_expected = self.get_expected_attendance(period)
        total_present = self.get_actual_attendance(period)
        return (total_present / total_expected) * 100

    def punctuality_rate(self, period):
        """Taxa de pontualidade no período"""
        total_entries = self.get_total_entries(period)
        on_time_entries = self.get_on_time_entries(period)
        return (on_time_entries / total_entries) * 100

    def biometric_success_rate(self, period):
        """Taxa de sucesso biométrico"""
        total_attempts = self.get_biometric_attempts(period)
        successful_attempts = self.get_successful_biometric(period)
        return (successful_attempts / total_attempts) * 100
```

#### **Análises Avançadas**
```python
class AdvancedAnalytics:
    def predict_absenteeism(self, employee_id):
        """Predizer probabilidade de ausência"""
        historical_data = self.get_employee_history(employee_id)
        patterns = self.analyze_patterns(historical_data)
        return self.calculate_absence_probability(patterns)

    def detect_anomalies(self, period):
        """Detectar anomalias nos registros"""
        normal_patterns = self.get_normal_patterns()
        current_data = self.get_period_data(period)
        return self.find_anomalies(current_data, normal_patterns)

    def productivity_analysis(self, department, period):
        """Análise de produtividade por departamento"""
        attendance_data = self.get_department_attendance(department, period)
        productivity_metrics = self.calculate_productivity(attendance_data)
        return self.generate_insights(productivity_metrics)
```

### 📊 DASHBOARDS EXECUTIVOS

#### **Dashboard Operacional**
- Registros em tempo real
- Status dos dispositivos biométricos
- Alertas de sistema
- Funcionários presentes/ausentes
- Gráfico de registros por hora

#### **Dashboard Gerencial**
- Taxa de presença mensal
- Análise de pontualidade por departamento
- Custos de horas extras
- Comparativo de produtividade
- Tendências de absenteísmo

#### **Dashboard Executivo**
- ROI do sistema de ponto
- Compliance trabalhista
- Análise de riscos
- Benchmarking setorial
- Projeções financeiras

---

## 🔧 MANUTENÇÃO E OPERAÇÃO

### 🔄 BACKUP E RECUPERAÇÃO

#### **Estratégia de Backup**
```python
class BackupManager:
    def __init__(self):
        self.backup_types = {
            'full': self.full_backup,
            'incremental': self.incremental_backup,
            'differential': self.differential_backup
        }

    def schedule_backups(self):
        """Agendar backups automáticos"""
        # Backup completo: Domingo às 02:00
        # Backup incremental: Diário às 23:00
        # Backup diferencial: Quarta às 02:00
        pass

    def verify_backup_integrity(self, backup_file):
        """Verificar integridade do backup"""
        checksum = self.calculate_checksum(backup_file)
        return self.validate_checksum(checksum)
```

#### **Plano de Recuperação de Desastres**
1. **RTO (Recovery Time Objective)**: 4 horas
2. **RPO (Recovery Point Objective)**: 1 hora
3. **Backup Offsite**: Armazenamento em nuvem
4. **Teste de Recuperação**: Mensal
5. **Documentação**: Procedimentos detalhados

### 📊 MONITORAMENTO E ALERTAS

#### **Métricas de Sistema**
```python
class SystemMonitoring:
    def check_database_health(self):
        """Verificar saúde do banco de dados"""
        metrics = {
            'connection_count': self.get_active_connections(),
            'query_performance': self.get_slow_queries(),
            'disk_usage': self.get_database_size(),
            'replication_lag': self.get_replication_status()
        }
        return self.evaluate_health(metrics)

    def check_application_health(self):
        """Verificar saúde da aplicação"""
        metrics = {
            'response_time': self.measure_response_time(),
            'error_rate': self.get_error_rate(),
            'memory_usage': self.get_memory_usage(),
            'cpu_usage': self.get_cpu_usage()
        }
        return self.evaluate_health(metrics)
```

#### **Sistema de Alertas**
```python
class AlertManager:
    def __init__(self):
        self.alert_channels = {
            'email': EmailNotifier(),
            'sms': SMSNotifier(),
            'slack': SlackNotifier(),
            'webhook': WebhookNotifier()
        }

    def send_alert(self, severity, message, channels=None):
        """Enviar alerta para canais especificados"""
        if channels is None:
            channels = self.get_default_channels(severity)

        for channel in channels:
            if channel in self.alert_channels:
                self.alert_channels[channel].send(severity, message)
```

### 🔄 ATUALIZAÇÕES E VERSIONAMENTO

#### **Estratégia de Deploy**
```python
class DeploymentManager:
    def __init__(self):
        self.environments = ['development', 'staging', 'production']
        self.deployment_strategies = {
            'blue_green': self.blue_green_deploy,
            'rolling': self.rolling_deploy,
            'canary': self.canary_deploy
        }

    def deploy_version(self, version, environment, strategy='blue_green'):
        """Deploy de nova versão"""
        if self.validate_version(version):
            return self.deployment_strategies[strategy](version, environment)
        else:
            raise ValueError("Versão inválida")
```

#### **Controle de Versão**
- **Semantic Versioning**: MAJOR.MINOR.PATCH
- **Git Flow**: Feature branches, develop, master
- **Tags**: Versionamento de releases
- **Changelog**: Documentação de mudanças
- **Migration Scripts**: Atualizações de banco

---

## 🎯 ROADMAP PARA NEXT.JS

### 🚀 FASE 1: SETUP E INFRAESTRUTURA (Semanas 1-2)

#### **Configuração Inicial**
```bash
# Criar projeto Next.js
npx create-next-app@latest rlponto-nextjs --typescript --tailwind --app

# Instalar dependências principais
npm install prisma @prisma/client
npm install next-auth
npm install @tanstack/react-query
npm install react-hook-form @hookform/resolvers zod
npm install recharts lucide-react
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu
```

#### **Estrutura de Pastas**
```
src/
├── app/                          # App Router (Next.js 13+)
│   ├── (auth)/                  # Grupo de rotas de autenticação
│   ├── (dashboard)/             # Grupo de rotas do dashboard
│   ├── api/                     # API Routes
│   ├── globals.css              # Estilos globais
│   ├── layout.tsx               # Layout raiz
│   └── page.tsx                 # Página inicial
├── components/                   # Componentes React
│   ├── ui/                      # Componentes base (shadcn/ui)
│   ├── forms/                   # Componentes de formulário
│   ├── tables/                  # Componentes de tabela
│   └── charts/                  # Componentes de gráfico
├── lib/                         # Utilitários
│   ├── auth.ts                  # Configuração NextAuth
│   ├── db.ts                    # Cliente Prisma
│   ├── validations.ts           # Schemas Zod
│   └── utils.ts                 # Funções utilitárias
├── hooks/                       # Custom hooks
├── types/                       # Definições TypeScript
└── styles/                      # Estilos adicionais
```

### 🔧 FASE 2: MIGRAÇÃO DO BACKEND (Semanas 3-6)

#### **API Routes Migration**
```typescript
// app/api/funcionarios/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { funcionarioSchema } from '@/lib/validations';

export async function GET(request: NextRequest) {
  try {
    const funcionarios = await prisma.funcionario.findMany({
      where: { ativo: true },
      include: { empresa: true }
    });

    return NextResponse.json(funcionarios);
  } catch (error) {
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = funcionarioSchema.parse(body);

    const funcionario = await prisma.funcionario.create({
      data: validatedData
    });

    return NextResponse.json(funcionario, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Dados inválidos' },
      { status: 400 }
    );
  }
}
```

#### **Database Schema (Prisma)**
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Funcionario {
  id              Int      @id @default(autoincrement())
  nomeCompleto    String   @map("nome_completo")
  cpf             String   @unique
  matriculaEmpresa String? @map("matricula_empresa")
  setor           String?
  cargo           String?
  empresa         String?
  dataNascimento  DateTime @map("data_nascimento")
  telefone        String?
  email           String?
  enderecoCompleto String? @map("endereco_completo")
  fotoUrl         String?  @map("foto_url")
  ativo           Boolean  @default(true)
  dataCadastro    DateTime @default(now()) @map("data_cadastro")
  dataAtualizacao DateTime @updatedAt @map("data_atualizacao")

  // Relacionamentos
  registrosPonto  RegistroPonto[]
  biometrias      BiometriaFuncionario[]

  @@map("funcionarios")
}

model RegistroPonto {
  id                Int      @id @default(autoincrement())
  funcionarioId     Int      @map("funcionario_id")
  dataHora          DateTime @map("data_hora")
  tipoRegistro      TipoRegistro @map("tipo_registro")
  metodoRegistro    MetodoRegistro @map("metodo_registro")
  qualidadeBiometria Int?    @map("qualidade_biometria")
  observacoes       String?
  ipOrigem          String?  @map("ip_origem")
  criadoPor         Int?     @map("criado_por")
  criadoEm          DateTime @default(now()) @map("criado_em")

  // Relacionamentos
  funcionario       Funcionario @relation(fields: [funcionarioId], references: [id])

  @@map("registros_ponto")
}

enum TipoRegistro {
  entrada_manha
  saida_almoco
  entrada_tarde
  saida
}

enum MetodoRegistro {
  biometrico
  manual
}
```

### 🎨 FASE 3: MIGRAÇÃO DO FRONTEND (Semanas 7-10)

#### **Componentes Base**
```typescript
// components/ui/data-table.tsx
import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  pagination?: boolean;
  sorting?: boolean;
  filtering?: boolean;
}

export function DataTable<T>({
  data,
  columns,
  pagination = true,
  sorting = true,
  filtering = true
}: DataTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof T;
    direction: 'asc' | 'desc';
  } | null>(null);

  // Implementação da tabela
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {/* Cabeçalhos da tabela */}
        </TableHeader>
        <TableBody>
          {/* Linhas da tabela */}
        </TableBody>
      </Table>
    </div>
  );
}
```

#### **Páginas Principais**
```typescript
// app/(dashboard)/funcionarios/page.tsx
import { Suspense } from 'react';
import { FuncionariosList } from '@/components/funcionarios/funcionarios-list';
import { FuncionariosFilters } from '@/components/funcionarios/funcionarios-filters';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';

export default function FuncionariosPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Funcionários</h1>
        <Button asChild>
          <Link href="/funcionarios/novo">
            <Plus className="mr-2 h-4 w-4" />
            Novo Funcionário
          </Link>
        </Button>
      </div>

      <FuncionariosFilters />

      <Suspense fallback={<div>Carregando...</div>}>
        <FuncionariosList />
      </Suspense>
    </div>
  );
}
```

### 📊 FASE 4: SISTEMA DE RELATÓRIOS (Semanas 11-12)

#### **Componentes de Gráficos**
```typescript
// components/charts/attendance-chart.tsx
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface AttendanceChartProps {
  data: Array<{
    date: string;
    present: number;
    absent: number;
    late: number;
  }>;
}

export function AttendanceChart({ data }: AttendanceChartProps) {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis />
        <Tooltip />
        <Line type="monotone" dataKey="present" stroke="#10b981" strokeWidth={2} />
        <Line type="monotone" dataKey="late" stroke="#f59e0b" strokeWidth={2} />
        <Line type="monotone" dataKey="absent" stroke="#ef4444" strokeWidth={2} />
      </LineChart>
    </ResponsiveContainer>
  );
}
```

### 🔐 FASE 5: AUTENTICAÇÃO E SEGURANÇA (Semanas 13-14)

#### **NextAuth Configuration**
```typescript
// lib/auth.ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { prisma } from '@/lib/db';
import { compare } from 'bcryptjs';

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        usuario: { label: 'Usuário', type: 'text' },
        senha: { label: 'Senha', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.usuario || !credentials?.senha) {
          return null;
        }

        const user = await prisma.usuario.findUnique({
          where: { usuario: credentials.usuario }
        });

        if (!user || !await compare(credentials.senha, user.senhaHash)) {
          return null;
        }

        return {
          id: user.id.toString(),
          name: user.nome,
          email: user.email,
          role: user.nivelAcesso
        };
      }
    })
  ],
  session: { strategy: 'jwt' },
  pages: {
    signIn: '/login'
  }
};

export default NextAuth(authOptions);
```

### 🧪 FASE 6: TESTES E QUALIDADE (Semanas 15-16)

#### **Configuração de Testes**
```typescript
// __tests__/funcionarios.test.tsx
import { render, screen } from '@testing-library/react';
import { FuncionariosList } from '@/components/funcionarios/funcionarios-list';

jest.mock('@/lib/db', () => ({
  funcionario: {
    findMany: jest.fn()
  }
}));

describe('FuncionariosList', () => {
  it('should render funcionarios list', async () => {
    const mockFuncionarios = [
      {
        id: 1,
        nomeCompleto: 'João Silva',
        cpf: '123.456.789-00',
        setor: 'TI'
      }
    ];

    (prisma.funcionario.findMany as jest.Mock).mockResolvedValue(mockFuncionarios);

    render(<FuncionariosList />);

    expect(await screen.findByText('João Silva')).toBeInTheDocument();
  });
});
```

---

**📝 Este documento completo serve como guia definitivo para migração do sistema RLPONTO-WEB de Flask/Python para Next.js/React, preservando toda a funcionalidade existente e implementando melhorias modernas de UX/UI e performance.**
