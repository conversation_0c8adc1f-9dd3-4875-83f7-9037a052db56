# 🔐 MÓDULO PONTO BIOMÉTRICO - Sistema RLPONTO

## 📋 Visão Geral
Mó<PERSON><PERSON> responsável pelo registro de ponto através de biometria digital e facial.

## 🎯 Funcionalidades
- Registro de ponto por biometria digital
- Registro de ponto por reconhecimento facial
- Validação em tempo real
- Detecção de fraudes
- Histórico de tentativas
- Integração com dispositivos biométricos
- Fallback para ponto manual

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── ponto/
│           ├── biometrico/
│           │   ├── page.tsx                # Página principal
│           │   └── components/
│           │       ├── biometric-scanner.tsx
│           │       ├── face-recognition.tsx
│           │       └── ponto-status.tsx
│           └── api/
│               └── biometrico/
│                   ├── route.ts            # API principal
│                   ├── digital/
│                   │   └── route.ts        # Biometria digital
│                   ├── facial/
│                   │   └── route.ts        # Reconhecimento facial
│                   └── validate/
│                       └── route.ts        # Validação
├── components/
│   └── ponto/
│       ├── biometric-capture.tsx          # Captura de biometria
│       ├── face-capture.tsx               # Captura facial
│       ├── ponto-timer.tsx                # Timer de ponto
│       └── device-status.tsx              # Status do dispositivo
└── lib/
    ├── biometric/
    │   ├── fingerprint.ts                 # SDK biometria digital
    │   ├── face-recognition.ts            # SDK reconhecimento facial
    │   └── device-manager.ts              # Gerenciamento de dispositivos
    └── validations/
        └── biometric.ts                   # Validações biométricas
```

## 🔧 Implementação Técnica

### 🖥️ Página Principal (page.tsx)
```typescript
// app/(dashboard)/ponto/biometrico/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { BiometricScanner } from './components/biometric-scanner';
import { PontoStatus } from './components/ponto-status';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Fingerprint, Camera, Clock } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Ponto Biométrico - RLPONTO',
  description: 'Registro de ponto através de biometria',
};

export default function PontoBiometricoPage() {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Fingerprint className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Ponto Biométrico</h1>
        </div>
        <p className="text-gray-600">Registre seu ponto usando biometria digital ou facial</p>
      </div>

      {/* Status do Ponto */}
      <Suspense fallback={<PontoStatusSkeleton />}>
        <PontoStatus />
      </Suspense>

      {/* Scanner Biométrico */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Biometria Digital */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Fingerprint className="h-5 w-5 text-blue-600" />
              <span>Biometria Digital</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BiometricScanner type="fingerprint" />
          </CardContent>
        </Card>

        {/* Reconhecimento Facial */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-green-600" />
              <span>Reconhecimento Facial</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BiometricScanner type="facial" />
          </CardContent>
        </Card>
      </div>

      {/* Histórico Recente */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-gray-600" />
            <span>Registros Recentes</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<HistoricoSkeleton />}>
            <HistoricoRecente />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function PontoStatusSkeleton() {
  return <div className="h-32 bg-gray-200 rounded-lg animate-pulse" />;
}

function HistoricoSkeleton() {
  return (
    <div className="space-y-2">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
      ))}
    </div>
  );
}
```

### 🔍 Scanner Biométrico (biometric-scanner.tsx)
```typescript
// app/(dashboard)/ponto/biometrico/components/biometric-scanner.tsx
'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Fingerprint, Camera, Loader2, CheckCircle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBiometricDevice } from '@/hooks/use-biometric-device';

interface BiometricScannerProps {
  type: 'fingerprint' | 'facial';
}

type ScanStatus = 'idle' | 'scanning' | 'processing' | 'success' | 'error';

export function BiometricScanner({ type }: BiometricScannerProps) {
  const [status, setStatus] = useState<ScanStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [funcionario, setFuncionario] = useState<any>(null);
  const scannerRef = useRef<HTMLDivElement>(null);

  const {
    device,
    isConnected,
    isLoading: deviceLoading,
    connect,
    disconnect,
  } = useBiometricDevice(type);

  useEffect(() => {
    // Auto-conectar ao dispositivo quando componente monta
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  const startScan = async () => {
    if (!isConnected) {
      setError('Dispositivo biométrico não conectado');
      return;
    }

    setStatus('scanning');
    setError(null);
    setProgress(0);

    try {
      // Simular progresso de scan
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 200);

      // Capturar biometria
      const biometricData = await captureBiometric(type);
      
      setStatus('processing');
      
      // Validar biometria no servidor
      const response = await fetch('/api/ponto/biometrico/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          data: biometricData,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Erro na validação biométrica');
      }

      const result = await response.json();

      if (result.success) {
        setFuncionario(result.funcionario);
        setStatus('success');
        
        // Registrar ponto
        await registrarPonto(result.funcionario.id, type, biometricData);
      } else {
        throw new Error(result.error || 'Biometria não reconhecida');
      }
    } catch (error) {
      console.error('Erro no scan biométrico:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
      setStatus('error');
    }
  };

  const captureBiometric = async (scanType: string): Promise<string> => {
    // Implementação específica para cada tipo de biometria
    if (scanType === 'fingerprint') {
      return await device?.captureFingerprint();
    } else {
      return await device?.captureFace();
    }
  };

  const registrarPonto = async (funcionarioId: number, scanType: string, biometricData: string) => {
    const response = await fetch('/api/ponto/registrar', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        funcionarioId,
        tipo: 'biometrico',
        metodo: scanType,
        biometricData,
        timestamp: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error('Erro ao registrar ponto');
    }
  };

  const resetScanner = () => {
    setStatus('idle');
    setProgress(0);
    setError(null);
    setFuncionario(null);
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'scanning':
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-600" />;
      case 'error':
        return <XCircle className="h-8 w-8 text-red-600" />;
      default:
        return type === 'fingerprint' 
          ? <Fingerprint className="h-8 w-8 text-gray-400" />
          : <Camera className="h-8 w-8 text-gray-400" />;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'scanning':
        return type === 'fingerprint' 
          ? 'Posicione o dedo no sensor...'
          : 'Posicione o rosto na câmera...';
      case 'processing':
        return 'Processando biometria...';
      case 'success':
        return `Ponto registrado com sucesso!`;
      case 'error':
        return error || 'Erro no reconhecimento';
      default:
        return `Clique para iniciar o ${type === 'fingerprint' ? 'scan digital' : 'reconhecimento facial'}`;
    }
  };

  if (deviceLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Conectando dispositivo...</span>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className="text-center space-y-4">
        <XCircle className="h-12 w-12 text-red-500 mx-auto" />
        <p className="text-red-600">Dispositivo biométrico não conectado</p>
        <Button onClick={connect} variant="outline">
          Tentar Reconectar
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Scanner Area */}
      <div
        ref={scannerRef}
        className={cn(
          'relative flex flex-col items-center justify-center h-64 border-2 border-dashed rounded-lg transition-colors',
          {
            'border-gray-300 bg-gray-50': status === 'idle',
            'border-blue-500 bg-blue-50': status === 'scanning',
            'border-yellow-500 bg-yellow-50': status === 'processing',
            'border-green-500 bg-green-50': status === 'success',
            'border-red-500 bg-red-50': status === 'error',
          }
        )}
      >
        {getStatusIcon()}
        <p className="mt-2 text-sm text-center text-gray-600">
          {getStatusMessage()}
        </p>

        {/* Progress Bar */}
        {(status === 'scanning' || status === 'processing') && (
          <div className="w-full max-w-xs mt-4">
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {/* Funcionário Identificado */}
        {status === 'success' && funcionario && (
          <div className="mt-4 p-3 bg-white rounded-lg border text-center">
            <p className="font-medium text-gray-900">{funcionario.nomeCompleto}</p>
            <p className="text-sm text-gray-600">{funcionario.cargo}</p>
            <p className="text-xs text-gray-500">
              {new Date().toLocaleString('pt-BR')}
            </p>
          </div>
        )}
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center space-x-2">
        {status === 'idle' && (
          <Button onClick={startScan} className="w-full">
            {type === 'fingerprint' ? 'Iniciar Scan Digital' : 'Iniciar Reconhecimento'}
          </Button>
        )}

        {(status === 'success' || status === 'error') && (
          <Button onClick={resetScanner} variant="outline" className="w-full">
            Novo Scan
          </Button>
        )}

        {status === 'scanning' && (
          <Button onClick={resetScanner} variant="outline" className="w-full">
            Cancelar
          </Button>
        )}
      </div>
    </div>
  );
}
```

### 📊 Status do Ponto (ponto-status.tsx)
```typescript
// app/(dashboard)/ponto/biometrico/components/ponto-status.tsx
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Calendar, MapPin, User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PontoAtual {
  funcionario: {
    id: number;
    nomeCompleto: string;
    cargo: string;
    setor: string;
  };
  ultimoRegistro?: {
    tipo: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim';
    timestamp: string;
    localizacao?: string;
  };
  statusAtual: 'fora' | 'trabalhando' | 'intervalo';
  horasTrabalhadas: string;
  proximoRegistro: 'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim';
}

export function PontoStatus() {
  const [pontoAtual, setPontoAtual] = useState<PontoAtual | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Atualizar hora atual a cada segundo
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    fetchPontoStatus();
  }, []);

  const fetchPontoStatus = async () => {
    try {
      const response = await fetch('/api/ponto/status');
      if (response.ok) {
        const data = await response.json();
        setPontoAtual(data);
      }
    } catch (error) {
      console.error('Erro ao buscar status do ponto:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trabalhando':
        return 'bg-green-100 text-green-800';
      case 'intervalo':
        return 'bg-yellow-100 text-yellow-800';
      case 'fora':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'trabalhando':
        return 'Trabalhando';
      case 'intervalo':
        return 'Em Intervalo';
      case 'fora':
        return 'Fora do Expediente';
      default:
        return 'Desconhecido';
    }
  };

  const getProximoRegistroLabel = (tipo: string) => {
    switch (tipo) {
      case 'entrada':
        return 'Próximo: Entrada';
      case 'saida':
        return 'Próximo: Saída';
      case 'intervalo_inicio':
        return 'Próximo: Início do Intervalo';
      case 'intervalo_fim':
        return 'Próximo: Fim do Intervalo';
      default:
        return 'Próximo: -';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-16 bg-gray-200 rounded"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Header com hora atual */}
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">
              {currentTime.toLocaleTimeString('pt-BR')}
            </div>
            <div className="text-sm text-gray-600">
              {currentTime.toLocaleDateString('pt-BR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </div>
          </div>

          {pontoAtual ? (
            <>
              {/* Status Atual */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-gray-600" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {pontoAtual.funcionario.nomeCompleto}
                    </p>
                    <p className="text-sm text-gray-600">
                      {pontoAtual.funcionario.cargo} - {pontoAtual.funcionario.setor}
                    </p>
                  </div>
                </div>
                <Badge className={cn('px-3 py-1', getStatusColor(pontoAtual.statusAtual))}>
                  {getStatusLabel(pontoAtual.statusAtual)}
                </Badge>
              </div>

              {/* Informações do Ponto */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Último Registro */}
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Último Registro</p>
                    <p className="text-xs text-gray-600">
                      {pontoAtual.ultimoRegistro
                        ? new Date(pontoAtual.ultimoRegistro.timestamp).toLocaleTimeString('pt-BR')
                        : 'Nenhum registro hoje'
                      }
                    </p>
                  </div>
                </div>

                {/* Horas Trabalhadas */}
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Calendar className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Horas Trabalhadas</p>
                    <p className="text-xs text-gray-600">{pontoAtual.horasTrabalhadas}</p>
                  </div>
                </div>

                {/* Próximo Registro */}
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <MapPin className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {getProximoRegistroLabel(pontoAtual.proximoRegistro)}
                    </p>
                    <p className="text-xs text-gray-600">
                      {pontoAtual.ultimoRegistro?.localizacao || 'Local não informado'}
                    </p>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nenhum funcionário identificado</p>
              <p className="text-sm text-gray-500">
                Use a biometria para identificar o funcionário
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

## 🔌 API Routes

### 🛡️ API de Validação Biométrica (validate/route.ts)
```typescript
// app/api/ponto/biometrico/validate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { validateFingerprint, validateFace } from '@/lib/biometric';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { type, data, timestamp } = await request.json();

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Tipo e dados biométricos são obrigatórios' },
        { status: 400 }
      );
    }

    let funcionario = null;

    // Validar biometria baseado no tipo
    if (type === 'fingerprint') {
      funcionario = await validateFingerprint(data);
    } else if (type === 'facial') {
      funcionario = await validateFace(data);
    } else {
      return NextResponse.json(
        { error: 'Tipo de biometria inválido' },
        { status: 400 }
      );
    }

    if (!funcionario) {
      // Log da tentativa de acesso
      await prisma.logAcesso.create({
        data: {
          tipo: 'biometrico_falhou',
          metodo: type,
          timestamp: new Date(timestamp),
          sucesso: false,
          detalhes: { biometricData: data.substring(0, 50) + '...' },
        },
      });

      return NextResponse.json({
        success: false,
        error: 'Biometria não reconhecida',
      });
    }

    // Log da tentativa bem-sucedida
    await prisma.logAcesso.create({
      data: {
        tipo: 'biometrico_sucesso',
        metodo: type,
        funcionarioId: funcionario.id,
        timestamp: new Date(timestamp),
        sucesso: true,
      },
    });

    return NextResponse.json({
      success: true,
      funcionario: {
        id: funcionario.id,
        nomeCompleto: funcionario.nomeCompleto,
        cargo: funcionario.cargo,
        setor: funcionario.setor,
        matriculaEmpresa: funcionario.matriculaEmpresa,
      },
    });
  } catch (error) {
    console.error('Erro na validação biométrica:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🧪 Testes

### ✅ Teste do Scanner Biométrico
```typescript
// __tests__/ponto/biometric-scanner.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BiometricScanner } from '@/app/(dashboard)/ponto/biometrico/components/biometric-scanner';

// Mock do hook de dispositivo biométrico
jest.mock('@/hooks/use-biometric-device', () => ({
  useBiometricDevice: () => ({
    device: {
      captureFingerprint: jest.fn().mockResolvedValue('fingerprint_data'),
      captureFace: jest.fn().mockResolvedValue('face_data'),
    },
    isConnected: true,
    isLoading: false,
    connect: jest.fn(),
    disconnect: jest.fn(),
  }),
}));

describe('BiometricScanner', () => {
  it('should start fingerprint scan', async () => {
    const mockFetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        funcionario: { id: 1, nomeCompleto: 'João Silva' },
      }),
    });
    global.fetch = mockFetch;

    render(<BiometricScanner type="fingerprint" />);

    fireEvent.click(screen.getByText(/iniciar scan digital/i));

    await waitFor(() => {
      expect(screen.getByText(/posicione o dedo no sensor/i)).toBeInTheDocument();
    });
  });

  it('should handle scan error', async () => {
    const mockFetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: false,
        error: 'Biometria não reconhecida',
      }),
    });
    global.fetch = mockFetch;

    render(<BiometricScanner type="fingerprint" />);

    fireEvent.click(screen.getByText(/iniciar scan digital/i));

    await waitFor(() => {
      expect(screen.getByText(/biometria não reconhecida/i)).toBeInTheDocument();
    });
  });
});
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Scanner de biometria digital
- [ ] Reconhecimento facial
- [ ] Validação em tempo real
- [ ] Status do ponto atual
- [ ] Histórico de registros
- [ ] Detecção de fraudes
- [ ] Logs de auditoria
- [ ] Fallback para ponto manual

### 🔧 Integrações
- [ ] SDK de biometria digital
- [ ] SDK de reconhecimento facial
- [ ] Gerenciamento de dispositivos
- [ ] Validação de qualidade biométrica
- [ ] Criptografia de dados biométricos

## 🚀 Próximos Passos
1. **Ponto Manual** - Implementar fallback manual
2. **Relatórios** - Análise de registros biométricos
3. **Administração** - Configurações de dispositivos

## 🔗 Integração com Dispositivos

### 📱 Dispositivos Suportados
- **Nitgen Hamster Plus** - Leitor biométrico USB
- **ZKTeco U160** - Leitor biométrico com display
- **Suprema BioMini Plus 2** - Scanner compacto
- **Câmeras IP** - Para reconhecimento facial
- **Tablets Android/iOS** - App móvel com biometria

### 🔧 Configuração de Hardware
```typescript
// lib/biometric/device-config.ts
export const DEVICE_CONFIGS = {
  nitgen_hamster: {
    vendor: 'Nitgen',
    model: 'Hamster Plus',
    driver: 'nitgen-sdk',
    capabilities: ['fingerprint'],
    resolution: '500 DPI',
  },
  zkteco_u160: {
    vendor: 'ZKTeco',
    model: 'U160',
    driver: 'zkteco-sdk',
    capabilities: ['fingerprint', 'rfid'],
    resolution: '500 DPI',
  },
  suprema_biomini: {
    vendor: 'Suprema',
    model: 'BioMini Plus 2',
    driver: 'suprema-sdk',
    capabilities: ['fingerprint'],
    resolution: '500 DPI',
  },
};
```
