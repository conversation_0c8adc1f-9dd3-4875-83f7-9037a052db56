# 🚀 Guia de Instalação - Sistema RLPONTO

## 📋 Visão Geral

Este documento fornece instruções detalhadas para configurar o ambiente de desenvolvimento do Sistema RLPONTO, incluindo requisitos, instalação e configuração.

## 💻 Requisitos do Sistema

### Requisitos Mínimos
- **Sistema Operacional**: Windows 10+, macOS 12+, Ubuntu 20.04+
- **Memória RAM**: 8GB (recomendado 16GB)
- **Espaço em Disco**: 10GB livres
- **Processador**: Intel i5 ou AMD Ryzen 5 (ou equivalente)
- **Conexão**: Internet banda larga

### Software Necessário
- **Node.js**: 18.0 ou superior
- **npm**: 9.0 ou superior (ou yarn 3.0+)
- **MySQL**: 8.0 ou superior
- **Git**: 2.30 ou superior
- **VS Code**: Última versão (recomendado)

### Verificação de Requisitos
```bash
# Verificar versões instaladas
node --version    # Deve ser >= 18.0
npm --version     # Deve ser >= 9.0
mysql --version   # Deve ser >= 8.0
git --version     # Deve ser >= 2.30
```

## 🛠️ Instalação do Ambiente

### 1. Instalação do Node.js
```bash
# Via Node Version Manager (recomendado)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Ou baixar diretamente de https://nodejs.org/
```

### 2. Instalação do MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server mysql-client

# macOS (via Homebrew)
brew install mysql

# Windows
# Baixar MySQL Installer de https://dev.mysql.com/downloads/installer/
```

### 3. Configuração do MySQL
```sql
-- Conectar ao MySQL como root
mysql -u root -p

-- Criar banco de dados
CREATE DATABASE rlponto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Criar usuário para a aplicação
CREATE USER 'rlponto_user'@'localhost' IDENTIFIED BY 'senha_segura_aqui';
GRANT ALL PRIVILEGES ON rlponto.* TO 'rlponto_user'@'localhost';
FLUSH PRIVILEGES;

-- Verificar criação
SHOW DATABASES;
SELECT User, Host FROM mysql.user WHERE User = 'rlponto_user';
```

### 4. Instalação do Git
```bash
# Ubuntu/Debian
sudo apt install git

# macOS (via Homebrew)
brew install git

# Windows
# Baixar de https://git-scm.com/download/win
```

## 📦 Clonagem e Configuração do Projeto

### 1. Clonar o Repositório
```bash
# Clonar o projeto
git clone https://github.com/sua-empresa/rlponto-system.git
cd rlponto-system

# Verificar estrutura
ls -la
```

### 2. Instalação das Dependências
```bash
# Instalar dependências do projeto
npm install

# Ou usando yarn
yarn install

# Verificar instalação
npm list --depth=0
```

### 3. Configuração das Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp .env.example .env.local

# Editar variáveis de ambiente
nano .env.local
```

```env
# .env.local - Configurações de desenvolvimento

# Database
DATABASE_URL="mysql://rlponto_user:senha_segura_aqui@localhost:3306/rlponto"

# NextAuth.js
NEXTAUTH_SECRET="seu-secret-super-seguro-aqui-min-32-chars"
NEXTAUTH_URL="http://localhost:3000"

# Email (opcional para desenvolvimento)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="sua-senha-app"
EMAIL_FROM="<EMAIL>"

# Upload de arquivos
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880  # 5MB

# Logs
LOG_LEVEL="debug"
LOG_FILE="./logs/app.log"

# Desenvolvimento
NODE_ENV="development"
DEBUG="rlponto:*"
```

### 4. Configuração do Banco de Dados
```bash
# Gerar cliente Prisma
npx prisma generate

# Executar migrações
npx prisma migrate dev --name init

# Verificar migrações
npx prisma migrate status

# Seed inicial (opcional)
npx prisma db seed
```

### 5. Verificação da Instalação
```bash
# Executar testes
npm test

# Verificar build
npm run build

# Iniciar em modo desenvolvimento
npm run dev
```

## 🔧 Configurações Adicionais

### VS Code - Extensões Recomendadas
```json
// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "prisma.prisma",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### VS Code - Configurações do Workspace
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  }
}
```

### Configuração do Prettier
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### Configuração do ESLint
```json
// .eslintrc.json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

## 🐳 Configuração com Docker (Opcional)

### Docker Compose para Desenvolvimento
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://rlponto_user:senha123@db:3306/rlponto
    depends_on:
      - db
      - redis

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: rlponto
      MYSQL_USER: rlponto_user
      MYSQL_PASSWORD: senha123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

### Dockerfile para Desenvolvimento
```dockerfile
# Dockerfile.dev
FROM node:18-alpine

WORKDIR /app

# Instalar dependências
COPY package*.json ./
RUN npm ci

# Copiar código fonte
COPY . .

# Gerar cliente Prisma
RUN npx prisma generate

EXPOSE 3000

CMD ["npm", "run", "dev"]
```

### Comandos Docker
```bash
# Iniciar ambiente com Docker
docker-compose -f docker-compose.dev.yml up -d

# Verificar status
docker-compose -f docker-compose.dev.yml ps

# Ver logs
docker-compose -f docker-compose.dev.yml logs -f app

# Parar ambiente
docker-compose -f docker-compose.dev.yml down
```

## 🧪 Configuração de Testes

### Jest Configuration
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
  ],
}

module.exports = createJestConfig(customJestConfig)
```

### Setup de Testes
```javascript
// jest.setup.js
import '@testing-library/jest-dom'

// Mock do NextAuth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: null,
    status: 'unauthenticated',
  })),
  signIn: jest.fn(),
  signOut: jest.fn(),
}))

// Mock do Prisma
jest.mock('@/lib/db', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    // ... outros modelos
  },
}))
```

## 📋 Comandos Úteis

### Desenvolvimento
```bash
# Iniciar servidor de desenvolvimento
npm run dev

# Build para produção
npm run build

# Iniciar servidor de produção
npm start

# Executar testes
npm test

# Executar testes em modo watch
npm run test:watch

# Verificar cobertura de testes
npm run test:coverage
```

### Banco de Dados
```bash
# Gerar cliente Prisma
npx prisma generate

# Executar migrações
npx prisma migrate dev

# Reset do banco de dados
npx prisma migrate reset

# Visualizar banco de dados
npx prisma studio

# Seed do banco de dados
npx prisma db seed
```

### Qualidade de Código
```bash
# Verificar lint
npm run lint

# Corrigir problemas de lint
npm run lint:fix

# Formatar código
npm run format

# Verificar tipos TypeScript
npm run type-check
```

### Docker
```bash
# Build da imagem
docker build -t rlponto-app .

# Executar container
docker run -p 3000:3000 rlponto-app

# Executar com Docker Compose
docker-compose up -d

# Ver logs
docker-compose logs -f
```

## 🔍 Troubleshooting

### Problemas Comuns

#### Erro de Conexão com MySQL
```bash
# Verificar se MySQL está rodando
sudo systemctl status mysql

# Reiniciar MySQL
sudo systemctl restart mysql

# Verificar conexão
mysql -u rlponto_user -p -h localhost rlponto
```

#### Erro de Permissões no Node.js
```bash
# Corrigir permissões do npm
npm config set prefix ~/.npm-global
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

#### Erro de Porta em Uso
```bash
# Verificar processo usando a porta 3000
lsof -i :3000

# Matar processo
kill -9 <PID>

# Ou usar porta diferente
PORT=3001 npm run dev
```

#### Problemas com Prisma
```bash
# Limpar cache do Prisma
npx prisma generate --force

# Verificar schema
npx prisma validate

# Debug de migrações
npx prisma migrate status --verbose
```

### Logs de Debug
```bash
# Habilitar logs detalhados
DEBUG=* npm run dev

# Logs específicos do Prisma
DEBUG=prisma:* npm run dev

# Logs da aplicação
DEBUG=rlponto:* npm run dev
```

## 📞 Suporte

### Recursos de Ajuda
- **Documentação**: [Link para docs]
- **Issues**: [Link para GitHub Issues]
- **Discord**: [Link para servidor Discord]
- **Email**: <EMAIL>

### Informações para Suporte
Ao reportar problemas, inclua:
- Versão do Node.js: `node --version`
- Versão do npm: `npm --version`
- Sistema operacional
- Logs de erro completos
- Passos para reproduzir o problema

---

**Documento criado em**: [Data]  
**Última atualização**: [Data]  
**Versão**: 1.0  
**Responsável**: [Nome do DevOps/Tech Lead]
