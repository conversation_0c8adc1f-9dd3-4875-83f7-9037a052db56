import { <PERSON>ada<PERSON> } from 'next';
import { 
  BarChart3, 
  ArrowLeft, 
  Users, 
  Building, 
  Calendar,
  Target,
  Download,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Aná<PERSON>e Comparativa - RLPONTO',
  description: 'Comparação entre departamentos, períodos e funcionários',
};

// Dados mockados para demonstração
const comparativosData = {
  departamentos: [
    { nome: 'RH', funcionarios: 8, pontualidade: 98.1, produtividade: 93.2, absenteismo: 1.5, ranking: 1 },
    { nome: 'Vendas', funcionarios: 25, pontualidade: 96.2, produtividade: 91.8, absenteismo: 2.1, ranking: 2 },
    { nome: 'Financeiro', funcionarios: 12, pontualidade: 93.7, produtividade: 89.4, absenteismo: 3.8, ranking: 3 },
    { nome: 'TI', funcionarios: 18, pontualidade: 91.8, produtividade: 87.6, absenteismo: 4.2, ranking: 4 },
    { nome: 'Operações', funcionarios: 35, pontualidade: 89.4, produtividade: 84.3, absenteismo: 5.2, ranking: 5 }
  ],
  periodos: [
    { periodo: 'Este Mês', pontualidade: 94.2, produtividade: 87.6, absenteismo: 3.8, horasExtras: 127.5 },
    { periodo: 'Mês Anterior', pontualidade: 92.1, produtividade: 85.3, absenteismo: 4.3, horasExtras: 112.3 },
    { periodo: 'Mesmo Mês Ano Anterior', pontualidade: 89.7, produtividade: 82.1, absenteismo: 5.1, horasExtras: 98.7 },
    { periodo: 'Média Anual', pontualidade: 91.8, produtividade: 84.9, absenteismo: 4.5, horasExtras: 108.2 }
  ],
  topFuncionarios: [
    { nome: 'Ana Silva', departamento: 'Vendas', score: 96.2, pontualidade: 98.5, produtividade: 94.8 },
    { nome: 'Carlos Santos', departamento: 'TI', score: 94.8, pontualidade: 96.2, produtividade: 93.4 },
    { nome: 'Maria Oliveira', departamento: 'RH', score: 93.5, pontualidade: 97.1, produtividade: 89.9 },
    { nome: 'João Costa', departamento: 'Financeiro', score: 92.1, pontualidade: 94.8, produtividade: 89.4 },
    { nome: 'Pedro Lima', departamento: 'Operações', score: 91.7, pontualidade: 93.2, produtividade: 90.2 }
  ],
  benchmarks: {
    industria: { pontualidade: 88.5, produtividade: 82.3, absenteismo: 6.2 },
    empresa: { pontualidade: 94.2, produtividade: 87.6, absenteismo: 3.8 },
    melhorPratica: { pontualidade: 97.0, produtividade: 92.0, absenteismo: 2.0 }
  }
};

export default function ComparativosPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Link href="/estatisticas">
                <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </button>
              </Link>
              <div className="p-2 bg-yellow-600 rounded-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Análise Comparativa</h1>
                <p className="text-gray-600">Benchmarking e comparação de performance</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option>Últimos 30 dias</option>
                <option>Últimos 90 dias</option>
                <option>Último ano</option>
              </select>
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>

          {/* Benchmark da Indústria */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Benchmark da Indústria</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Target className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-blue-800">Média da Indústria</h3>
                  <div className="mt-2 space-y-1">
                    <p className="text-sm">Pontualidade: {comparativosData.benchmarks.industria.pontualidade}%</p>
                    <p className="text-sm">Produtividade: {comparativosData.benchmarks.industria.produtividade}%</p>
                    <p className="text-sm">Absenteísmo: {comparativosData.benchmarks.industria.absenteismo}%</p>
                  </div>
                </div>

                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Building className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-green-800">Nossa Empresa</h3>
                  <div className="mt-2 space-y-1">
                    <p className="text-sm font-medium text-green-700">
                      Pontualidade: {comparativosData.benchmarks.empresa.pontualidade}%
                      <TrendingUp className="inline h-3 w-3 ml-1" />
                    </p>
                    <p className="text-sm font-medium text-green-700">
                      Produtividade: {comparativosData.benchmarks.empresa.produtividade}%
                      <TrendingUp className="inline h-3 w-3 ml-1" />
                    </p>
                    <p className="text-sm font-medium text-green-700">
                      Absenteísmo: {comparativosData.benchmarks.empresa.absenteismo}%
                      <TrendingDown className="inline h-3 w-3 ml-1" />
                    </p>
                  </div>
                </div>

                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-purple-800">Melhor Prática</h3>
                  <div className="mt-2 space-y-1">
                    <p className="text-sm">Pontualidade: {comparativosData.benchmarks.melhorPratica.pontualidade}%</p>
                    <p className="text-sm">Produtividade: {comparativosData.benchmarks.melhorPratica.produtividade}%</p>
                    <p className="text-sm">Absenteísmo: {comparativosData.benchmarks.melhorPratica.absenteismo}%</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Ranking de Departamentos */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Ranking de Departamentos</h2>
            </div>
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ranking
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Departamento
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Funcionários
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pontualidade
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Produtividade
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Absenteísmo
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {comparativosData.departamentos.map((dept, index) => (
                      <tr key={index} className={index < 3 ? 'bg-green-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                            index === 0 ? 'bg-yellow-400 text-yellow-900' :
                            index === 1 ? 'bg-gray-300 text-gray-700' :
                            index === 2 ? 'bg-orange-300 text-orange-900' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {dept.ranking}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {dept.nome}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.funcionarios}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            dept.pontualidade >= 95 ? 'bg-green-100 text-green-800' :
                            dept.pontualidade >= 90 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.pontualidade}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            dept.produtividade >= 90 ? 'bg-green-100 text-green-800' :
                            dept.produtividade >= 85 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.produtividade}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            dept.absenteismo <= 2 ? 'bg-green-100 text-green-800' :
                            dept.absenteismo <= 4 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.absenteismo}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Comparação Temporal */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Comparação Temporal</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {comparativosData.periodos.map((periodo, index) => (
                    <div key={index} className={`p-3 rounded-lg border ${
                      index === 0 ? 'bg-blue-50 border-blue-200' : 'border-gray-200'
                    }`}>
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-sm font-medium text-gray-900">{periodo.periodo}</h3>
                        {index === 0 && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            Atual
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>Pontualidade: {periodo.pontualidade}%</div>
                        <div>Produtividade: {periodo.produtividade}%</div>
                        <div>Absenteísmo: {periodo.absenteismo}%</div>
                        <div>H. Extras: {periodo.horasExtras}h</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Top Funcionários */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Top Funcionários</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {comparativosData.topFuncionarios.map((funcionario, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          index === 0 ? 'bg-yellow-400 text-yellow-900' :
                          index === 1 ? 'bg-gray-300 text-gray-700' :
                          index === 2 ? 'bg-orange-300 text-orange-900' :
                          'bg-gray-100 text-gray-600'
                        }`}>
                          {index + 1}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{funcionario.nome}</p>
                          <p className="text-xs text-gray-500">{funcionario.departamento}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">Score: {funcionario.score}%</p>
                        <p className="text-xs text-gray-500">
                          P: {funcionario.pontualidade}% | Prod: {funcionario.produtividade}%
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Gráfico Comparativo (Placeholder) */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Comparação Visual</h2>
            </div>
            <div className="p-6">
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Gráfico de Barras Comparativo</p>
                  <p className="text-sm text-gray-400">Implementação com biblioteca de gráficos</p>
                  <p className="text-xs text-gray-400 mt-2">
                    Comparação visual entre departamentos e períodos
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Insights Comparativos */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Insights Comparativos</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Destaque Positivo</h3>
                      <p className="text-sm text-green-700 mt-1">
                        O departamento de RH lidera em todas as métricas, sendo referência 
                        para os demais setores da empresa.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <Target className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Oportunidade</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Nossa empresa está 5.7% acima da média da indústria em pontualidade, 
                        mas ainda há espaço para melhoria.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

