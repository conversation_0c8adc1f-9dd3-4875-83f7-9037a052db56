# 🔒 MÓDULO FECHAMENTO - Sistema RLPONTO

## 📋 Visão Geral
Módulo responsável pelo processo de fechamento mensal do ponto, incluindo validações, cálculos finais, geração de relatórios e bloqueio de alterações.

## 🎯 Funcionalidades
- Fechamento mensal automatizado
- Validações pré-fechamento
- Cálculo de totalizadores
- Geração de relatórios finais
- Bloqueio de alterações
- Reabertura controlada
- Exportação para folha de pagamento
- Auditoria completa
- Notificações automáticas

## 🏗️ Estrutura de Arquivos Next.js

### 📁 Estrutura de Pastas
```
src/
├── app/
│   └── (dashboard)/
│       └── fechamento/
│           ├── page.tsx                    # Dashboard de fechamento
│           ├── periodo/
│           │   └── [periodo]/
│           │       ├── page.tsx            # Fechamento específico
│           │       ├── validacoes/
│           │       │   └── page.tsx        # Validações
│           │       └── relatorios/
│           │           └── page.tsx        # Relatórios finais
│           └── components/
│               ├── closing-wizard.tsx      # Wizard de fechamento
│               ├── validation-panel.tsx    # Painel de validações
│               ├── totals-summary.tsx      # Resumo de totais
│               └── export-panel.tsx        # Painel de exportação
├── components/
│   └── fechamento/
│       ├── period-selector.tsx            # Seletor de período
│       ├── validation-item.tsx            # Item de validação
│       ├── closing-status.tsx             # Status do fechamento
│       └── reopen-modal.tsx               # Modal de reabertura
└── api/
    └── fechamento/
        ├── route.ts                       # API principal
        ├── [periodo]/
        │   ├── route.ts                   # Fechamento específico
        │   ├── validar/
        │   │   └── route.ts               # Validações
        │   ├── fechar/
        │   │   └── route.ts               # Executar fechamento
        │   └── reabrir/
        │       └── route.ts               # Reabertura
        └── export/
            └── route.ts                   # Exportação
```

## 🔧 Implementação Técnica

### 🔒 Dashboard de Fechamento (page.tsx)
```typescript
// app/(dashboard)/fechamento/page.tsx
import { Metadata } from 'next/metadata';
import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lock, Unlock, Calendar, CheckCircle, AlertTriangle, FileText, Download } from 'lucide-react';
import Link from 'next/link';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export const metadata: Metadata = {
  title: 'Fechamento - RLPONTO',
  description: 'Fechamento mensal do ponto eletrônico',
};

interface PeriodoFechamento {
  periodo: string;
  status: 'aberto' | 'fechado' | 'processando';
  dataFechamento?: string;
  totalFuncionarios: number;
  totalRegistros: number;
  pendencias: number;
  fechadoPor?: string;
}

export default function FechamentoPage() {
  // Simular dados dos últimos 6 meses
  const periodos: PeriodoFechamento[] = [
    {
      periodo: '2024-01',
      status: 'fechado',
      dataFechamento: '2024-02-05T10:30:00Z',
      totalFuncionarios: 150,
      totalRegistros: 3200,
      pendencias: 0,
      fechadoPor: 'Admin Sistema',
    },
    {
      periodo: '2024-02',
      status: 'fechado',
      dataFechamento: '2024-03-05T14:15:00Z',
      totalFuncionarios: 152,
      totalRegistros: 3250,
      pendencias: 0,
      fechadoPor: 'Admin Sistema',
    },
    {
      periodo: '2024-03',
      status: 'aberto',
      totalFuncionarios: 155,
      totalRegistros: 3180,
      pendencias: 12,
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      aberto: { label: 'Aberto', icon: Unlock, color: 'bg-green-100 text-green-800' },
      fechado: { label: 'Fechado', icon: Lock, color: 'bg-gray-100 text-gray-800' },
      processando: { label: 'Processando', icon: Calendar, color: 'bg-blue-100 text-blue-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.aberto;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const formatPeriod = (periodo: string) => {
    const [year, month] = periodo.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return format(date, 'MMMM yyyy', { locale: ptBR });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Lock className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Fechamento</h1>
            <p className="text-gray-600">Fechamento mensal do ponto eletrônico</p>
          </div>
        </div>
        <Button asChild>
          <Link href="/fechamento/novo">
            <Lock className="h-4 w-4 mr-2" />
            Novo Fechamento
          </Link>
        </Button>
      </div>

      {/* Status Geral */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Período Atual</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Março 2024</div>
            <p className="text-xs text-muted-foreground">Em andamento</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendências</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">12</div>
            <p className="text-xs text-muted-foreground">registros pendentes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Funcionários</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">155</div>
            <p className="text-xs text-muted-foreground">funcionários ativos</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Registros</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3,180</div>
            <p className="text-xs text-muted-foreground">registros no mês</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Períodos */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Fechamentos</CardTitle>
          <CardDescription>
            Visualize e gerencie os fechamentos mensais
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {periodos.map((periodo) => (
              <div
                key={periodo.periodo}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center space-x-4">
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {formatPeriod(periodo.periodo)}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <span>{periodo.totalFuncionarios} funcionários</span>
                      <span>{periodo.totalRegistros.toLocaleString()} registros</span>
                      {periodo.pendencias > 0 && (
                        <span className="text-orange-600">
                          {periodo.pendencias} pendências
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {getStatusBadge(periodo.status)}
                  
                  {periodo.status === 'fechado' && periodo.dataFechamento && (
                    <div className="text-sm text-gray-500 text-right">
                      <div>Fechado em {format(new Date(periodo.dataFechamento), 'dd/MM/yyyy HH:mm')}</div>
                      <div>por {periodo.fechadoPor}</div>
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/fechamento/periodo/${periodo.periodo}`}>
                        <FileText className="h-4 w-4 mr-1" />
                        Detalhes
                      </Link>
                    </Button>
                    
                    {periodo.status === 'fechado' && (
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-1" />
                        Exportar
                      </Button>
                    )}
                    
                    {periodo.status === 'aberto' && periodo.pendencias === 0 && (
                      <Button size="sm" asChild>
                        <Link href={`/fechamento/periodo/${periodo.periodo}/fechar`}>
                          <Lock className="h-4 w-4 mr-1" />
                          Fechar
                        </Link>
                      </Button>
                    )}
                    
                    {periodo.status === 'fechado' && (
                      <Button variant="destructive" size="sm">
                        <Unlock className="h-4 w-4 mr-1" />
                        Reabrir
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Ações Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Validações</CardTitle>
            <CardDescription>Verificar pendências antes do fechamento</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/fechamento/validacoes">
                <CheckCircle className="h-4 w-4 mr-2" />
                Executar Validações
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Relatórios</CardTitle>
            <CardDescription>Gerar relatórios do período</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/fechamento/relatorios">
                <FileText className="h-4 w-4 mr-2" />
                Gerar Relatórios
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Exportação</CardTitle>
            <CardDescription>Exportar dados para folha</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline" asChild>
              <Link href="/fechamento/exportar">
                <Download className="h-4 w-4 mr-2" />
                Exportar Dados
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

### 🧙‍♂️ Wizard de Fechamento (closing-wizard.tsx)
```typescript
// app/(dashboard)/fechamento/components/closing-wizard.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle, Clock, Lock, FileText, Download } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ValidationItem {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'error';
  details?: string;
  count?: number;
}

interface ClosingWizardProps {
  periodo: string;
}

export function ClosingWizard({ periodo }: ClosingWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [validations, setValidations] = useState<ValidationItem[]>([
    {
      id: 'funcionarios',
      title: 'Funcionários Ativos',
      description: 'Verificar funcionários com registros no período',
      status: 'pending',
    },
    {
      id: 'registros',
      title: 'Registros de Ponto',
      description: 'Validar integridade dos registros',
      status: 'pending',
    },
    {
      id: 'classificacao',
      title: 'Classificação de Horas',
      description: 'Verificar se todas as horas foram classificadas',
      status: 'pending',
    },
    {
      id: 'aprovacoes',
      title: 'Aprovações Pendentes',
      description: 'Verificar registros pendentes de aprovação',
      status: 'pending',
    },
    {
      id: 'calculos',
      title: 'Cálculos Finais',
      description: 'Executar cálculos de totalizadores',
      status: 'pending',
    },
  ]);

  const router = useRouter();

  const steps = [
    { title: 'Validações', description: 'Verificar pendências' },
    { title: 'Cálculos', description: 'Processar totalizadores' },
    { title: 'Relatórios', description: 'Gerar relatórios finais' },
    { title: 'Fechamento', description: 'Finalizar processo' },
  ];

  useEffect(() => {
    if (currentStep === 0) {
      runValidations();
    }
  }, [currentStep]);

  const runValidations = async () => {
    setIsProcessing(true);

    for (let i = 0; i < validations.length; i++) {
      // Atualizar status para "running"
      setValidations(prev => prev.map((v, index) => 
        index === i ? { ...v, status: 'running' } : v
      ));

      try {
        // Simular chamada da API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const response = await fetch(`/api/fechamento/${periodo}/validar`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ validationId: validations[i].id }),
        });

        const result = await response.json();

        setValidations(prev => prev.map((v, index) => 
          index === i ? { 
            ...v, 
            status: result.success ? 'success' : 'error',
            details: result.message,
            count: result.count,
          } : v
        ));

      } catch (error) {
        setValidations(prev => prev.map((v, index) => 
          index === i ? { 
            ...v, 
            status: 'error',
            details: 'Erro ao executar validação',
          } : v
        ));
      }
    }

    setIsProcessing(false);
  };

  const getValidationIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'running':
        return <Clock className="h-5 w-5 text-blue-600 animate-spin" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getValidationBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Pendente', color: 'bg-gray-100 text-gray-800' },
      running: { label: 'Executando', color: 'bg-blue-100 text-blue-800' },
      success: { label: 'Sucesso', color: 'bg-green-100 text-green-800' },
      error: { label: 'Erro', color: 'bg-red-100 text-red-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const canProceed = () => {
    return validations.every(v => v.status === 'success') && !isProcessing;
  };

  const hasErrors = () => {
    return validations.some(v => v.status === 'error');
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleClose = async () => {
    setIsProcessing(true);
    
    try {
      const response = await fetch(`/api/fechamento/${periodo}/fechar`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        router.push('/fechamento?success=true');
      } else {
        throw new Error('Erro ao fechar período');
      }
    } catch (error) {
      console.error('Erro ao fechar período:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Fechamento do Período: {periodo}</CardTitle>
          <CardDescription>
            Processo de fechamento mensal - Etapa {currentStep + 1} de {steps.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={(currentStep / (steps.length - 1)) * 100} className="w-full" />
            <div className="flex justify-between text-sm text-gray-600">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`text-center ${
                    index <= currentStep ? 'text-blue-600 font-medium' : 'text-gray-400'
                  }`}
                >
                  <div>{step.title}</div>
                  <div className="text-xs">{step.description}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {currentStep === 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Validações Pré-Fechamento</CardTitle>
            <CardDescription>
              Verificando integridade dos dados antes do fechamento
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {validations.map((validation) => (
                <div
                  key={validation.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {getValidationIcon(validation.status)}
                    <div>
                      <h4 className="font-medium">{validation.title}</h4>
                      <p className="text-sm text-gray-600">{validation.description}</p>
                      {validation.details && (
                        <p className="text-xs text-gray-500 mt-1">{validation.details}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {validation.count !== undefined && (
                      <span className="text-sm text-gray-600">{validation.count}</span>
                    )}
                    {getValidationBadge(validation.status)}
                  </div>
                </div>
              ))}

              {hasErrors() && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Existem erros que precisam ser corrigidos antes de prosseguir com o fechamento.
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => router.back()}>
                  Cancelar
                </Button>
                <Button 
                  onClick={handleNext} 
                  disabled={!canProceed()}
                >
                  Próximo
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {currentStep === 3 && (
        <Card>
          <CardHeader>
            <CardTitle>Confirmar Fechamento</CardTitle>
            <CardDescription>
              Confirme o fechamento do período. Esta ação não pode ser desfeita facilmente.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert>
                <Lock className="h-4 w-4" />
                <AlertDescription>
                  Após o fechamento, os registros do período ficarão bloqueados para edição.
                  Apenas administradores poderão reabrir o período se necessário.
                </AlertDescription>
              </Alert>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>
                  Voltar
                </Button>
                <Button 
                  onClick={handleClose}
                  disabled={isProcessing}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isProcessing ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Fechando...
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4 mr-2" />
                      Confirmar Fechamento
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
```

## 🔌 API Routes

### 🔒 API de Fechamento (route.ts)
```typescript
// app/api/fechamento/[periodo]/fechar/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { startOfMonth, endOfMonth, parseISO } from 'date-fns';

export async function POST(
  request: NextRequest,
  { params }: { params: { periodo: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { periodo } = params;
    const [year, month] = periodo.split('-');
    const dataInicio = startOfMonth(new Date(parseInt(year), parseInt(month) - 1));
    const dataFim = endOfMonth(new Date(parseInt(year), parseInt(month) - 1));

    // Verificar se já está fechado
    const fechamentoExistente = await prisma.fechamentoMensal.findFirst({
      where: {
        periodo,
        status: 'fechado',
      },
    });

    if (fechamentoExistente) {
      return NextResponse.json(
        { error: 'Período já está fechado' },
        { status: 400 }
      );
    }

    // Executar validações finais
    const validacoes = await executarValidacoes(dataInicio, dataFim);
    
    if (validacoes.some(v => !v.success)) {
      return NextResponse.json(
        { error: 'Existem validações pendentes', validacoes },
        { status: 400 }
      );
    }

    // Calcular totalizadores
    const totalizadores = await calcularTotalizadores(dataInicio, dataFim);

    // Criar registro de fechamento
    const fechamento = await prisma.fechamentoMensal.create({
      data: {
        periodo,
        dataInicio,
        dataFim,
        status: 'fechado',
        totalFuncionarios: totalizadores.totalFuncionarios,
        totalRegistros: totalizadores.totalRegistros,
        totalHorasNormais: totalizadores.totalHorasNormais,
        totalHorasExtras: totalizadores.totalHorasExtras,
        totalFaltas: totalizadores.totalFaltas,
        fechadoPorId: parseInt(session.user.id),
        dataFechamento: new Date(),
        observacoes: 'Fechamento automático via sistema',
      },
    });

    // Bloquear registros do período
    await prisma.registroPonto.updateMany({
      where: {
        data: {
          gte: dataInicio,
          lte: dataFim,
        },
      },
      data: {
        bloqueado: true,
        fechamentoId: fechamento.id,
      },
    });

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        acao: 'fechamento_mensal',
        usuarioId: parseInt(session.user.id),
        detalhes: JSON.stringify({
          periodo,
          fechamentoId: fechamento.id,
          totalizadores,
        }),
        timestamp: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      fechamento,
      totalizadores,
    });

  } catch (error) {
    console.error('Erro ao fechar período:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

async function executarValidacoes(dataInicio: Date, dataFim: Date) {
  const validacoes = [];

  // Validar funcionários ativos
  const funcionariosAtivos = await prisma.funcionario.count({
    where: { ativo: true },
  });
  validacoes.push({
    id: 'funcionarios',
    success: funcionariosAtivos > 0,
    count: funcionariosAtivos,
  });

  // Validar registros não classificados
  const registrosNaoClassificados = await prisma.registroPonto.count({
    where: {
      data: { gte: dataInicio, lte: dataFim },
      classificado: false,
    },
  });
  validacoes.push({
    id: 'classificacao',
    success: registrosNaoClassificados === 0,
    count: registrosNaoClassificados,
  });

  // Validar aprovações pendentes
  const aprovacoesPendentes = await prisma.registroClassificacao.count({
    where: {
      data: { gte: dataInicio, lte: dataFim },
      status: 'pendente',
    },
  });
  validacoes.push({
    id: 'aprovacoes',
    success: aprovacoesPendentes === 0,
    count: aprovacoesPendentes,
  });

  return validacoes;
}

async function calcularTotalizadores(dataInicio: Date, dataFim: Date) {
  const [
    totalFuncionarios,
    totalRegistros,
    horasNormais,
    horasExtras,
    totalFaltas,
  ] = await Promise.all([
    prisma.funcionario.count({ where: { ativo: true } }),
    prisma.registroPonto.count({
      where: { data: { gte: dataInicio, lte: dataFim } },
    }),
    prisma.registroClassificacao.aggregate({
      where: {
        data: { gte: dataInicio, lte: dataFim },
        tipoHora: 'normal',
      },
      _sum: { horasTrabalhadas: true },
    }),
    prisma.registroClassificacao.aggregate({
      where: {
        data: { gte: dataInicio, lte: dataFim },
        tipoHora: { in: ['extra_50', 'extra_100'] },
      },
      _sum: { horasTrabalhadas: true },
    }),
    prisma.registroClassificacao.count({
      where: {
        data: { gte: dataInicio, lte: dataFim },
        tipoHora: 'falta',
      },
    }),
  ]);

  return {
    totalFuncionarios,
    totalRegistros,
    totalHorasNormais: horasNormais._sum.horasTrabalhadas || 0,
    totalHorasExtras: horasExtras._sum.horasTrabalhadas || 0,
    totalFaltas,
  };
}
```

## 🗄️ Schema do Banco de Dados

### 🔒 Modelo Prisma
```prisma
model FechamentoMensal {
  id                  Int       @id @default(autoincrement())
  periodo             String    @unique // 'YYYY-MM'
  dataInicio          DateTime  @map("data_inicio")
  dataFim             DateTime  @map("data_fim")
  status              String    @default("aberto") // 'aberto', 'fechado', 'processando'
  totalFuncionarios   Int       @map("total_funcionarios")
  totalRegistros      Int       @map("total_registros")
  totalHorasNormais   Int       @map("total_horas_normais") // em minutos
  totalHorasExtras    Int       @map("total_horas_extras") // em minutos
  totalFaltas         Int       @map("total_faltas")
  dataFechamento      DateTime? @map("data_fechamento")
  fechadoPorId        Int?      @map("fechado_por_id")
  observacoes         String?
  criadoEm            DateTime  @default(now()) @map("criado_em")

  // Relacionamentos
  fechadoPor          Usuario?  @relation(fields: [fechadoPorId], references: [id])
  registros           RegistroPonto[]

  @@map("fechamentos_mensais")
}
```

## 📝 Checklist de Implementação

### ✅ Funcionalidades Principais
- [ ] Dashboard de fechamento
- [ ] Wizard de fechamento
- [ ] Validações automáticas
- [ ] Cálculo de totalizadores
- [ ] Bloqueio de registros
- [ ] Reabertura controlada
- [ ] Exportação de dados
- [ ] Auditoria completa

### 🔧 Validações
- [ ] Verificar registros pendentes
- [ ] Validar classificações
- [ ] Confirmar aprovações
- [ ] Calcular totais
- [ ] Gerar relatórios

## 🚀 Próximos Passos
1. **Relatórios** - Relatórios detalhados
2. **Estatísticas** - Análises avançadas
3. **Empresa Principal** - Gestão da empresa
