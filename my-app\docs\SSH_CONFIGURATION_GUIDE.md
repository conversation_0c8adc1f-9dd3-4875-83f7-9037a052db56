# 🔑 Guia Completo de Configuração SSH - Sistema RLPONTO

## 📋 Visão Geral

Este documento explica como criar e configurar conexões SSH sem senha para o Sistema RLPONTO, incluindo diagnóstico de problemas e soluções.

## 🚨 Status Atual Identificado

### ❌ **PROBLEMAS ENCONTRADOS:**
1. **Chave SSH não existe** (`~/.ssh/rl-ponto-next`)
2. **Configuração SSH não existe** (`~/.ssh/config`)
3. **Chave pública não está no servidor** (`authorized_keys`)

### 📊 **Resultado do Teste:**
```bash
❌ SSH pede senha (INCORRETO)
✅ SSH com senha funciona (servidor acessível)
❌ SSH sem senha falha (chaves não configuradas)
```

## 🔧 Solução Completa

### 1. **Configuração Automática (RECOMENDADO)**

```bash
# Execute o script que faz tudo automaticamente
bash scripts/setup-production.sh
```

### 2. **Configuração Manual (Passo a Passo)**

Se preferir fazer manualmente ou entender o processo:

#### Passo 1: Gerar Chave SSH
```bash
# Gerar chave SSH específica para o projeto
ssh-keygen -t ed25519 -f ~/.ssh/rl-ponto-next -N "" -C "rlponto-deploy@$(hostname)"

# Verificar se foi criada
ls -la ~/.ssh/rl-ponto-next*
```

#### Passo 2: Configurar Permissões
```bash
# Definir permissões corretas
chmod 600 ~/.ssh/rl-ponto-next
chmod 644 ~/.ssh/rl-ponto-next.pub
chmod 700 ~/.ssh
```

#### Passo 3: Criar Configuração SSH
```bash
# Criar ou editar ~/.ssh/config
cat >> ~/.ssh/config << 'EOF'

# Sistema RLPONTO - Servidor de Produção
Host rlponto-prod
    HostName ************
    User root
    IdentityFile ~/.ssh/rl-ponto-next
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
EOF

# Definir permissões
chmod 600 ~/.ssh/config
```

#### Passo 4: Copiar Chave para o Servidor
```bash
# Método 1: Usando ssh-copy-id (se disponível)
ssh-copy-id -i ~/.ssh/rl-ponto-next.pub root@************

# Método 2: Manual (se ssh-copy-id não funcionar)
cat ~/.ssh/rl-ponto-next.pub | ssh root@************ "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"
```

**Senha quando solicitada:** `@Ric6109`

#### Passo 5: Testar Configuração
```bash
# Testar SSH sem senha
ssh rlponto-prod "echo 'SSH sem senha funcionando!' && hostname"

# Se funcionar, você verá:
# SSH sem senha funcionando!
# RLPONTO
```

## 🔍 Diagnóstico e Testes

### Script de Teste Rigoroso
```bash
# Executar diagnóstico completo
bash scripts/test-ssh-rigorous.sh
```

### Testes Manuais

#### 1. Verificar Chaves Locais
```bash
# Verificar se chaves existem
ls -la ~/.ssh/rl-ponto-next*

# Ver conteúdo da chave pública
cat ~/.ssh/rl-ponto-next.pub
```

#### 2. Verificar Configuração
```bash
# Ver configuração SSH
cat ~/.ssh/config | grep -A 10 "Host rlponto-prod"
```

#### 3. Testar Conexão
```bash
# Teste sem senha (deve funcionar)
ssh -o BatchMode=yes rlponto-prod "echo 'OK'"

# Teste com alias
ssh rlponto-prod "whoami && hostname"

# Teste de conectividade
ssh rlponto-prod "ping -c 2 google.com"
```

## 🚨 Troubleshooting

### Problema 1: "Permission denied (publickey)"
```bash
# Verificar permissões locais
ls -la ~/.ssh/rl-ponto-next*
chmod 600 ~/.ssh/rl-ponto-next
chmod 644 ~/.ssh/rl-ponto-next.pub

# Verificar se chave está no servidor
ssh root@************ "cat ~/.ssh/authorized_keys"
```

### Problema 2: "Host key verification failed"
```bash
# Limpar known_hosts
ssh-keygen -R ************

# Ou usar configuração que ignora
ssh -o StrictHostKeyChecking=no rlponto-prod
```

### Problema 3: "Connection refused"
```bash
# Verificar se servidor está acessível
ping ************

# Verificar porta SSH
nc -zv ************ 22
```

### Problema 4: SSH ainda pede senha
```bash
# Debug da conexão SSH
ssh -vvv rlponto-prod

# Verificar se está usando a chave correta
ssh -i ~/.ssh/rl-ponto-next root@************
```

## 📁 Estrutura de Arquivos SSH

### Estrutura Local (~/.ssh/)
```
~/.ssh/
├── config                    # Configuração SSH
├── rl-ponto-next            # Chave privada (600)
├── rl-ponto-next.pub        # Chave pública (644)
├── known_hosts              # Hosts conhecidos
└── authorized_keys          # (não usado localmente)
```

### Estrutura no Servidor (/root/.ssh/)
```
/root/.ssh/
├── authorized_keys          # Chaves públicas autorizadas (600)
└── known_hosts             # Hosts conhecidos
```

## 🔒 Segurança

### Boas Práticas Implementadas
- ✅ **Chave específica** para o projeto (`rl-ponto-next`)
- ✅ **Algoritmo moderno** (ed25519)
- ✅ **Permissões corretas** (600 para privadas, 644 para públicas)
- ✅ **Configuração isolada** (não afeta outras conexões SSH)
- ✅ **Timeout configurado** (evita conexões penduradas)

### Configurações de Segurança
```bash
# No arquivo ~/.ssh/config
Host rlponto-prod
    IdentitiesOnly yes          # Usar apenas a chave especificada
    StrictHostKeyChecking no    # Para ambiente local (ajustar em prod)
    UserKnownHostsFile /dev/null # Não salvar host key (ambiente local)
    ServerAliveInterval 60      # Keep-alive a cada 60s
    ServerAliveCountMax 3       # Máximo 3 tentativas
```

## 📋 Checklist de Configuração

### Antes da Configuração:
- [ ] Servidor acessível (ping ************)
- [ ] Porta SSH aberta (nc -zv ************ 22)
- [ ] Credenciais de acesso (root/@Ric6109)
- [ ] SSH client instalado

### Durante a Configuração:
- [ ] Chave SSH gerada (`~/.ssh/rl-ponto-next`)
- [ ] Permissões corretas definidas
- [ ] Configuração SSH criada (`~/.ssh/config`)
- [ ] Chave pública copiada para servidor

### Após a Configuração:
- [ ] SSH sem senha funciona (`ssh rlponto-prod`)
- [ ] Alias funciona corretamente
- [ ] Conexão estável
- [ ] Deploy scripts funcionam

## 🚀 Scripts Automatizados

### 1. Configuração Completa
```bash
# Configura SSH + servidor + deploy
bash scripts/setup-complete.sh
```

### 2. Apenas SSH
```bash
# Configura apenas SSH sem senha
bash scripts/setup-production.sh
```

### 3. Teste de SSH
```bash
# Testa configuração SSH
bash scripts/test-ssh-rigorous.sh
```

### 4. Deploy
```bash
# Deploy da aplicação (após SSH configurado)
bash scripts/deploy.sh
```

## 📞 Comandos de Emergência

### Resetar Configuração SSH
```bash
# Remover configuração atual
rm -f ~/.ssh/rl-ponto-next*
sed -i '/Host rlponto-prod/,+10d' ~/.ssh/config

# Reconfigurar
bash scripts/setup-production.sh
```

### Acesso de Emergência
```bash
# Se SSH sem senha falhar, usar senha
ssh root@************
# Senha: @Ric6109
```

### Verificar Status no Servidor
```bash
# Conectar e verificar authorized_keys
ssh root@************ "cat ~/.ssh/authorized_keys"
ssh root@************ "ls -la ~/.ssh/"
```

## 🎯 Próximos Passos

### 1. **Configurar SSH (OBRIGATÓRIO)**
```bash
bash scripts/setup-production.sh
```

### 2. **Testar SSH**
```bash
ssh rlponto-prod "echo 'SSH OK'"
```

### 3. **Configurar Servidor**
```bash
bash scripts/setup-complete.sh
```

### 4. **Deploy da Aplicação**
```bash
bash scripts/deploy.sh
```

---

## 📊 Status Esperado Após Configuração

### ✅ **SSH Funcionando Corretamente:**
```bash
$ ssh rlponto-prod "whoami && hostname"
root
RLPONTO

$ ssh rlponto-prod "echo 'Sem senha!'"
Sem senha!
```

### ❌ **SSH com Problemas:**
```bash
$ ssh rlponto-prod
root@************'s password:  # ← NÃO deve pedir senha
```

**Se ainda pedir senha após configuração, execute o diagnóstico:**
```bash
bash scripts/test-ssh-rigorous.sh
```

---

**Documento criado em**: Janeiro 2024  
**Última atualização**: Janeiro 2024  
**Versão**: 1.0  
**Responsável**: Equipe DevOps RLPONTO
