(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[509],{1007:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1243:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3769:(e,a,s)=>{"use strict";s.d(a,{$n:()=>i,pd:()=>n,WI:()=>m.SearchIcon});var t=s(5155);s(2115);var r=s(4001);let i=e=>{let{children:a,className:s,variant:i="primary",size:n="md",disabled:l=!1,loading:c=!1,type:o="button",onClick:d,...m}=e;return(0,t.jsxs)("button",{type:o,className:(0,r.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[i],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[n],s),disabled:l||c,onClick:d,...m,children:[c&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})},n=e=>{let{className:a,type:s="text",placeholder:i,value:n,defaultValue:l,disabled:c=!1,required:o=!1,error:d,label:m,id:u,name:x,onChange:h,onBlur:g,onFocus:p,...v}=e,f=u||x;return(0,t.jsxs)("div",{className:"w-full",children:[m&&(0,t.jsxs)("label",{htmlFor:f,className:"block text-sm font-medium text-gray-700 mb-1",children:[m,o&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("input",{type:s,id:f,name:x,style:{color:"#000000",backgroundColor:"#ffffff",fontSize:"16px",fontWeight:"600"},className:(0,r.cn)("flex h-12 w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-base font-semibold placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",d&&"border-red-500 focus:ring-red-500 focus:border-red-500",a),placeholder:i,value:n,defaultValue:l,disabled:c,required:o,onChange:h,onBlur:g,onFocus:p,...v}),d&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d})]})};var l=s(646),c=s(4861),o=s(5339),d=s(1284);l.A,c.A,o.A,d.A;var m=s(9829)},4001:(e,a,s)=>{"use strict";s.d(a,{cn:()=>i});var t=s(2596),r=s(9688);function i(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},4186:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4355:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4541:(e,a,s)=>{"use strict";s.d(a,{HistoricoRecente:()=>h});var t=s(5155),r=s(2115),i=s(9946);let n=(0,i.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);var l=s(4835);let c=(0,i.A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);var o=s(4186),d=s(8196),m=s(4355),u=s(646),x=s(5339);function h(){let[e,a]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0),[h,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{g(!0),p()},[]);let p=async()=>{try{i(!0);let e=new Date("2024-01-27"),s=[{id:"1",funcionario:{nome:"Jo\xe3o Silva Santos",matricula:"EMP001"},tipo:"entrada",metodo:"fingerprint",horario:"08:00:15",data:e.toLocaleDateString("pt-BR"),status:"sucesso"},{id:"2",funcionario:{nome:"Maria Oliveira Costa",matricula:"EMP002"},tipo:"intervalo_inicio",metodo:"facial",horario:"12:00:32",data:e.toLocaleDateString("pt-BR"),status:"sucesso"},{id:"3",funcionario:{nome:"Carlos Roberto Lima",matricula:"EMP003"},tipo:"saida",metodo:"fingerprint",horario:"17:30:45",data:new Date(e.getTime()-864e5).toLocaleDateString("pt-BR"),status:"sucesso"},{id:"4",funcionario:{nome:"Ana Paula Silva",matricula:"EMP004"},tipo:"entrada",metodo:"facial",horario:"07:45:12",data:new Date(e.getTime()-864e5).toLocaleDateString("pt-BR"),status:"erro"}];await new Promise(e=>setTimeout(e,1e3)),a(s)}catch(e){console.error("Erro ao buscar hist\xf3rico:",e)}finally{i(!1)}};return s?(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0].map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg animate-pulse",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]}),(0,t.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded"})]},a))}):0===e.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum registro encontrado"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Os registros de ponto aparecer\xe3o aqui"})]}):(0,t.jsxs)("div",{className:"space-y-3",children:[e.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg transition-colors ".concat("sucesso"===e.status?"border-gray-200 hover:bg-gray-50":"border-red-200 bg-red-50"),children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center",children:(e=>{switch(e){case"entrada":return(0,t.jsx)(n,{className:"h-5 w-5 text-green-600"});case"saida":return(0,t.jsx)(l.A,{className:"h-5 w-5 text-red-600"});case"intervalo_inicio":case"intervalo_fim":return(0,t.jsx)(c,{className:"h-5 w-5 text-yellow-600"});default:return(0,t.jsx)(o.A,{className:"h-5 w-5 text-gray-600"})}})(e.tipo)})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.funcionario.nome}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.funcionario.matricula,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsx)("span",{children:(e=>{switch(e){case"entrada":return"Entrada";case"saida":return"Sa\xedda";case"intervalo_inicio":return"In\xedcio Intervalo";case"intervalo_fim":return"Fim Intervalo";default:return e}})(e.tipo)})}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"fingerprint":return(0,t.jsx)(d.A,{className:"h-4 w-4 text-blue-600"});case"facial":return(0,t.jsx)(m.A,{className:"h-4 w-4 text-green-600"});default:return(0,t.jsx)(o.A,{className:"h-4 w-4 text-gray-600"})}})(e.metodo),(0,t.jsx)("span",{children:(e=>{switch(e){case"fingerprint":return"Biometria Digital";case"facial":return"Reconhecimento Facial";case"manual":return"Manual";default:return e}})(e.metodo)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"sucesso":return(0,t.jsx)(u.A,{className:"h-4 w-4 text-green-600"});case"erro":return(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"});default:return(0,t.jsx)(o.A,{className:"h-4 w-4 text-gray-600"})}})(e.status),(0,t.jsx)("span",{className:"sucesso"===e.status?"text-green-600":"text-red-600",children:"sucesso"===e.status?"Sucesso":"Erro"})]})]})]}),(0,t.jsxs)("div",{className:"flex-shrink-0 text-right",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.horario}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.data})]})]},e.id)),(0,t.jsx)("div",{className:"text-center pt-4",children:(0,t.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Ver hist\xf3rico completo"})})]})}},4639:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,9706)),Promise.resolve().then(s.bind(s,4541)),Promise.resolve().then(s.bind(s,4772)),Promise.resolve().then(s.bind(s,9829))},4772:(e,a,s)=>{"use strict";s.d(a,{PontoStatus:()=>o});var t=s(5155),r=s(2115),i=s(646),n=s(4186),l=s(5339),c=s(1007);function o(){let[e,a]=(0,r.useState)(null),[s,o]=(0,r.useState)(!0),[d,m]=(0,r.useState)(new Date);(0,r.useEffect)(()=>{let e=setInterval(()=>{m(new Date)},1e3);return()=>clearInterval(e)},[]),(0,r.useEffect)(()=>{u()},[]);let u=async()=>{try{o(!0);let e={funcionario:{nome:"Jo\xe3o Silva Santos",matricula:"EMP001",cargo:"Analista de Sistemas"},statusAtual:"entrada",ultimoRegistro:{tipo:"Entrada",horario:"08:00",data:new Date().toLocaleDateString("pt-BR")},proximoRegistro:"Sa\xedda para Intervalo",horasTrabalhadas:"04:15",saldoHoras:"+00:15"};await new Promise(e=>setTimeout(e,1e3)),a(e)}catch(e){console.error("Erro ao buscar status do ponto:",e)}finally{o(!1)}};return s?(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"})]})]})}):e?(0,t.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(c.A,{className:"h-6 w-6 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Status do Ponto"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:d.toLocaleDateString("pt-BR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:d.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Hor\xe1rio atual"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Funcion\xe1rio"}),(0,t.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e.funcionario.nome}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[e.funcionario.matricula," • ",e.funcionario.cargo]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Horas Trabalhadas"}),(0,t.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e.horasTrabalhadas}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Hoje"})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Saldo de Horas"}),(0,t.jsx)("div",{className:"text-lg font-semibold ".concat(e.saldoHoras.startsWith("+")?"text-green-600":"text-red-600"),children:e.saldoHoras}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Acumulado"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 rounded-full border ".concat((e=>{switch(e){case"entrada":return"bg-green-100 text-green-800 border-green-200";case"saida":return"bg-blue-100 text-blue-800 border-blue-200";case"intervalo_inicio":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"intervalo_fim":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(e.statusAtual)),children:[(e=>{switch(e){case"entrada":case"saida":return(0,t.jsx)(i.A,{className:"h-5 w-5"});case"intervalo_inicio":case"intervalo_fim":return(0,t.jsx)(n.A,{className:"h-5 w-5"});default:return(0,t.jsx)(l.A,{className:"h-5 w-5"})}})(e.statusAtual),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Pr\xf3ximo: ",e.proximoRegistro]})]})}),e.ultimoRegistro&&(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["\xdaltimo: ",e.ultimoRegistro.tipo]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[e.ultimoRegistro.horario," - ",e.ultimoRegistro.data]})]})]})]})}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"text-center text-gray-500",children:[(0,t.jsx)(l.A,{className:"h-12 w-12 mx-auto mb-4"}),(0,t.jsx)("p",{children:"Erro ao carregar status do ponto"})]})})}},4835:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},8196:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("fingerprint",[["path",{d:"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4",key:"1nerag"}],["path",{d:"M14 13.12c0 2.38 0 6.38-1 8.88",key:"o46ks0"}],["path",{d:"M17.29 21.02c.12-.6.43-2.3.5-3.02",key:"ptglia"}],["path",{d:"M2 12a10 10 0 0 1 18-6",key:"ydlgp0"}],["path",{d:"M2 16h.01",key:"1gqxmh"}],["path",{d:"M21.8 16c.2-2 .131-5.354 0-6",key:"drycrb"}],["path",{d:"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2",key:"1tidbn"}],["path",{d:"M8.65 22c.21-.66.45-1.32.57-2",key:"13wd9y"}],["path",{d:"M9 6.8a6 6 0 0 1 9 5.2v2",key:"1fr1j5"}]])},9706:(e,a,s)=>{"use strict";s.d(a,{BiometricScanner:()=>h});var t=s(5155),r=s(2115),i=s(3769),n=s(8196),l=s(4355),c=s(1154),o=s(646),d=s(4861),m=s(1007),u=s(4186),x=s(1243);function h(e){let{type:a}=e,[s,h]=(0,r.useState)("idle"),[g,p]=(0,r.useState)(null),[v,f]=(0,r.useState)(0),y="fingerprint"===a,j=y?n.A:l.A,b=y?"Posicione seu dedo no leitor biom\xe9trico":"Posicione seu rosto na c\xe2mera",N=async()=>{h("scanning"),f(0),p(null);let e=setInterval(()=>{f(a=>a>=100?(clearInterval(e),100):a+10)},200);try{if(await new Promise(e=>setTimeout(e,2e3)),Date.now()%5!=0){let e={funcionario:{nome:"Jo\xe3o Silva Santos",matricula:"EMP001",cargo:"Analista de Sistemas"},tipoRegistro:"Entrada",horario:new Date().toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}),message:"Ponto registrado com sucesso!"};p(e),h("success"),await w(e)}else{let e=["not_found","error"][Date.now()%2];h(e),p({tipoRegistro:"",horario:"",message:"not_found"===e?"Biometria n\xe3o encontrada. Verifique o cadastro.":"Erro na leitura. Tente novamente."})}}catch(e){h("error"),p({tipoRegistro:"",horario:"",message:"Erro de conex\xe3o. Tente novamente."})}clearInterval(e),f(100)},w=async e=>{try{var s;if(!(await fetch("/api/ponto/biometrico",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({funcionarioId:null==(s=e.funcionario)?void 0:s.matricula,tipo:e.tipoRegistro.toLowerCase(),biometricType:a,timestamp:new Date().toISOString()})})).ok)throw Error("Erro ao registrar ponto");console.log("Ponto registrado com sucesso")}catch(e){console.error("Erro ao registrar ponto:",e)}},A=()=>{h("idle"),p(null),f(0)};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ".concat((()=>{switch(s){case"scanning":return"border-blue-300 bg-blue-50";case"success":return"border-green-300 bg-green-50";case"error":case"not_found":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-white"}})()),children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(()=>{switch(s){case"scanning":return(0,t.jsx)(c.A,{className:"h-12 w-12 text-blue-600 animate-spin"});case"success":return(0,t.jsx)(o.A,{className:"h-12 w-12 text-green-600"});case"error":case"not_found":return(0,t.jsx)(d.A,{className:"h-12 w-12 text-red-600"});default:return(0,t.jsx)(j,{className:"h-12 w-12 text-gray-500"})}})(),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:(()=>{switch(s){case"scanning":return"Escaneando...";case"success":return(null==g?void 0:g.message)||"Sucesso!";case"error":case"not_found":return(null==g?void 0:g.message)||"Erro na leitura";default:return b}})()}),"scanning"===s&&(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-4",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(v,"%")}})})]})]}),g&&"success"===s&&g.funcionario&&(0,t.jsxs)("div",{className:"mt-6 p-4 bg-white rounded-lg border border-green-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:g.funcionario.nome}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[g.funcionario.matricula," • ",g.funcionario.cargo]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-gray-600",children:"Tipo:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:g.tipoRegistro})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Hor\xe1rio:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:g.horario})]})]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:["idle"===s&&(0,t.jsxs)(i.$n,{onClick:N,className:"flex-1",variant:"primary",children:[(0,t.jsx)(j,{className:"h-4 w-4 mr-2"}),"Iniciar ",y?"Biometria Digital":"Reconhecimento Facial"]}),"scanning"===s&&(0,t.jsx)(i.$n,{onClick:A,className:"flex-1",variant:"outline",children:"Cancelar"}),("success"===s||"error"===s||"not_found"===s)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.$n,{onClick:A,className:"flex-1",variant:"outline",children:"Novo Scan"}),"success"===s&&(0,t.jsx)(i.$n,{onClick:N,className:"flex-1",variant:"primary",children:"Registrar Novamente"})]})]}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,t.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Instru\xe7\xf5es:"}),(0,t.jsx)("ul",{className:"space-y-1 text-xs",children:y?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("li",{children:"• Posicione o dedo firmemente no sensor"}),(0,t.jsx)("li",{children:"• Mantenha o dedo im\xf3vel durante o scan"}),(0,t.jsx)("li",{children:"• Certifique-se de que o dedo esteja limpo"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("li",{children:"• Posicione o rosto centralizado na c\xe2mera"}),(0,t.jsx)("li",{children:"• Mantenha-se im\xf3vel durante o scan"}),(0,t.jsx)("li",{children:"• Certifique-se de ter boa ilumina\xe7\xe3o"})]})})]})]})})]})}},9829:(e,a,s)=>{"use strict";s.d(a,{SearchIcon:()=>n});var t=s(5155),r=s(7924),i=s(4001);function n(e){let{className:a}=e;return(0,t.jsx)(r.A,{className:(0,i.cn)("h-4 w-4 text-gray-500",a)})}}},e=>{e.O(0,[874,596,441,964,358],()=>e(e.s=4639)),_N_E=e.O()}]);