import { Metadata } from 'next';
import { Suspense } from 'react';
import { MetricsOverview } from '@/components/periodo-apuracao/metrics-overview';
import { FrequencyChart } from '@/components/periodo-apuracao/frequency-chart';
import { AlertsPanel } from '@/components/periodo-apuracao/alerts-panel';
import { EmployeeSummary } from '@/components/periodo-apuracao/employee-summary';
import { PeriodSelector } from '@/components/periodo-apuracao/period-selector';
import { BarChart3, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Dashboard - Período de Apuração',
  description: 'Visão geral do período de apuração de ponto',
};

interface DashboardPageProps {
  searchParams: Promise<{
    periodo?: string;
    ano?: string;
    mes?: string;
  }>;
}

export default async function PeriodoApuracaoPage({ searchParams }: DashboardPageProps) {
  const params = await searchParams;
  const currentPeriod = {
    ano: parseInt(params.ano || new Date().getFullYear().toString()),
    mes: parseInt(params.mes || (new Date().getMonth() + 1).toString()),
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Período de Apuração</h1>
                  <p className="text-gray-600">Dashboard de análise e controle de frequência</p>
                </div>
              </div>
            </div>
            <PeriodSelector currentPeriod={currentPeriod} />
          </div>

          {/* Métricas Principais */}
          <Suspense fallback={<MetricsSkeleton />}>
            <MetricsOverview period={currentPeriod} />
          </Suspense>

          {/* Grid Principal */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Gráfico de Frequência */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Frequência Mensal
                </h2>
                <Suspense fallback={<ChartSkeleton />}>
                  <FrequencyChart period={currentPeriod} />
                </Suspense>
              </div>
            </div>

            {/* Painel de Alertas */}
            <div>
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Alertas e Inconsistências
                </h2>
                <Suspense fallback={<AlertsSkeleton />}>
                  <AlertsPanel period={currentPeriod} />
                </Suspense>
              </div>
            </div>
          </div>

          {/* Resumo por Funcionário */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                Resumo por Funcionário
              </h2>
            </div>
            <div className="p-6">
              <Suspense fallback={<TableSkeleton />}>
                <EmployeeSummary period={currentPeriod} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Skeletons para loading states
function MetricsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-white rounded-lg shadow p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded" />
              <div className="w-16 h-4 bg-gray-200 rounded" />
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded mb-2" />
            <div className="w-24 h-4 bg-gray-200 rounded" />
          </div>
        </div>
      ))}
    </div>
  );
}

function ChartSkeleton() {
  return (
    <div className="h-80 bg-gray-200 rounded animate-pulse" />
  );
}

function AlertsSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-start space-x-3 p-3 border rounded animate-pulse">
          <div className="w-5 h-5 bg-gray-200 rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="w-3/4 h-4 bg-gray-200 rounded" />
            <div className="w-1/2 h-3 bg-gray-200 rounded" />
          </div>
        </div>
      ))}
    </div>
  );
}

function TableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-6 gap-4 pb-2 border-b">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="h-4 bg-gray-200 rounded" />
        ))}
      </div>
      {[...Array(5)].map((_, i) => (
        <div key={i} className="grid grid-cols-6 gap-4 py-3">
          {[...Array(6)].map((_, j) => (
            <div key={j} className="h-4 bg-gray-200 rounded" />
          ))}
        </div>
      ))}
    </div>
  );
}
