(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[772],{1007:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2952:(e,a,r)=>{"use strict";r.d(a,{ManualForm:()=>x});var t=r(5155),s=r(2115),o=r(3769),i=r(646),n=r(5339),l=r(1007),c=r(7434),d=r(4516),u=r(1154),m=r(4186);function x(){let[e,a]=(0,s.useState)([]),[r,x]=(0,s.useState)({funcionarioId:"",tipo:"entrada",horario:"",data:"",justificativa:""}),[g,h]=(0,s.useState)(!1),[p,f]=(0,s.useState)(!1),[y,b]=(0,s.useState)(""),[v,j]=(0,s.useState)(!1),[N,w]=(0,s.useState)(!1);(0,s.useEffect)(()=>{w(!0),k();let e=new Date,a=e.toTimeString().slice(0,5),r=e.toISOString().split("T")[0];x(e=>({...e,horario:a,data:r}))},[]);let k=async()=>{try{let e=await fetch("/api/ponto/manual/funcionarios");if(e.ok){let r=await e.json();a(r.funcionarios||[])}}catch(e){console.error("Erro ao buscar funcion\xe1rios:",e)}},A=async()=>{if(!navigator.geolocation)return void b("Geolocaliza\xe7\xe3o n\xe3o suportada pelo navegador");j(!0),navigator.geolocation.getCurrentPosition(async e=>{try{let{latitude:a,longitude:r}=e.coords,t="Lat: ".concat(a.toFixed(6),", Lng: ").concat(r.toFixed(6));x(e=>({...e,localizacao:{latitude:a,longitude:r,endereco:t}}))}catch(e){console.error("Erro ao obter endere\xe7o:",e)}finally{j(!1)}},e=>{b("Erro ao obter localiza\xe7\xe3o: "+e.message),j(!1)},{enableHighAccuracy:!0,timeout:1e4})},S=async e=>{if(e.preventDefault(),!r.funcionarioId||!r.justificativa.trim())return void b("Funcion\xe1rio e justificativa s\xe3o obrigat\xf3rios");h(!0),b("");try{let e=await fetch("/api/ponto/manual",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,timestamp:new Date("".concat(r.data,"T").concat(r.horario)).toISOString()})}),a=await e.json();e.ok?(f(!0),x({funcionarioId:"",tipo:"entrada",horario:new Date().toTimeString().slice(0,5),data:new Date().toISOString().split("T")[0],justificativa:""})):b(a.error||"Erro ao registrar ponto")}catch(e){b("Erro de conex\xe3o. Tente novamente.")}finally{h(!1)}},C=e.find(e=>e.id===r.funcionarioId);return p?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(i.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Registro Enviado com Sucesso!"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"O registro manual foi enviado para aprova\xe7\xe3o do supervisor."}),(0,t.jsx)(o.$n,{onClick:()=>f(!1),variant:"primary",children:"Fazer Novo Registro"})]}):(0,t.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[y&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{className:"text-sm text-red-700",children:y})]})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 inline mr-2"}),"Funcion\xe1rio *"]}),(0,t.jsxs)("select",{value:r.funcionarioId,onChange:e=>x(a=>({...a,funcionarioId:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",style:{color:r.funcionarioId?"#111827":"#6B7280"},required:!0,children:[(0,t.jsx)("option",{value:"",style:{color:"#6B7280"},children:"Selecione um funcion\xe1rio"}),e.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.nome," - ",e.matricula]},e.id))]}),C&&(0,t.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-2 rounded",children:[(0,t.jsx)("strong",{children:"Cargo:"})," ",C.cargo]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Data *"}),(0,t.jsx)("input",{type:"date",value:r.data,onChange:e=>x(a=>({...a,data:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",style:{colorScheme:"light"},required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Hor\xe1rio *"}),(0,t.jsx)("input",{type:"time",value:r.horario,onChange:e=>x(a=>({...a,horario:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",style:{colorScheme:"light"},required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Tipo de Registro *"}),(0,t.jsxs)("select",{value:r.tipo,onChange:e=>x(a=>({...a,tipo:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900",required:!0,children:[(0,t.jsx)("option",{value:"entrada",children:"Entrada"}),(0,t.jsx)("option",{value:"saida",children:"Sa\xedda"}),(0,t.jsx)("option",{value:"intervalo_inicio",children:"In\xedcio Intervalo"}),(0,t.jsx)("option",{value:"intervalo_fim",children:"Fim Intervalo"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 inline mr-2"}),"Justificativa *"]}),(0,t.jsx)("textarea",{value:r.justificativa,onChange:e=>x(a=>({...a,justificativa:e.target.value})),placeholder:"Descreva o motivo do registro manual (ex: problema na biometria, trabalho externo, etc.)",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 placeholder:text-gray-600",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 inline mr-2"}),"Localiza\xe7\xe3o (Opcional)"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(o.$n,{type:"button",onClick:A,disabled:v,variant:"outline",className:"flex-shrink-0",children:[v?(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Capturar Localiza\xe7\xe3o"]}),r.localizacao&&(0,t.jsxs)("div",{className:"flex-1 text-sm text-gray-600 bg-green-50 p-2 rounded border",children:["\uD83D\uDCCD ",r.localizacao.endereco]})]})]}),(0,t.jsx)("div",{className:"flex space-x-4",children:(0,t.jsx)(o.$n,{type:"submit",disabled:g,variant:"primary",className:"flex-1",children:g?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Enviando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Registrar Ponto Manual"]})})})]})}},3769:(e,a,r)=>{"use strict";r.d(a,{$n:()=>o,pd:()=>i,WI:()=>u.SearchIcon});var t=r(5155);r(2115);var s=r(4001);let o=e=>{let{children:a,className:r,variant:o="primary",size:i="md",disabled:n=!1,loading:l=!1,type:c="button",onClick:d,...u}=e;return(0,t.jsxs)("button",{type:c,className:(0,s.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[o],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[i],r),disabled:n||l,onClick:d,...u,children:[l&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})},i=e=>{let{className:a,type:r="text",placeholder:o,value:i,defaultValue:n,disabled:l=!1,required:c=!1,error:d,label:u,id:m,name:x,onChange:g,onBlur:h,onFocus:p,...f}=e,y=m||x;return(0,t.jsxs)("div",{className:"w-full",children:[u&&(0,t.jsxs)("label",{htmlFor:y,className:"block text-sm font-medium text-gray-700 mb-1",children:[u,c&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("input",{type:r,id:y,name:x,style:{color:"#000000",backgroundColor:"#ffffff",fontSize:"16px",fontWeight:"600"},className:(0,s.cn)("flex h-12 w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-base font-semibold placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",d&&"border-red-500 focus:ring-red-500 focus:border-red-500",a),placeholder:o,value:i,defaultValue:n,disabled:l,required:c,onChange:g,onBlur:h,onFocus:p,...f}),d&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d})]})};var n=r(646),l=r(4861),c=r(5339),d=r(1284);n.A,l.A,c.A,d.A;var u=r(9829)},4001:(e,a,r)=>{"use strict";r.d(a,{cn:()=>o});var t=r(2596),s=r(9688);function o(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,s.QP)((0,t.$)(a))}},4186:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7434:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9829:(e,a,r)=>{"use strict";r.d(a,{SearchIcon:()=>i});var t=r(5155),s=r(7924),o=r(4001);function i(e){let{className:a}=e;return(0,t.jsx)(s.A,{className:(0,o.cn)("h-4 w-4 text-gray-500",a)})}},9957:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,2952)),Promise.resolve().then(r.bind(r,9829))}},e=>{e.O(0,[874,596,441,964,358],()=>e(e.s=9957)),_N_E=e.O()}]);