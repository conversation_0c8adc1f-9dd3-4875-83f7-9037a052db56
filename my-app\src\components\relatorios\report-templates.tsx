'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  Users, 
  Calendar, 
  Clock, 
  TrendingUp, 
  BarChart3, 
  <PERSON><PERSON><PERSON>,
  FileText,
  Star,
  ArrowRight,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui';

interface ReportTemplate {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'individual' | 'periodo' | 'analitico' | 'frequencia';
  href: string;
  color: string;
  bgColor: string;
  popular?: boolean;
  estimatedTime: string;
  features: string[];
}

export function ReportTemplates() {
  const [selectedCategory, setSelectedCategory] = useState<string>('todos');

  const templates: ReportTemplate[] = [
    {
      id: 'funcionario-individual',
      title: 'Relatório Individual',
      description: 'Relatório detalhado de um funcionário específico',
      icon: Users,
      category: 'individual',
      href: '/relatorios/funcionario',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      popular: true,
      estimatedTime: '2-3 min',
      features: ['Histórico completo', 'Horas trabalhadas', 'Frequência', 'Gráficos']
    },
    {
      id: 'periodo-consolidado',
      title: 'Relatório por Período',
      description: 'Consolidado mensal de todos os funcionários',
      icon: Calendar,
      category: 'periodo',
      href: '/relatorios/periodo',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      popular: true,
      estimatedTime: '3-5 min',
      features: ['Dados consolidados', 'Comparativos', 'Totalizadores', 'Exportação']
    },
    {
      id: 'frequencia-pontualidade',
      title: 'Frequência e Pontualidade',
      description: 'Análise de presença e atrasos',
      icon: Clock,
      category: 'frequencia',
      href: '/relatorios/frequencia',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      estimatedTime: '1-2 min',
      features: ['Taxa de presença', 'Atrasos', 'Faltas', 'Tendências']
    },
    {
      id: 'horas-extras',
      title: 'Horas Extras',
      description: 'Relatório detalhado de horas extras',
      icon: TrendingUp,
      category: 'analitico',
      href: '/relatorios/horas-extras',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      estimatedTime: '2-3 min',
      features: ['Horas extras', 'Custos', 'Distribuição', 'Análises']
    },
    {
      id: 'dashboard-analitico',
      title: 'Dashboard Analítico',
      description: 'Visão analítica com gráficos e métricas',
      icon: BarChart3,
      category: 'analitico',
      href: '/relatorios/analiticos',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      popular: true,
      estimatedTime: '1-2 min',
      features: ['Gráficos interativos', 'KPIs', 'Comparativos', 'Insights']
    },
    {
      id: 'absenteismo',
      title: 'Análise de Absenteísmo',
      description: 'Relatório de ausências e padrões',
      icon: PieChart,
      category: 'analitico',
      href: '/relatorios/absenteismo',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      estimatedTime: '2-4 min',
      features: ['Taxa de absenteísmo', 'Padrões', 'Motivos', 'Impacto']
    }
  ];

  const categories = [
    { id: 'todos', label: 'Todos', count: templates.length },
    { id: 'individual', label: 'Individual', count: templates.filter(t => t.category === 'individual').length },
    { id: 'periodo', label: 'Por Período', count: templates.filter(t => t.category === 'periodo').length },
    { id: 'analitico', label: 'Analíticos', count: templates.filter(t => t.category === 'analitico').length },
    { id: 'frequencia', label: 'Frequência', count: templates.filter(t => t.category === 'frequencia').length }
  ];

  const filteredTemplates = selectedCategory === 'todos' 
    ? templates 
    : templates.filter(template => template.category === selectedCategory);

  const popularTemplates = templates.filter(template => template.popular);

  return (
    <div className="space-y-6">
      {/* Filtros de Categoria */}
      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        <Filter className="h-4 w-4 text-gray-500 flex-shrink-0" />
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
              selectedCategory === category.id
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            <span>{category.label}</span>
            <span className={`px-2 py-0.5 text-xs rounded-full ${
              selectedCategory === category.id
                ? 'bg-blue-200 text-blue-800'
                : 'bg-gray-200 text-gray-600'
            }`}>
              {category.count}
            </span>
          </button>
        ))}
      </div>

      {/* Templates Populares */}
      {selectedCategory === 'todos' && (
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Star className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">Mais Populares</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {popularTemplates.map((template) => (
              <Link key={template.id} href={template.href}>
                <div className={`p-4 border-2 border-dashed border-yellow-200 rounded-lg hover:border-yellow-300 hover:${template.bgColor} transition-all cursor-pointer`}>
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`p-2 ${template.bgColor} rounded-lg`}>
                      <template.icon className={`h-5 w-5 ${template.color}`} />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{template.title}</h4>
                      <p className="text-sm text-gray-600">{template.description}</p>
                    </div>
                    <Star className="h-4 w-4 text-yellow-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{template.estimatedTime}</span>
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Grid de Templates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredTemplates.map((template) => (
          <Link key={template.id} href={template.href}>
            <div className={`p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:${template.bgColor} transition-all cursor-pointer group`}>
              <div className="flex items-start space-x-3 mb-3">
                <div className={`p-2 ${template.bgColor} rounded-lg group-hover:scale-110 transition-transform`}>
                  <template.icon className={`h-5 w-5 ${template.color}`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-medium text-gray-900">{template.title}</h4>
                    {template.popular && (
                      <Star className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">⏱️ {template.estimatedTime}</span>
                    <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </div>
                </div>
              </div>
              
              {/* Features */}
              <div className="flex flex-wrap gap-1">
                {template.features.slice(0, 3).map((feature, index) => (
                  <span 
                    key={index}
                    className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                  >
                    {feature}
                  </span>
                ))}
                {template.features.length > 3 && (
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    +{template.features.length - 3}
                  </span>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum template encontrado
          </h3>
          <p className="text-gray-600 mb-4">
            Não há templates disponíveis para esta categoria.
          </p>
          <Button 
            variant="outline" 
            onClick={() => setSelectedCategory('todos')}
          >
            Ver todos os templates
          </Button>
        </div>
      )}
    </div>
  );
}

