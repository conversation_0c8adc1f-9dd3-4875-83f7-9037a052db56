'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  Pause, 
  <PERSON><PERSON><PERSON>, 
  RefreshC<PERSON>, 
  AlertT<PERSON>gle, 
  CheckCircle,
  TrendingUp,
  BarChart3,
  <PERSON>,
  Clock
} from 'lucide-react';

interface InsightConfig {
  enabled: boolean;
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
  sensitivity: 'low' | 'medium' | 'high';
  categories: string[];
  notifications: boolean;
  autoActions: boolean;
}

interface Insight {
  id: string;
  type: 'critical' | 'opportunity' | 'info' | 'trend';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  category: string;
  createdAt: string;
  status: 'new' | 'reviewed' | 'implemented' | 'dismissed';
}

const insightCategories = [
  { id: 'attendance', name: '<PERSON>equê<PERSON>', icon: <Users className="w-4 h-4" /> },
  { id: 'punctuality', name: 'Pontualidade', icon: <Clock className="w-4 h-4" /> },
  { id: 'overtime', name: '<PERSON><PERSON> Extra<PERSON>', icon: <TrendingUp className="w-4 h-4" /> },
  { id: 'productivity', name: 'Produtividade', icon: <BarChart3 className="w-4 h-4" /> },
  { id: 'patterns', name: 'Padrões', icon: <Brain className="w-4 h-4" /> }
];

const mockInsights: Insight[] = [
  {
    id: '1',
    type: 'critical',
    title: 'Aumento de atrasos no Departamento de Vendas',
    description: 'Detectado aumento de 40% nos atrasos nos últimos 5 dias',
    confidence: 95,
    impact: 'high',
    category: 'punctuality',
    createdAt: '2025-01-27T10:30:00Z',
    status: 'new'
  },
  {
    id: '2',
    type: 'opportunity',
    title: 'Otimização de horas extras possível',
    description: 'Redistribuição de tarefas pode reduzir 25% das horas extras',
    confidence: 87,
    impact: 'medium',
    category: 'overtime',
    createdAt: '2025-01-27T09:15:00Z',
    status: 'reviewed'
  },
  {
    id: '3',
    type: 'trend',
    title: 'Melhoria na pontualidade geral',
    description: 'Tendência positiva de 12% na pontualidade nos últimos 30 dias',
    confidence: 92,
    impact: 'medium',
    category: 'punctuality',
    createdAt: '2025-01-27T08:00:00Z',
    status: 'implemented'
  }
];

export function InsightsEngine() {
  const [config, setConfig] = useState<InsightConfig>({
    enabled: true,
    frequency: 'hourly',
    sensitivity: 'medium',
    categories: ['attendance', 'punctuality', 'overtime'],
    notifications: true,
    autoActions: false
  });

  const [insights] = useState<Insight[]>(mockInsights);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const updateConfig = (field: keyof InsightConfig, value: unknown) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const toggleCategory = (categoryId: string) => {
    setConfig(prev => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter(c => c !== categoryId)
        : [...prev.categories, categoryId]
    }));
  };

  const runAnalysis = async () => {
    setIsAnalyzing(true);
    // Simular análise
    await new Promise(resolve => setTimeout(resolve, 3000));
    setIsAnalyzing(false);
    alert('Análise concluída! 3 novos insights foram gerados.');
  };

  const getInsightIcon = (type: Insight['type']) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'opportunity':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'trend':
        return <TrendingUp className="w-4 h-4 text-blue-600" />;
      default:
        return <Brain className="w-4 h-4 text-gray-600" />;
    }
  };

  const getInsightColor = (type: Insight['type']) => {
    switch (type) {
      case 'critical':
        return 'border-red-200 bg-red-50';
      case 'opportunity':
        return 'border-green-200 bg-green-50';
      case 'trend':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getStatusColor = (status: Insight['status']) => {
    switch (status) {
      case 'new':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewed':
        return 'bg-blue-100 text-blue-800';
      case 'implemented':
        return 'bg-green-100 text-green-800';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Controles Principais */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${config.enabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            <span className="text-sm font-medium text-gray-700">
              Engine {config.enabled ? 'Ativa' : 'Inativa'}
            </span>
          </div>
          
          <button
            onClick={() => updateConfig('enabled', !config.enabled)}
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              config.enabled 
                ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                : 'bg-green-100 text-green-700 hover:bg-green-200'
            }`}
          >
            {config.enabled ? (
              <>
                <Pause className="w-4 h-4 mr-1 inline" />
                Pausar
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-1 inline" />
                Ativar
              </>
            )}
          </button>
        </div>

        <button
          onClick={runAnalysis}
          disabled={isAnalyzing || !config.enabled}
          className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isAnalyzing ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 inline animate-spin" />
              Analisando...
            </>
          ) : (
            <>
              <Brain className="w-4 h-4 mr-2 inline" />
              Executar Análise
            </>
          )}
        </button>
      </div>

      {/* Configurações */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Configurações Gerais</h4>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Frequência de Análise
            </label>
            <select
              value={config.frequency}
              onChange={(e) => updateConfig('frequency', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="realtime">Tempo Real</option>
              <option value="hourly">A cada hora</option>
              <option value="daily">Diariamente</option>
              <option value="weekly">Semanalmente</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sensibilidade
            </label>
            <select
              value={config.sensitivity}
              onChange={(e) => updateConfig('sensitivity', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="low">Baixa (apenas anomalias significativas)</option>
              <option value="medium">Média (balanceada)</option>
              <option value="high">Alta (detecta variações menores)</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.notifications}
                onChange={(e) => updateConfig('notifications', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Enviar notificações por email</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.autoActions}
                onChange={(e) => updateConfig('autoActions', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Executar ações automáticas (experimental)</span>
            </label>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Categorias de Análise</h4>
          <div className="space-y-2">
            {insightCategories.map(category => (
              <label key={category.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                <input
                  type="checkbox"
                  checked={config.categories.includes(category.id)}
                  onChange={() => toggleCategory(category.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div className="flex items-center space-x-2">
                  {category.icon}
                  <span className="text-sm text-gray-700">{category.name}</span>
                </div>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Insights Recentes */}
      <div>
        <h4 className="font-medium text-gray-900 mb-4">Insights Recentes</h4>
        <div className="space-y-3">
          {insights.slice(0, 3).map(insight => (
            <div key={insight.id} className={`border rounded-lg p-4 ${getInsightColor(insight.type)}`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">{insight.title}</h5>
                    <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>Confiança: {insight.confidence}%</span>
                      <span>Impacto: {insight.impact}</span>
                      <span>{new Date(insight.createdAt).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(insight.status)}`}>
                  {insight.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Status da Engine */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Status da Engine</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Última Análise:</span>
            <p className="font-medium">Há 23 min</p>
          </div>
          <div>
            <span className="text-gray-600">Próxima Análise:</span>
            <p className="font-medium">Em 37 min</p>
          </div>
          <div>
            <span className="text-gray-600">Registros Processados:</span>
            <p className="font-medium">1,247</p>
          </div>
          <div>
            <span className="text-gray-600">CPU Usage:</span>
            <p className="font-medium">12%</p>
          </div>
        </div>
      </div>
    </div>
  );
}
