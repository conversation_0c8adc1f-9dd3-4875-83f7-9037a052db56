(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{1007:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let s=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let s=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2355:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let s=(0,r(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let s=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3229:(e,a,r)=>{"use strict";r.d(a,{FuncionarioWizard:()=>N});var s=r(5155),t=r(2115),l=r(5695),o=r(9946);let i=(0,o.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);function d(e){let{steps:a,currentStepIndex:r,completedSteps:t}=e,l=new Set(t.map(e=>e.id));return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Progresso do Cadastro"}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[r+1," de ",a.length]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat((r+1)/a.length*100,"%")}})})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:a.map((e,t)=>{let o=l.has(e.id)||t<r,d=t===r;return(0,s.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center w-full",children:[t>0&&(0,s.jsx)("div",{className:"flex-1 h-1 ".concat(o?"bg-blue-600":"bg-gray-200")}),(0,s.jsx)("div",{className:"\n                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200\n                    ".concat(o?"bg-blue-600 border-blue-600 text-white":d?"bg-white border-blue-600 text-blue-600":"bg-white border-gray-300 text-gray-500","\n                  "),children:o?(0,s.jsx)(i,{className:"h-5 w-5"}):(0,s.jsx)("span",{className:"text-sm font-medium",children:t+1})}),t<a.length-1&&(0,s.jsx)("div",{className:"flex-1 h-1 ".concat(t<r?"bg-blue-600":"bg-gray-200")})]}),(0,s.jsxs)("div",{className:"mt-3 text-center max-w-[120px]",children:[(0,s.jsx)("div",{className:"text-sm font-medium ".concat(d?"text-blue-600":o?"text-gray-900":"text-gray-500"),children:e.title}),(0,s.jsx)("div",{className:"text-xs mt-1 ".concat(d?"text-blue-500":o?"text-gray-600":"text-gray-500"),children:e.description})]})]},e.id)})})]})}var n=r(3769),c=r(1007);function m(e){let{data:a,onDataChange:r,onValidationChange:l}=e,[o,i]=(0,t.useState)({nomeCompleto:a.nomeCompleto||"",cpf:a.cpf||"",rg:a.rg||"",email:a.email||"",telefone:a.telefone||"",celular:a.celular||"",cep:a.cep||"",logradouro:a.logradouro||"",numero:a.numero||"",complemento:a.complemento||"",bairro:a.bairro||"",cidade:a.cidade||"",uf:a.uf||""}),[d,m]=(0,t.useState)({}),x=(e,a)=>{let s={...o,[e]:a};i(s),r(s)},u=e=>{let a=e.replace(/\D/g,"");return a.length<=10?a.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):a.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3")},h=async e=>{let a=e.replace(/\D/g,"");if(8===a.length)try{let e=await fetch("https://viacep.com.br/ws/".concat(a,"/json/")),s=await e.json();s.erro||(i(e=>({...e,logradouro:s.logradouro||"",bairro:s.bairro||"",cidade:s.localidade||"",uf:s.uf||""})),r({...o,logradouro:s.logradouro||"",bairro:s.bairro||"",cidade:s.localidade||"",uf:s.uf||""}))}catch(e){console.error("Erro ao buscar CEP:",e)}};return(0,t.useEffect)(()=>{(()=>{let e={};return o.nomeCompleto.trim()||(e.nomeCompleto="Nome completo \xe9 obrigat\xf3rio"),o.cpf.trim()?/^\d{11}$/.test(o.cpf.replace(/\D/g,""))||(e.cpf="CPF deve ter 11 d\xedgitos"):e.cpf="CPF \xe9 obrigat\xf3rio",o.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.email)&&(e.email="Email inv\xe1lido"),m(e),l(0===Object.keys(e).length)})()},[o]),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Pessoais"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Informe os dados pessoais do funcion\xe1rio"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nome Completo *"}),(0,s.jsx)(n.pd,{placeholder:"Digite o nome completo",value:o.nomeCompleto,onChange:e=>x("nomeCompleto",e.target.value),error:d.nomeCompleto})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CPF *"}),(0,s.jsx)(n.pd,{placeholder:"000.000.000-00",value:o.cpf.replace(/\D/g,"").replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4"),onChange:e=>x("cpf",e.target.value.replace(/\D/g,"")),error:d.cpf})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"RG"}),(0,s.jsx)(n.pd,{placeholder:"Digite o RG",value:o.rg,onChange:e=>x("rg",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,s.jsx)(n.pd,{type:"email",placeholder:"<EMAIL>",value:o.email,onChange:e=>x("email",e.target.value),error:d.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefone"}),(0,s.jsx)(n.pd,{placeholder:"(11) 1234-5678",value:u(o.telefone),onChange:e=>x("telefone",e.target.value.replace(/\D/g,""))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Celular"}),(0,s.jsx)(n.pd,{placeholder:"(11) 99999-9999",value:u(o.celular),onChange:e=>x("celular",e.target.value.replace(/\D/g,""))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CEP"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(n.pd,{placeholder:"00000-000",value:o.cep.replace(/\D/g,"").replace(/(\d{5})(\d{3})/,"$1-$2"),onChange:e=>x("cep",e.target.value.replace(/\D/g,""))}),(0,s.jsx)(n.$n,{type:"button",variant:"outline",onClick:()=>h(o.cep),disabled:8!==o.cep.replace(/\D/g,"").length,children:"Buscar"})]})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logradouro"}),(0,s.jsx)(n.pd,{placeholder:"Rua, Avenida, etc.",value:o.logradouro,onChange:e=>x("logradouro",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"N\xfamero"}),(0,s.jsx)(n.pd,{placeholder:"123",value:o.numero,onChange:e=>x("numero",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Complemento"}),(0,s.jsx)(n.pd,{placeholder:"Apto, Bloco, etc.",value:o.complemento,onChange:e=>x("complemento",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bairro"}),(0,s.jsx)(n.pd,{placeholder:"Nome do bairro",value:o.bairro,onChange:e=>x("bairro",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cidade"}),(0,s.jsx)(n.pd,{placeholder:"Nome da cidade",value:o.cidade,onChange:e=>x("cidade",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"UF"}),(0,s.jsxs)("select",{value:o.uf,onChange:e=>x("uf",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Selecione"}),(0,s.jsx)("option",{value:"SP",children:"S\xe3o Paulo"}),(0,s.jsx)("option",{value:"RJ",children:"Rio de Janeiro"}),(0,s.jsx)("option",{value:"MG",children:"Minas Gerais"}),(0,s.jsx)("option",{value:"RS",children:"Rio Grande do Sul"}),(0,s.jsx)("option",{value:"PR",children:"Paran\xe1"}),(0,s.jsx)("option",{value:"SC",children:"Santa Catarina"})]})]})]})]})}let x=(0,o.A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);function u(e){let{data:a,onDataChange:r,onValidationChange:l}=e,[o,i]=(0,t.useState)({matricula:a.matricula||"",cargo:a.cargo||"",setor:a.setor||"",dataAdmissao:a.dataAdmissao||"",salario:a.salario||0,cargaHoraria:a.cargaHoraria||40,horarioEntrada:a.horarioEntrada||"",horarioSaida:a.horarioSaida||"",intervaloInicio:a.intervaloInicio||"",intervaloFim:a.intervaloFim||"",observacoes:a.observacoes||""}),[d,c]=(0,t.useState)({}),m=(e,a)=>{let s={...o,[e]:a};i(s);let t={...s};"salario"===e&&"string"==typeof a&&(t.salario=parseFloat(a)||0),r(t)};return(0,t.useEffect)(()=>{(()=>{let e={};return o.matricula.trim()||(e.matricula="Matr\xedcula \xe9 obrigat\xf3ria"),o.cargo.trim()||(e.cargo="Cargo \xe9 obrigat\xf3rio"),o.setor.trim()||(e.setor="Setor \xe9 obrigat\xf3rio"),o.dataAdmissao||(e.dataAdmissao="Data de admiss\xe3o \xe9 obrigat\xf3ria"),o.horarioEntrada||(e.horarioEntrada="Hor\xe1rio de entrada \xe9 obrigat\xf3rio"),o.horarioSaida||(e.horarioSaida="Hor\xe1rio de sa\xedda \xe9 obrigat\xf3rio"),o.cargaHoraria<=0&&(e.cargaHoraria="Carga hor\xe1ria deve ser maior que zero"),c(e),l(0===Object.keys(e).length)})()},[o]),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,s.jsx)(x,{className:"h-8 w-8 text-green-600"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Profissionais"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Informe os dados profissionais do funcion\xe1rio"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Matr\xedcula *"}),(0,s.jsx)(n.pd,{placeholder:"Digite a matr\xedcula",value:o.matricula,onChange:e=>m("matricula",e.target.value),error:d.matricula})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Data de Admiss\xe3o *"}),(0,s.jsx)("input",{type:"date",value:o.dataAdmissao,onChange:e=>m("dataAdmissao",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(d.dataAdmissao?"border-red-500":"border-gray-300")}),d.dataAdmissao&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.dataAdmissao})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cargo *"}),(0,s.jsx)(n.pd,{placeholder:"Digite o cargo",value:o.cargo,onChange:e=>m("cargo",e.target.value),error:d.cargo})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Setor *"}),(0,s.jsxs)("select",{value:o.setor,onChange:e=>m("setor",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(d.setor?"border-red-500":"border-gray-300"),children:[(0,s.jsx)("option",{value:"",children:"Selecione o setor"}),(0,s.jsx)("option",{value:"administracao",children:"Administra\xe7\xe3o"}),(0,s.jsx)("option",{value:"producao",children:"Produ\xe7\xe3o"}),(0,s.jsx)("option",{value:"vendas",children:"Vendas"}),(0,s.jsx)("option",{value:"rh",children:"Recursos Humanos"}),(0,s.jsx)("option",{value:"ti",children:"Tecnologia"}),(0,s.jsx)("option",{value:"financeiro",children:"Financeiro"}),(0,s.jsx)("option",{value:"marketing",children:"Marketing"}),(0,s.jsx)("option",{value:"operacoes",children:"Opera\xe7\xf5es"})]}),d.setor&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.setor})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sal\xe1rio"}),(0,s.jsx)(n.pd,{placeholder:"R$ 0,00",value:o.salario.toString(),onChange:e=>m("salario",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Carga Hor\xe1ria Semanal *"}),(0,s.jsx)("input",{type:"number",placeholder:"40",value:o.cargaHoraria.toString(),onChange:e=>m("cargaHoraria",parseInt(e.target.value)||0),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(d.cargaHoraria?"border-red-500":"border-gray-300"),min:"1",max:"60"}),d.cargaHoraria&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.cargaHoraria})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Entrada *"}),(0,s.jsx)("input",{type:"time",value:o.horarioEntrada,onChange:e=>m("horarioEntrada",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(d.horarioEntrada?"border-red-500":"border-gray-300")}),d.horarioEntrada&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.horarioEntrada})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Sa\xedda *"}),(0,s.jsx)("input",{type:"time",value:o.horarioSaida,onChange:e=>m("horarioSaida",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(d.horarioSaida?"border-red-500":"border-gray-300")}),d.horarioSaida&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.horarioSaida})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"In\xedcio do Intervalo"}),(0,s.jsx)("input",{type:"time",value:o.intervaloInicio,onChange:e=>m("intervaloInicio",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fim do Intervalo"}),(0,s.jsx)("input",{type:"time",value:o.intervaloFim,onChange:e=>m("intervaloFim",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Observa\xe7\xf5es"}),(0,s.jsx)("textarea",{placeholder:"Observa\xe7\xf5es adicionais sobre o funcion\xe1rio...",value:o.observacoes,onChange:e=>m("observacoes",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),o.horarioEntrada&&o.horarioSaida&&(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Resumo dos Hor\xe1rios"}),(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Expediente:"})," ",o.horarioEntrada," \xe0s ",o.horarioSaida]}),o.intervaloInicio&&o.intervaloFim&&(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Intervalo:"})," ",o.intervaloInicio," \xe0s ",o.intervaloFim]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Carga Hor\xe1ria:"})," ",o.cargaHoraria,"h semanais"]})]})]})]})}var h=r(646),g=r(4516),p=r(4186);function b(e){var a,r;let{data:l,onValidationChange:o}=e;(0,t.useEffect)(()=>{o(!!(l.nomeCompleto&&l.cpf&&l.matricula&&l.cargo&&l.setor&&l.dataAdmissao&&l.horarioEntrada&&l.horarioSaida&&l.cargaHoraria))},[l,o]);let i=e=>{if(!e)return"";let a=e.replace(/\D/g,"");return a.length<=10?a.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):a.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3")};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,s.jsx)(h.A,{className:"h-8 w-8 text-green-600"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Confirma\xe7\xe3o dos Dados"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Revise todas as informa\xe7\xf5es antes de finalizar o cadastro"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Pessoais"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Nome Completo"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.nomeCompleto||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"CPF"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.cpf?l.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4"):"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"RG"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.rg||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.email||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Telefone"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.telefone?i(l.telefone):"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Celular"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.celular?i(l.celular):"-"})]})]}),(l.logradouro||l.cidade)&&(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Endere\xe7o"})]}),(0,s.jsx)("p",{className:"text-gray-900",children:[l.logradouro,l.numero,l.complemento,l.bairro,l.cidade,l.uf].filter(Boolean).join(", ")||"-"}),l.cep&&(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["CEP: ",l.cep]})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(x,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Profissionais"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Matr\xedcula"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.matricula||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Data de Admiss\xe3o"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.dataAdmissao?(a=l.dataAdmissao)?new Date(a).toLocaleDateString("pt-BR"):"":"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Cargo"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.cargo||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Setor"}),(0,s.jsx)("p",{className:"text-gray-900 capitalize",children:l.setor||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Sal\xe1rio"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.salario?(r=l.salario)?("string"==typeof r?parseFloat(r):r).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"":"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Carga Hor\xe1ria"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.cargaHoraria?"".concat(l.cargaHoraria,"h semanais"):"-"})]})]}),(l.horarioEntrada||l.horarioSaida)&&(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Hor\xe1rios de Trabalho"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Expediente"}),(0,s.jsx)("p",{className:"text-gray-900",children:l.horarioEntrada&&l.horarioSaida?"".concat(l.horarioEntrada," \xe0s ").concat(l.horarioSaida):"-"})]}),l.intervaloInicio&&l.intervaloFim&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Intervalo"}),(0,s.jsx)("p",{className:"text-gray-900",children:"".concat(l.intervaloInicio," \xe0s ").concat(l.intervaloFim)})]})]})]}),l.observacoes&&(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Observa\xe7\xf5es"}),(0,s.jsx)("p",{className:"text-gray-900 mt-1",children:l.observacoes})]})]})]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(h.A,{className:"h-5 w-5 text-yellow-400"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Confirme os dados antes de finalizar"}),(0,s.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,s.jsx)("p",{children:"Verifique se todas as informa\xe7\xf5es est\xe3o corretas. Ap\xf3s confirmar, o funcion\xe1rio ser\xe1 cadastrado no sistema e poder\xe1 come\xe7ar a utilizar o controle de ponto."})})]})]})})]})}var j=r(2355),f=r(3052),v=r(1154);let y=[{id:"pessoal",title:"Dados Pessoais",description:"Informa\xe7\xf5es b\xe1sicas do funcion\xe1rio"},{id:"profissional",title:"Dados Profissionais",description:"Cargo, setor e hor\xe1rios"},{id:"confirmacao",title:"Confirma\xe7\xe3o",description:"Revisar e confirmar dados"}];function N(){let e=(0,l.useRouter)(),[a,r]=(0,t.useState)(!1),[o,c]=(0,t.useState)(null),[x,h]=(0,t.useState)({}),g=function(e){let{steps:a,initialStep:r=0}=e,[s,l]=(0,t.useState)(r),[o,i]=(0,t.useState)({}),[d,n]=(0,t.useState)(new Set),c=a[s],m=0===s,x=s===a.length-1,u=(0,t.useCallback)(()=>{x||l(e=>e+1)},[x]),h=(0,t.useCallback)(()=>{m||l(e=>e-1)},[m]),g=(0,t.useCallback)(e=>{e>=0&&e<a.length&&l(e)},[a.length]),p=(0,t.useCallback)((e,a)=>{i(r=>({...r,[e]:a}))},[]),b=(0,t.useCallback)(e=>{n(a=>new Set([...a,e]))},[]),j=(0,t.useCallback)(e=>{var a;return null!=(a=o[e])&&a},[o]),f=(0,t.useCallback)(e=>d.has(e),[d]),v=j(null==c?void 0:c.id),y=!m,N=(s+1)/a.length*100;return{currentStep:c,currentStepIndex:s,steps:a,isFirstStep:m,isLastStep:x,canGoNext:v,canGoPrevious:y,progress:N,goToNext:u,goToPrevious:h,goToStep:g,setStepValid:p,markStepCompleted:b,isStepValid:j,isStepCompleted:f}}({steps:y,initialStep:0}),p=e=>{h(a=>({...a,...e}))},N=async()=>{try{r(!0),c(null);let a=await fetch("/api/funcionarios",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(x)});if(!a.ok){let e=await a.json();throw Error(e.error||"Erro ao cadastrar funcion\xe1rio")}await a.json(),e.push("/funcionarios?success=funcionario-cadastrado")}catch(e){console.error("Erro ao cadastrar funcion\xe1rio:",e),c(e instanceof Error?e.message:"Erro desconhecido")}finally{r(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(d,{steps:g.steps,currentStepIndex:g.currentStepIndex,completedSteps:g.steps.slice(0,g.currentStepIndex)}),o&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"text-red-800",children:[(0,s.jsx)("strong",{children:"Erro:"})," ",o]})}),(0,s.jsx)("div",{className:"min-h-[400px]",children:(()=>{switch(g.currentStep.id){case"pessoal":return(0,s.jsx)(m,{data:x,onDataChange:p,onValidationChange:e=>g.setStepValid("pessoal",e)});case"profissional":return(0,s.jsx)(u,{data:x,onDataChange:p,onValidationChange:e=>g.setStepValid("profissional",e)});case"confirmacao":return(0,s.jsx)(b,{data:x,onValidationChange:e=>g.setStepValid("confirmacao",e)});default:return null}})()}),(0,s.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,s.jsx)("div",{children:!g.isFirstStep&&(0,s.jsxs)(n.$n,{variant:"outline",onClick:g.goToPrevious,disabled:a,children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Anterior"]})}),(0,s.jsx)("div",{className:"flex space-x-3",children:g.isLastStep?(0,s.jsx)(n.$n,{onClick:N,disabled:!g.canGoNext||a,variant:"primary",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Cadastrando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i,{className:"h-4 w-4 mr-2"}),"Cadastrar Funcion\xe1rio"]})}):(0,s.jsxs)(n.$n,{onClick:g.goToNext,disabled:!g.canGoNext||a,variant:"primary",children:["Pr\xf3ximo",(0,s.jsx)(f.A,{className:"h-4 w-4 ml-2"})]})})]})]})}},3769:(e,a,r)=>{"use strict";r.d(a,{$n:()=>l,pd:()=>o,WI:()=>m.SearchIcon});var s=r(5155);r(2115);var t=r(4001);let l=e=>{let{children:a,className:r,variant:l="primary",size:o="md",disabled:i=!1,loading:d=!1,type:n="button",onClick:c,...m}=e;return(0,s.jsxs)("button",{type:n,className:(0,t.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[l],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[o],r),disabled:i||d,onClick:c,...m,children:[d&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})},o=e=>{let{className:a,type:r="text",placeholder:l,value:o,defaultValue:i,disabled:d=!1,required:n=!1,error:c,label:m,id:x,name:u,onChange:h,onBlur:g,onFocus:p,...b}=e,j=x||u;return(0,s.jsxs)("div",{className:"w-full",children:[m&&(0,s.jsxs)("label",{htmlFor:j,className:"block text-sm font-medium text-gray-700 mb-1",children:[m,n&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("input",{type:r,id:j,name:u,style:{color:"#000000",backgroundColor:"#ffffff",fontSize:"16px",fontWeight:"600"},className:(0,t.cn)("flex h-12 w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-base font-semibold placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",c&&"border-red-500 focus:ring-red-500 focus:border-red-500",a),placeholder:l,value:o,defaultValue:i,disabled:d,required:n,onChange:h,onBlur:g,onFocus:p,...b}),c&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c})]})};var i=r(646),d=r(4861),n=r(5339),c=r(1284);i.A,d.A,n.A,c.A;var m=r(9829)},4001:(e,a,r)=>{"use strict";r.d(a,{cn:()=>l});var s=r(2596),t=r(9688);function l(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,t.QP)((0,s.$)(a))}},4186:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let s=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,a,r)=>{"use strict";r.d(a,{A:()=>s});let s=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5695:(e,a,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(a,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(a,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(a,{useSearchParams:function(){return s.useSearchParams}})},6533:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,3229)),Promise.resolve().then(r.bind(r,9829))},9829:(e,a,r)=>{"use strict";r.d(a,{SearchIcon:()=>o});var s=r(5155),t=r(7924),l=r(4001);function o(e){let{className:a}=e;return(0,s.jsx)(t.A,{className:(0,l.cn)("h-4 w-4 text-gray-500",a)})}}},e=>{e.O(0,[874,596,441,964,358],()=>e(e.s=6533)),_N_E=e.O()}]);