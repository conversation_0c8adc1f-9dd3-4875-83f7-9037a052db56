import { Metada<PERSON> } from 'next';
import { 
  TrendingUp, 
  ArrowLeft, 
  Users, 
  Clock, 
  Target,
  BarChart3,
  Calendar,
  Download
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Análise de Produtividade - RLPONTO',
  description: 'Análise detalhada de produtividade dos funcionários',
};

// Dados mockados para demonstração
const produtividadeData = {
  geral: {
    indiceGeral: 87.6,
    horasTrabalhadasMes: 2840,
    horasProdutivasMes: 2488,
    eficienciaMedia: 87.6
  },
  topFuncionarios: [
    { nome: '<PERSON>', departamento: 'Vendas', produtividade: 96.2, horasExtras: 8 },
    { nome: '<PERSON>', departamento: 'TI', produtividade: 94.8, horasExtras: 12 },
    { nome: '<PERSON>', departamento: 'RH', produtividade: 93.5, horasExtras: 4 },
    { nome: '<PERSON>', departamento: 'Financeiro', produtividade: 92.1, horasExtras: 6 },
    { nome: '<PERSON>', departamento: 'Operações', produtividade: 91.7, horasExtras: 15 }
  ],
  departamentos: [
    { nome: 'RH', produtividade: 93.2, funcionarios: 8, tendencia: 'up' },
    { nome: 'Vendas', produtividade: 91.8, funcionarios: 25, tendencia: 'up' },
    { nome: 'Financeiro', produtividade: 89.4, funcionarios: 12, tendencia: 'stable' },
    { nome: 'TI', produtividade: 87.6, funcionarios: 18, tendencia: 'down' },
    { nome: 'Operações', produtividade: 84.3, funcionarios: 35, tendencia: 'down' }
  ]
};

export default function ProdutividadePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Link href="/estatisticas">
                <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </button>
              </Link>
              <div className="p-2 bg-green-600 rounded-lg">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Análise de Produtividade</h1>
                <p className="text-gray-600">Métricas detalhadas de produtividade e eficiência</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option>Últimos 30 dias</option>
                <option>Últimos 90 dias</option>
                <option>Último ano</option>
              </select>
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>

          {/* Métricas Principais */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Target className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Índice Geral</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {produtividadeData.geral.indiceGeral}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Horas Trabalhadas</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {produtividadeData.geral.horasTrabalhadasMes}h
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Horas Produtivas</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {produtividadeData.geral.horasProdutivasMes}h
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Eficiência Média</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {produtividadeData.geral.eficienciaMedia}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Gráfico de Produtividade (Placeholder) */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Evolução da Produtividade</h2>
            </div>
            <div className="p-6">
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Gráfico de evolução da produtividade</p>
                  <p className="text-sm text-gray-400">Implementação com biblioteca de gráficos</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Funcionários */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Top Funcionários</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {produtividadeData.topFuncionarios.map((funcionario, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-green-600">#{index + 1}</span>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{funcionario.nome}</p>
                          <p className="text-xs text-gray-500">{funcionario.departamento}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">{funcionario.produtividade}%</p>
                        <p className="text-xs text-gray-500">{funcionario.horasExtras}h extras</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Produtividade por Departamento */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Por Departamento</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {produtividadeData.departamentos.map((dept, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <Users className="w-4 h-4 text-blue-600" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{dept.nome}</p>
                          <p className="text-xs text-gray-500">{dept.funcionarios} funcionários</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          dept.produtividade >= 90 ? 'bg-green-100 text-green-800' :
                          dept.produtividade >= 85 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {dept.produtividade}%
                        </span>
                        <div className={`w-2 h-2 rounded-full ${
                          dept.tendencia === 'up' ? 'bg-green-500' :
                          dept.tendencia === 'down' ? 'bg-red-500' :
                          'bg-gray-500'
                        }`} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Insights e Recomendações */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Insights e Recomendações</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Tendência Positiva</h3>
                      <p className="text-sm text-green-700 mt-1">
                        A produtividade geral aumentou 2.3% em relação ao mês anterior, 
                        com destaque para o departamento de Vendas.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <Target className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Oportunidade</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        O departamento de Operações apresenta potencial de melhoria. 
                        Considere treinamentos ou revisão de processos.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

