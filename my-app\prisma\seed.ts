import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...');

  // Criar usuário administrador
  const adminPassword = await hash('200381', 12);
  
  const admin = await prisma.usuario.upsert({
    where: { usuario: 'admin' },
    update: {},
    create: {
      usuario: 'admin',
      nome: 'Administrador Master',
      email: '<EMAIL>',
      senhaHash: adminPassword,
      nivelAcesso: 'admin',
      ativo: true,
    },
  });

  console.log('✅ Usuário administrador criado:', admin);

  // Criar usuário de teste
  const userPassword = await hash('user123', 12);
  
  const user = await prisma.usuario.upsert({
    where: { usuario: 'usuario' },
    update: {},
    create: {
      usuario: 'usuario',
      nome: 'Usuário de Teste',
      email: '<EMAIL>',
      senhaHash: userPassword,
      nivelAcesso: 'usuario',
      ativo: true,
    },
  });

  console.log('✅ Usuário de teste criado:', user);

  console.log('🎉 Seed concluído com sucesso!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Erro no seed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
