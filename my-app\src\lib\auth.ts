import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { prisma } from '@/lib/db';
import { compare } from 'bcryptjs';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        usuario: { label: 'Usuário', type: 'text' },
        senha: { label: 'Senha', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.usuario || !credentials?.senha) {
          return null;
        }

        try {
          const user = await prisma.usuario.findUnique({
            where: { usuario: credentials.usuario },
            include: { funcionario: true }
          });

          if (!user || !user.ativo) {
            return null;
          }

          const isPasswordValid = await compare(credentials.senha, user.senhaHash);

          if (!isPasswordValid) {
            return null;
          }

          return {
            id: user.id.toString(),
            name: user.nome,
            email: user.email || '',
            role: user.nivelAcesso,
            usuario: user.usuario
          };
        } catch (error) {
          console.error('Erro na autenticação:', error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 8 * 60 * 60, // 8 horas
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    maxAge: 8 * 60 * 60, // 8 horas
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.usuario = user.usuario;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.usuario = token.usuario as string;
      }
      return session;
    }
  },
  pages: {
    signIn: '/login',
    error: '/login?error=auth_error',
  },
  debug: false,
};
