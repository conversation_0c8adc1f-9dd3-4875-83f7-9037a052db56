!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(n=n||self).rafSchd=e()}(this,function(){"use strict";return function(n){var e=[],t=null,o=function(){for(var o=arguments.length,i=new Array(o),f=0;f<o;f++)i[f]=arguments[f];e=i,t||(t=requestAnimationFrame(function(){t=null,n.apply(void 0,e)}))};return o.cancel=function(){t&&(cancelAnimationFrame(t),t=null)},o}});
