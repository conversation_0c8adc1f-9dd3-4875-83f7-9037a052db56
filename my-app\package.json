{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.0", "lucide-react": "^0.526.0", "next": "15.4.4", "next-auth": "^4.24.11", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^2.2.1", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.2.5", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "prisma": {"seed": "tsx prisma/seed.ts"}}