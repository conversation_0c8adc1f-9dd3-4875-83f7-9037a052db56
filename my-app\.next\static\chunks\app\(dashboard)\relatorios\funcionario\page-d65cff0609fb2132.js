(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[204],{381:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1154:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1788:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2657:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3769:(e,a,s)=>{"use strict";s.d(a,{$n:()=>t,pd:()=>i,WI:()=>m.SearchIcon});var r=s(5155);s(2115);var l=s(4001);let t=e=>{let{children:a,className:s,variant:t="primary",size:i="md",disabled:d=!1,loading:n=!1,type:c="button",onClick:o,...m}=e;return(0,r.jsxs)("button",{type:c,className:(0,l.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[t],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[i],s),disabled:d||n,onClick:o,...m,children:[n&&(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})},i=e=>{let{className:a,type:s="text",placeholder:t,value:i,defaultValue:d,disabled:n=!1,required:c=!1,error:o,label:m,id:x,name:u,onChange:h,onBlur:p,onFocus:g,...b}=e,y=x||u;return(0,r.jsxs)("div",{className:"w-full",children:[m&&(0,r.jsxs)("label",{htmlFor:y,className:"block text-sm font-medium text-gray-700 mb-1",children:[m,c&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("input",{type:s,id:y,name:u,style:{color:"#000000",backgroundColor:"#ffffff",fontSize:"16px",fontWeight:"600"},className:(0,l.cn)("flex h-12 w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-base font-semibold placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",o&&"border-red-500 focus:ring-red-500 focus:border-red-500",a),placeholder:t,value:i,defaultValue:d,disabled:n,required:c,onChange:h,onBlur:p,onFocus:g,...b}),o&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]})};var d=s(646),n=s(4861),c=s(5339),o=s(1284);d.A,n.A,c.A,o.A;var m=s(9829)},4001:(e,a,s)=>{"use strict";s.d(a,{cn:()=>t});var r=s(2596),l=s(9688);function t(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,l.QP)((0,r.$)(a))}},4960:(e,a,s)=>{"use strict";s.d(a,{ReportForm:()=>u});var r=s(5155),l=s(2115),t=s(3769),i=s(381),d=s(646),n=s(9074),c=s(2657),o=s(1154),m=s(1788),x=s(7434);function u(e){let{config:a}=e,[s,u]=(0,l.useState)(()=>{let e={};return a.fields.forEach(a=>{void 0!==a.defaultValue&&(e[a.name]=a.defaultValue)}),e}),[h,p]=(0,l.useState)(!1),[g,b]=(0,l.useState)(!1),[y,v]=(0,l.useState)({}),f=(e,a)=>{u(s=>({...s,[e]:a})),y[e]&&v(a=>({...a,[e]:""}))},j=async e=>{if((()=>{let e={};return a.fields.forEach(a=>{a.required&&!s[a.name]&&(e[a.name]="".concat(a.label," \xe9 obrigat\xf3rio"))}),v(e),0===Object.keys(e).length})()){p(!0);try{await new Promise(e=>setTimeout(e,3e3)),"preview"===e?b(!0):(console.log("Gerando relat\xf3rio:",{config:a.type,data:s}),alert("Relat\xf3rio gerado com sucesso! O download iniciar\xe1 em breve."))}catch(e){console.error("Erro ao gerar relat\xf3rio:",e)}finally{p(!1)}}};return(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:a.title}),(0,r.jsx)("p",{className:"text-gray-600",children:a.description})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.fields.map(e=>(0,r.jsxs)("div",{className:"radio"===e.type?"md:col-span-2":"",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[e.label,e.required&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(e=>{var a,l,i,d,n,c;switch(e.type){case"select":return(0,r.jsxs)("select",{value:s[e.name]||"",onChange:a=>f(e.name,a.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(y[e.name]?"border-red-500":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:e.placeholder}),null==(a=e.options)?void 0:a.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]});case"checkbox":return(0,r.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",checked:s[e.name]||!1,onChange:a=>f(e.name,a.target.checked),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.label}),e.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]});case"radio":return(0,r.jsx)("div",{className:"space-y-3",children:null==(l=e.options)?void 0:l.map(a=>(0,r.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"radio",name:e.name,value:a.value,checked:s[e.name]===a.value,onChange:a=>f(e.name,a.target.value),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.label}),a.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]})]},a.value))});case"daterange":return(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"Data Inicial"}),(0,r.jsx)("input",{type:"date",value:(null==(d=s[e.name])||null==(i=d.start)?void 0:i.toISOString().split("T")[0])||"",onChange:a=>f(e.name,{...s[e.name],start:new Date(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"Data Final"}),(0,r.jsx)("input",{type:"date",value:(null==(c=s[e.name])||null==(n=c.end)?void 0:n.toISOString().split("T")[0])||"",onChange:a=>f(e.name,{...s[e.name],end:new Date(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]});default:return(0,r.jsx)(t.pd,{type:e.type,placeholder:e.placeholder,value:s[e.name]||"",onChange:a=>f(e.name,a.target.value),error:y[e.name]})}})(e),y[e.name]&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y[e.name]})]},e.name))})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"h-5 w-5 text-gray-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Se\xe7\xf5es do Relat\xf3rio"})]}),(0,r.jsx)("div",{className:"space-y-3",children:a.sections.map((e,a)=>(0,r.jsx)("div",{className:"p-3 border border-gray-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:e.description})]}),(0,r.jsx)("div",{className:"ml-3",children:e.included?(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)("div",{className:"h-4 w-4 border border-gray-300 rounded"})})]})},a))})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 inline mr-1"}),"Tempo estimado: 2-3 minutos"]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)(t.$n,{variant:"outline",onClick:()=>j("preview"),disabled:h,children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Visualizar"]}),(0,r.jsx)(t.$n,{variant:"primary",onClick:()=>j("generate"),disabled:h,children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Gerando..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Gerar Relat\xf3rio"]})})]})]})}),g&&(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("h4",{className:"font-medium text-blue-900",children:"Preview do Relat\xf3rio"})]}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'O relat\xf3rio ser\xe1 gerado com os par\xe2metros selecionados. Clique em "Gerar Relat\xf3rio" para fazer o download.'})]})})]})}},6139:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,4960)),Promise.resolve().then(s.bind(s,9829))},7434:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9074:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9829:(e,a,s)=>{"use strict";s.d(a,{SearchIcon:()=>i});var r=s(5155),l=s(7924),t=s(4001);function i(e){let{className:a}=e;return(0,r.jsx)(l.A,{className:(0,t.cn)("h-4 w-4 text-gray-500",a)})}}},e=>{e.O(0,[874,596,441,964,358],()=>e(e.s=6139)),_N_E=e.O()}]);