{"name": "@hello-pangea/dnd", "version": "18.0.1", "private": false, "description": "Beautiful and accessible drag and drop for lists with React", "author": "<PERSON> <<EMAIL>>", "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "keywords": ["drag and drop", "dnd", "sortable", "reorder", "reorderable", "react", "react.js", "natural", "beautiful", "accessible"], "repository": {"type": "git", "url": "https://github.com/hello-pangea/dnd.git"}, "bugs": {"url": "https://github.com/hello-pangea/dnd/issues"}, "main": "dist/dnd.cjs.js", "module": "dist/dnd.esm.js", "types": "dist/dnd.d.ts", "sideEffects": false, "files": ["/dist", "/src"], "publishConfig": {"access": "public"}, "config": {"prettier_target": "*.{js,jsx,ts,tsx,md,json} src/**/*.{js,jsx,ts,tsx,md,json} test/**/*.{js,jsx,ts,tsx,md,json} docs/**/*.{js,jsx,ts,tsx,md,json} stories/**/*.{js,jsx,ts,tsx,md,json} cypress/**/*.{js,jsx,ts,tsx,md,json} csp-server/**/*.{js,jsx,ts,tsx,md,json}"}, "scripts": {"commit": "cz", "chromatic": "chromatic --project-token=f92123f238de", "prepare": "husky", "release": "release-it", "release:test": "release-it --dry-run", "test:accessibility": "lighthouse http://localhost:9002/iframe.html?id=examples-single-vertical-list--basic --no-enable-error-reporting --config-path=lighthouse.config.js --chrome-flags='--headless' --output=json --output=html --output-path=./test-reports/lighthouse/a11y.json && node a11y-audit-parse.js", "test": "jest --config ./jest.config.ts", "test:react-18": "cross-env REACT_MAJOR_VERSION=18 pnpm test", "test:react-19": "cross-env REACT_MAJOR_VERSION=19 pnpm test", "test:browser": "cypress open", "test:browser:ci": "cypress run", "test:coverage": "pnpm test --coverage --coveragePathIgnorePatterns=/debug", "validate": "pnpm prettier:check && pnpm lint:eslint && pnpm lint:css && pnpm typecheck", "prettier:check": "pnpm prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "pnpm prettier --write $npm_package_config_prettier_target", "lint:eslint": "pnpm eslint \"./**/*.{js,jsx,ts,tsx}\"", "lint:css": "stylelint \"stories/**/*.{js,jsx,ts,tsx}\"", "typecheck": "pnpm typecheck:lib && pnpm typecheck:test && pnpm typecheck:storybook", "typecheck:lib": "pnpm tsc --project tsconfig.json", "typecheck:storybook": "pnpm tsc --project stories/tsconfig.json && pnpm tsc --project .storybook/tsconfig.json", "typecheck:test": "pnpm tsc --project test/tsconfig.json && pnpm tsc --project csp-server/tsconfig.json && pnpm tsc --project cypress/tsconfig.json", "bundle-size:check": "cross-env SNAPSHOT=match pnpm bundle-size:update", "bundle-size:update": "pnpm build:clean && pnpm build:dist && pnpm build:clean", "build": "pnpm build:clean && pnpm build:dist", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "cross-env NODE_ENV=production rollup -c", "storybook": "storybook dev -p 9002", "build-storybook": "storybook build -c .storybook -o site", "prepublishOnly": "pnpm build"}, "dependencies": {"@babel/runtime": "^7.26.7", "css-box-model": "^1.2.1", "raf-schd": "^4.0.3", "react-redux": "^9.2.0", "redux": "^5.0.1"}, "devDependencies": {"@atlaskit/css-reset": "6.11.5", "@atlaskit/theme": "13.1.0", "@babel/core": "7.26.8", "@babel/eslint-parser": "7.26.8", "@babel/plugin-transform-class-properties": "7.25.9", "@babel/plugin-transform-modules-commonjs": "7.26.3", "@babel/plugin-transform-object-assign": "7.25.9", "@babel/plugin-transform-private-methods": "7.25.9", "@babel/plugin-transform-private-property-in-object": "7.25.9", "@babel/plugin-transform-runtime": "7.26.8", "@babel/preset-env": "7.26.8", "@babel/preset-react": "7.26.3", "@babel/preset-typescript": "7.26.0", "@commitlint/cli": "19.7.1", "@commitlint/config-conventional": "19.7.1", "@commitlint/cz-commitlint": "19.6.1", "@emotion/eslint-plugin": "11.12.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@jest/environment": "29.7.0", "@release-it/conventional-changelog": "10.0.0", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "25.0.8", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "15.3.1", "@rollup/plugin-replace": "5.0.7", "@rollup/plugin-strip": "3.0.4", "@rollup/plugin-terser": "0.4.4", "@storybook/addon-docs": "8.5.3", "@storybook/addon-essentials": "8.5.3", "@storybook/addon-storysource": "8.5.3", "@storybook/addon-webpack5-compiler-swc": "1.0.6", "@storybook/manager-api": "8.5.3", "@storybook/react": "8.5.3", "@storybook/react-webpack5": "8.5.3", "@storybook/theming": "8.5.3", "@swc/core": "1.10.15", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@types/express": "4.17.21", "@types/fs-extra": "11.0.4", "@types/jest": "29.5.14", "@types/jest-axe": "3.5.9", "@types/jsdom": "21.1.7", "@types/lodash.isequal": "4.5.8", "@types/markdown-it": "14.1.2", "@types/node": "22.13.1", "@types/raf-schd": "4.0.3", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@types/react-window": "1.8.8", "@types/seedrandom": "3.0.8", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "babel-jest": "29.7.0", "babel-loader": "9.2.1", "babel-plugin-dev-expression": "0.2.3", "babel-plugin-module-resolver": "5.0.2", "browserslist": "4.24.4", "commitizen": "4.3.1", "cross-env": "7.0.3", "csstype": "3.1.3", "cypress": "13.17.0", "dotenv": "16.4.7", "eslint": "8.57.1", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-cypress": "2.15.2", "eslint-plugin-es5": "1.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "27.9.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-n": "17.15.1", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-storybook": "0.11.2", "express": "4.21.2", "fast-glob": "3.3.3", "fs-extra": "11.3.0", "husky": "9.1.7", "jest": "29.7.0", "jest-axe": "9.0.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "jest-watch-typeahead": "2.2.2", "jsdom": "25.0.1", "lighthouse": "12.3.0", "lodash.isequal": "4.5.0", "markdown-it": "14.1.0", "memory-fs": "0.5.0", "postcss-styled-syntax": "0.7.1", "prettier": "3.5.0", "raf-stub": "3.0.0", "react": "19.0.0", "react-18": "npm:react@18.3.1", "react-dom": "19.0.0", "react-dom-18": "npm:react-dom@18.3.1", "react-window": "1.8.11", "release-it": "18.1.2", "require-from-string": "2.0.2", "rimraf": "6.0.1", "rollup": "4.34.6", "rollup-plugin-dts": "6.1.1", "seedrandom": "3.0.5", "storybook": "8.5.3", "styled-components": "6.1.15", "stylelint": "16.14.1", "stylelint-config-recommended": "14.0.1", "stylelint-config-standard": "36.0.1", "ts-node": "10.9.2", "typescript": "5.7.3", "wait-on": "8.0.2", "webpack": "5.97.1"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "license": "Apache-2.0", "jest-junit": {"output": "test-reports/junit/js-test-results.xml"}, "packageManager": "pnpm@10.2.1"}