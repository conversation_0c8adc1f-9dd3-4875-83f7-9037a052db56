# 🔒 Segurança e Privacidade - Sistema RLPONTO

## 📋 Visão Geral

Este documento define as estratégias de segurança e privacidade implementadas no Sistema RLPONTO, incluindo proteção de dados, criptografia, autenticação, controle de acesso e conformidade com regulamentações.

## 🎯 Princípios de Segurança

### Princípios Fundamentais
- **Defesa em Profundidade**: Múltiplas camadas de segurança
- **Princípio do Menor Privilégio**: Acesso mínimo necessário
- **Segurança por Design**: Segurança integrada desde o desenvolvimento
- **Zero Trust**: Verificação contínua de identidade e acesso
- **Transparência**: Logs e auditoria completos
- **Privacidade por Design**: Proteção de dados desde a concepção

### Conformidade Regulatória
- **LGPD** (Lei Geral de Proteção de Dados)
- **CLT** (Consolidação das Leis do Trabalho)
- **ISO 27001** (Gestão de Segurança da Informação)
- **OWASP Top 10** (Principais vulnerabilidades web)

## 🔐 Autenticação e Autorização

### Sistema de Autenticação
```typescript
// Configuração NextAuth.js
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        username: { label: "Usuário", type: "text" },
        password: { label: "Senha", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        // Verificar tentativas de login
        const attempts = await getLoginAttempts(credentials.username);
        if (attempts >= 5) {
          throw new Error('Conta bloqueada por excesso de tentativas');
        }

        // Validar credenciais
        const user = await validateCredentials(credentials);
        if (!user) {
          await recordFailedAttempt(credentials.username);
          return null;
        }

        // Verificar se usuário está ativo
        if (!user.ativo || user.bloqueado) {
          throw new Error('Usuário inativo ou bloqueado');
        }

        // Registrar login bem-sucedido
        await recordSuccessfulLogin(user.id);
        
        return {
          id: user.id.toString(),
          email: user.email,
          name: user.nome,
          role: user.nivelAcesso,
        };
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 horas
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    maxAge: 8 * 60 * 60, // 8 horas
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.permissions = await getUserPermissions(user.id);
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.sub!;
      session.user.role = token.role as string;
      session.user.permissions = token.permissions as string[];
      return session;
    }
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },
  events: {
    async signIn({ user, account, profile }) {
      await auditLog('user_login', {
        userId: user.id,
        method: account?.provider,
        ip: getClientIP(),
        userAgent: getUserAgent(),
      });
    },
    async signOut({ session }) {
      await auditLog('user_logout', {
        userId: session?.user?.id,
        ip: getClientIP(),
      });
    }
  }
};
```

### Usuário Master do Sistema

O sistema possui um usuário master (super administrador) com privilégios especiais:

#### Credenciais Master
- **Usuário**: `admin`
- **Senha**: `200381`
- **Nível**: `master`
- **Características**:
  - ✅ Não pode ser deletado
  - ✅ Não pode se auto-deletar
  - ✅ Acesso total ao sistema
  - ✅ Bypass de restrições
  - ✅ Auditoria especial

#### Proteções Implementadas
```typescript
// Proteção contra auto-exclusão
const canDeleteUser = (currentUser: User, targetUser: User): boolean => {
  if (targetUser.usuario === 'admin' && targetUser.nivelAcesso === 'master') {
    return false; // Master nunca pode ser deletado
  }

  if (currentUser.id === targetUser.id) {
    return false; // Usuário não pode se deletar
  }

  return currentUser.nivelAcesso === 'master';
};
```

### Controle de Acesso (RBAC)
```typescript
// Sistema de roles e permissões
enum UserRole {
  MASTER = 'master',    // Super administrador (usuário admin)
  ADMIN = 'admin',
  HR = 'hr',
  MANAGER = 'manager',
  USER = 'user',
  READONLY = 'readonly'
}

interface Permission {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.MASTER]: [
    { resource: '*', action: '*' }, // Acesso total irrestrito
    { resource: 'system', action: 'master_operations' }, // Operações especiais
  ],
  [UserRole.ADMIN]: [
    { resource: '*', action: '*' }, // Acesso total (exceto operações master)
  ],
  [UserRole.HR]: [
    { resource: 'funcionarios', action: 'create' },
    { resource: 'funcionarios', action: 'read' },
    { resource: 'funcionarios', action: 'update' },
    { resource: 'funcionarios', action: 'delete' },
    { resource: 'relatorios', action: 'read' },
    { resource: 'relatorios', action: 'create' },
    { resource: 'usuarios', action: 'read' },
  ],
  [UserRole.MANAGER]: [
    { resource: 'funcionarios', action: 'read', conditions: { departamento: 'own' } },
    { resource: 'ponto', action: 'approve' },
    { resource: 'relatorios', action: 'read', conditions: { departamento: 'own' } },
  ],
  [UserRole.USER]: [
    { resource: 'ponto', action: 'create', conditions: { funcionario: 'self' } },
    { resource: 'ponto', action: 'read', conditions: { funcionario: 'self' } },
    { resource: 'funcionarios', action: 'read', conditions: { funcionario: 'self' } },
  ],
  [UserRole.READONLY]: [
    { resource: 'relatorios', action: 'read' },
    { resource: 'dashboard', action: 'read' },
  ],
};

// Middleware de autorização
export function requirePermission(resource: string, action: string) {
  return async (req: NextApiRequest, res: NextApiResponse, next: NextFunction) => {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ error: 'Não autenticado' });
    }

    const hasPermission = await checkPermission(
      session.user.role,
      resource,
      action,
      { userId: session.user.id, ...req.query }
    );

    if (!hasPermission) {
      await auditLog('access_denied', {
        userId: session.user.id,
        resource,
        action,
        ip: getClientIP(req),
      });
      return res.status(403).json({ error: 'Acesso negado' });
    }

    next();
  };
}
```

## 🛡️ Proteção de Dados

### Criptografia
```typescript
// Criptografia de dados sensíveis
import crypto from 'crypto';
import bcrypt from 'bcryptjs';

class CryptoService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32;
  private readonly ivLength = 16;
  private readonly tagLength = 16;

  // Criptografar dados sensíveis
  encrypt(text: string, key?: string): string {
    const encryptionKey = key ? Buffer.from(key, 'hex') : this.getEncryptionKey();
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipher(this.algorithm, encryptionKey, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
  }

  // Descriptografar dados
  decrypt(encryptedData: string, key?: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const tag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const encryptionKey = key ? Buffer.from(key, 'hex') : this.getEncryptionKey();
    const decipher = crypto.createDecipher(this.algorithm, encryptionKey, iv);
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  // Hash de senhas
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  // Verificar senha
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // Gerar chave de criptografia
  private getEncryptionKey(): Buffer {
    const key = process.env.ENCRYPTION_KEY;
    if (!key) {
      throw new Error('ENCRYPTION_KEY não configurada');
    }
    return Buffer.from(key, 'hex');
  }
}
```

### Proteção de Dados Pessoais (LGPD)
```typescript
// Implementação de conformidade LGPD
interface DataSubject {
  id: number;
  nome: string;
  email: string;
  cpf: string;
  telefone?: string;
  endereco?: string;
}

class LGPDService {
  // Anonimizar dados pessoais
  async anonymizePersonalData(userId: number): Promise<void> {
    const anonymizedData = {
      nome: `Usuário Anônimo ${userId}`,
      email: `anonimo${userId}@anonimizado.com`,
      cpf: this.anonymizeCPF(),
      telefone: null,
      endereco: null,
    };

    await prisma.funcionario.update({
      where: { id: userId },
      data: anonymizedData,
    });

    await this.auditDataProcessing('anonymization', userId);
  }

  // Exportar dados pessoais (direito de portabilidade)
  async exportPersonalData(userId: number): Promise<any> {
    const userData = await prisma.funcionario.findUnique({
      where: { id: userId },
      include: {
        registrosPonto: true,
        documentos: true,
      },
    });

    await this.auditDataProcessing('data_export', userId);
    
    return {
      dadosPessoais: userData,
      dataExportacao: new Date().toISOString(),
      formato: 'JSON',
    };
  }

  // Deletar dados pessoais (direito ao esquecimento)
  async deletePersonalData(userId: number): Promise<void> {
    // Verificar se há obrigações legais de retenção
    const retentionPeriod = await this.checkRetentionRequirements(userId);
    if (retentionPeriod > 0) {
      throw new Error(`Dados devem ser mantidos por mais ${retentionPeriod} dias`);
    }

    // Deletar dados relacionados
    await prisma.registroPonto.deleteMany({ where: { funcionarioId: userId } });
    await prisma.documento.deleteMany({ where: { funcionarioId: userId } });
    await prisma.funcionario.delete({ where: { id: userId } });

    await this.auditDataProcessing('data_deletion', userId);
  }

  // Registrar consentimento
  async recordConsent(userId: number, purpose: string, granted: boolean): Promise<void> {
    await prisma.consentimento.create({
      data: {
        funcionarioId: userId,
        finalidade: purpose,
        concedido: granted,
        dataConsentimento: new Date(),
        versaoTermos: process.env.TERMS_VERSION || '1.0',
      },
    });
  }

  private anonymizeCPF(): string {
    return '***.***.***-**';
  }

  private async auditDataProcessing(action: string, userId: number): Promise<void> {
    await prisma.logLGPD.create({
      data: {
        acao: action,
        funcionarioId: userId,
        timestamp: new Date(),
        operadorId: getCurrentUserId(),
      },
    });
  }
}
```

## 🔍 Validação e Sanitização

### Validação de Entrada
```typescript
// Schemas de validação com Zod
import { z } from 'zod';

// Schema para funcionário
export const funcionarioSchema = z.object({
  nome: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  
  cpf: z.string()
    .regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'CPF deve estar no formato XXX.XXX.XXX-XX')
    .refine(validateCPF, 'CPF inválido'),
  
  email: z.string()
    .email('Email inválido')
    .max(255, 'Email muito longo')
    .optional(),
  
  telefone: z.string()
    .regex(/^\(\d{2}\)\s\d{4,5}-\d{4}$/, 'Telefone deve estar no formato (XX) XXXXX-XXXX')
    .optional(),
  
  dataAdmissao: z.date()
    .max(new Date(), 'Data de admissão não pode ser futura'),
  
  salario: z.number()
    .positive('Salário deve ser positivo')
    .max(1000000, 'Salário muito alto')
    .optional(),
});

// Sanitização de dados
class InputSanitizer {
  static sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove caracteres HTML perigosos
      .replace(/javascript:/gi, '') // Remove JavaScript
      .replace(/on\w+=/gi, '') // Remove event handlers
      .substring(0, 1000); // Limita tamanho
  }

  static sanitizeHTML(input: string): string {
    // Usar biblioteca como DOMPurify para sanitização HTML
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: [],
    });
  }

  static sanitizeSQL(input: string): string {
    // Remover caracteres perigosos para SQL
    return input.replace(/['";\\]/g, '');
  }
}
```

### Proteção contra Ataques
```typescript
// Middleware de segurança
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { body, validationResult } from 'express-validator';

// Rate limiting
const createRateLimit = (windowMs: number, max: number) => rateLimit({
  windowMs,
  max,
  message: 'Muitas tentativas, tente novamente mais tarde',
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limits específicos
export const authRateLimit = createRateLimit(15 * 60 * 1000, 5); // 5 tentativas por 15min
export const apiRateLimit = createRateLimit(15 * 60 * 1000, 100); // 100 requests por 15min
export const uploadRateLimit = createRateLimit(60 * 60 * 1000, 10); // 10 uploads por hora

// Proteção CSRF
export function csrfProtection(req: NextApiRequest, res: NextApiResponse, next: NextFunction) {
  const token = req.headers['x-csrf-token'] as string;
  const sessionToken = req.session?.csrfToken;

  if (!token || token !== sessionToken) {
    return res.status(403).json({ error: 'Token CSRF inválido' });
  }

  next();
}

// Validação de origem
export function validateOrigin(req: NextApiRequest, res: NextApiResponse, next: NextFunction) {
  const origin = req.headers.origin;
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];

  if (origin && !allowedOrigins.includes(origin)) {
    return res.status(403).json({ error: 'Origem não permitida' });
  }

  next();
}

// Headers de segurança
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", 'https://vercel.live'],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});
```

## 📊 Auditoria e Logs

### Sistema de Auditoria
```typescript
// Sistema completo de auditoria
interface AuditEvent {
  id: string;
  userId?: number;
  action: string;
  resource: string;
  resourceId?: number;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

class AuditService {
  async logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
    const auditEvent: AuditEvent = {
      ...event,
      id: crypto.randomUUID(),
      timestamp: new Date(),
    };

    // Salvar no banco de dados
    await prisma.auditLog.create({
      data: auditEvent,
    });

    // Enviar para sistema de logs externo (opcional)
    if (process.env.EXTERNAL_LOGGING_ENABLED === 'true') {
      await this.sendToExternalLogging(auditEvent);
    }

    // Alertar sobre eventos críticos
    if (this.isCriticalEvent(event.action)) {
      await this.sendSecurityAlert(auditEvent);
    }
  }

  async getAuditTrail(filters: {
    userId?: number;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }): Promise<{ events: AuditEvent[]; total: number }> {
    const where: any = {};

    if (filters.userId) where.userId = filters.userId;
    if (filters.resource) where.resource = filters.resource;
    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) where.timestamp.gte = filters.startDate;
      if (filters.endDate) where.timestamp.lte = filters.endDate;
    }

    const [events, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: ((filters.page || 1) - 1) * (filters.limit || 50),
        take: filters.limit || 50,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return { events, total };
  }

  private isCriticalEvent(action: string): boolean {
    const criticalActions = [
      'user_deleted',
      'data_exported',
      'system_config_changed',
      'security_breach_detected',
      'unauthorized_access_attempt',
    ];
    return criticalActions.includes(action);
  }

  private async sendSecurityAlert(event: AuditEvent): Promise<void> {
    // Implementar notificação para equipe de segurança
    await emailService.sendSecurityAlert({
      event,
      severity: 'high',
      timestamp: event.timestamp,
    });
  }
}

// Decorator para auditoria automática
export function Auditable(resource: string, action: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      let success = true;
      let errorMessage: string | undefined;
      let result: any;

      try {
        result = await method.apply(this, args);
        return result;
      } catch (error) {
        success = false;
        errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        throw error;
      } finally {
        const auditService = new AuditService();
        await auditService.logEvent({
          userId: getCurrentUserId(),
          action,
          resource,
          resourceId: result?.id,
          ipAddress: getClientIP(),
          userAgent: getUserAgent(),
          success,
          errorMessage,
        });
      }
    };
  };
}
```

## 🚨 Monitoramento de Segurança

### Detecção de Anomalias
```typescript
// Sistema de detecção de anomalias
class SecurityMonitor {
  async detectAnomalies(): Promise<void> {
    await Promise.all([
      this.detectUnusualLoginPatterns(),
      this.detectMassDataAccess(),
      this.detectPrivilegeEscalation(),
      this.detectSuspiciousFileAccess(),
    ]);
  }

  private async detectUnusualLoginPatterns(): Promise<void> {
    // Detectar logins em horários incomuns
    const unusualLogins = await prisma.auditLog.findMany({
      where: {
        action: 'user_login',
        timestamp: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Últimas 24h
        },
        OR: [
          { timestamp: { gte: new Date().setHours(22, 0, 0, 0) } }, // Após 22h
          { timestamp: { lte: new Date().setHours(6, 0, 0, 0) } },  // Antes das 6h
        ],
      },
    });

    if (unusualLogins.length > 0) {
      await this.createSecurityAlert('unusual_login_times', unusualLogins);
    }
  }

  private async detectMassDataAccess(): Promise<void> {
    // Detectar acesso em massa a dados
    const massAccess = await prisma.auditLog.groupBy({
      by: ['userId'],
      where: {
        action: { in: ['data_read', 'data_export'] },
        timestamp: {
          gte: new Date(Date.now() - 60 * 60 * 1000), // Última hora
        },
      },
      _count: { action: true },
      having: {
        action: { _count: { gt: 100 } }, // Mais de 100 acessos por hora
      },
    });

    if (massAccess.length > 0) {
      await this.createSecurityAlert('mass_data_access', massAccess);
    }
  }

  private async createSecurityAlert(type: string, data: any): Promise<void> {
    await prisma.securityAlert.create({
      data: {
        type,
        severity: 'medium',
        description: `Anomalia detectada: ${type}`,
        data: JSON.stringify(data),
        timestamp: new Date(),
        resolved: false,
      },
    });

    // Notificar equipe de segurança
    await this.notifySecurityTeam(type, data);
  }
}
```

## 📋 Checklist de Segurança

### Desenvolvimento Seguro
- [ ] Validação de entrada em todas as APIs
- [ ] Sanitização de dados de saída
- [ ] Uso de prepared statements (Prisma ORM)
- [ ] Criptografia de dados sensíveis
- [ ] Headers de segurança configurados
- [ ] Rate limiting implementado
- [ ] Logs de auditoria completos
- [ ] Testes de segurança automatizados

### Infraestrutura
- [ ] HTTPS obrigatório
- [ ] Certificados SSL válidos
- [ ] Firewall configurado
- [ ] Backup criptografado
- [ ] Monitoramento de segurança
- [ ] Atualizações de segurança aplicadas
- [ ] Acesso restrito ao servidor
- [ ] Logs centralizados

### Conformidade
- [ ] Política de privacidade atualizada
- [ ] Termos de uso definidos
- [ ] Consentimento LGPD implementado
- [ ] Direitos do titular implementados
- [ ] DPO (Data Protection Officer) designado
- [ ] Avaliação de impacto realizada
- [ ] Procedimentos de breach definidos
- [ ] Treinamento de equipe realizado

## 📞 Contatos de Segurança

### Equipe de Segurança
- **CISO**: <EMAIL>
- **DPO**: <EMAIL>
- **Emergência**: +55 11 9999-9999

### Reportar Vulnerabilidades
- **Email**: <EMAIL>
- **Bug Bounty**: [Link para programa]
- **PGP Key**: [Link para chave pública]

---

**Documento criado em**: [Data]  
**Última atualização**: [Data]  
**Versão**: 1.0  
**Responsável**: [Nome do CISO/Security Lead]
