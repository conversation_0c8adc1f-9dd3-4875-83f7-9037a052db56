# 🏗️ Arquitetura do Sistema - RLPONTO

## 📋 Visão Geral

Este documento descreve a arquitetura do Sistema RLPONTO, incluindo padrões arquiteturais, fluxo de dados, comunicação entre módulos e diagramas de referência.

## 🎯 Princípios Arquiteturais

### Princípios Fundamentais
- **Separação de Responsabilidades**: Cada camada tem uma responsabilidade específica
- **Baixo Acoplamento**: Módulos independentes e intercambiáveis
- **Alta Coesão**: Funcionalidades relacionadas agrupadas
- **Escalabilidade**: Arquitetura preparada para crescimento
- **Manutenibilidade**: Código limpo e bem documentado
- **Testabilidade**: Componentes facilmente testáveis

### Padrões Adotados
- **MVC (Model-View-Controller)**: Separação clara de responsabilidades
- **Repository Pattern**: Abstração da camada de dados
- **Dependency Injection**: Inversão de controle
- **Observer Pattern**: Sistema de eventos e notificações
- **Factory Pattern**: Criação de objetos complexos

## 🏛️ Arquitetura Geral

### Visão de Alto Nível
```
┌─────────────────────────────────────────────────────────────┐
│                    SISTEMA RLPONTO                         │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Next.js 15 + React 18 + TypeScript)            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Pages     │ │ Components  │ │   Hooks     │          │
│  │   Layouts   │ │   UI Kit    │ │   Context   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Backend (Next.js API Routes + TypeScript)                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Controllers │ │  Services   │ │ Middleware  │          │
│  │ Validators  │ │  Business   │ │    Auth     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (Prisma ORM + MySQL)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Models    │ │ Migrations  │ │   Seeds     │          │
│  │ Repositories│ │   Indexes   │ │  Backups    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  External Services                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Biometric   │ │   Email     │ │   Storage   │          │
│  │  Devices    │ │  Service    │ │   (Files)   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### Tipo de Arquitetura
**Monolito Modular**: Aplicação única com módulos bem definidos e separados

#### Justificativa:
- **Simplicidade**: Mais fácil de desenvolver, testar e deployar
- **Performance**: Comunicação interna mais rápida
- **Consistência**: Transações ACID garantidas
- **Custo**: Menor complexidade operacional
- **Equipe**: Adequado para equipes pequenas/médias

## 📊 Fluxo de Dados

### Fluxo Principal de Requisição
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Browser   │───▶│  Next.js    │───▶│ API Routes  │───▶│   Database  │
│  (Client)   │    │ (Frontend)  │    │ (Backend)   │    │   (MySQL)   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       ▲                   │                   │                   │
       │                   ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Response   │◀───│   Render    │◀───│  Business   │◀───│   Query     │
│   (JSON)    │    │   (SSR)     │    │   Logic     │    │  Results    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Fluxo de Autenticação
```
1. Login Request
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │   Client    │───▶│ NextAuth.js │───▶│  Database   │
   │             │    │             │    │             │
   └─────────────┘    └─────────────┘    └─────────────┘

2. JWT Generation
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │  Database   │───▶│ NextAuth.js │───▶│   Client    │
   │             │    │    (JWT)    │    │  (Cookie)   │
   └─────────────┘    └─────────────┘    └─────────────┘

3. Protected Request
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │   Client    │───▶│ Middleware  │───▶│ API Route   │
   │  (Cookie)   │    │ (Validate)  │    │ (Protected) │
   └─────────────┘    └─────────────┘    └─────────────┘
```

### Fluxo de Registro de Ponto
```
1. Biometric Capture
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │  Biometric  │───▶│   Client    │───▶│   Server    │
   │   Device    │    │ (Capture)   │    │ (Validate)  │
   └─────────────┘    └─────────────┘    └─────────────┘

2. Data Processing
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │   Server    │───▶│  Business   │───▶│  Database   │
   │ (Validate)  │    │   Rules     │    │   (Store)   │
   └─────────────┘    └─────────────┘    └─────────────┘

3. Response & Notification
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │  Database   │───▶│   Server    │───▶│   Client    │
   │             │    │ (Response)  │    │ (Feedback)  │
   └─────────────┘    └─────────────┘    └─────────────┘
```

## 🔗 Comunicação entre Módulos

### Padrão de Comunicação
```typescript
// Interface de comunicação entre módulos
interface ModuleInterface {
  name: string;
  version: string;
  dependencies: string[];
  exports: Record<string, Function>;
  events: Record<string, EventHandler>;
}

// Exemplo: Módulo de Funcionários
const funcionariosModule: ModuleInterface = {
  name: 'funcionarios',
  version: '1.0.0',
  dependencies: ['auth', 'validation'],
  exports: {
    createEmployee: createEmployeeService,
    updateEmployee: updateEmployeeService,
    deleteEmployee: deleteEmployeeService,
  },
  events: {
    'employee.created': onEmployeeCreated,
    'employee.updated': onEmployeeUpdated,
    'employee.deleted': onEmployeeDeleted,
  },
};
```

### Sistema de Eventos
```typescript
// Event Bus para comunicação assíncrona
class EventBus {
  private listeners: Map<string, Function[]> = new Map();

  subscribe(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }

  unsubscribe(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event) || [];
    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
    }
  }
}

// Uso do Event Bus
eventBus.subscribe('employee.created', (employee) => {
  // Enviar email de boas-vindas
  emailService.sendWelcomeEmail(employee);
});

eventBus.subscribe('timecard.registered', (timecard) => {
  // Atualizar estatísticas em tempo real
  statisticsService.updateRealTimeStats(timecard);
});
```

### APIs Internas
```typescript
// Estrutura padronizada de APIs internas
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationInfo;
    timestamp: string;
    version: string;
  };
}

// Exemplo de API interna
class EmployeeAPI {
  async getEmployees(params: GetEmployeesParams): Promise<APIResponse<Employee[]>> {
    try {
      const employees = await employeeService.findMany(params);
      return {
        success: true,
        data: employees,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'EMPLOYEE_FETCH_ERROR',
          message: 'Erro ao buscar funcionários',
          details: error.message,
        },
      };
    }
  }
}
```

## 📐 Diagramas de Arquitetura

### Diagrama C4 - Nível 1 (Contexto)
```
                    ┌─────────────────┐
                    │   Funcionário   │
                    │    (Pessoa)     │
                    └─────────┬───────┘
                              │
                              ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │   Gestor RH     │ │  Sistema        │ │  Dispositivo    │
    │   (Pessoa)      │◄┤  RLPONTO        │◄┤  Biométrico     │
    └─────────────────┘ │  (Software)     │ │  (Hardware)     │
                        └─────────┬───────┘ └─────────────────┘
                                  │
                                  ▼
                        ┌─────────────────┐
                        │   Sistema       │
                        │   Folha de      │
                        │   Pagamento     │
                        │  (Software)     │
                        └─────────────────┘
```

### Diagrama C4 - Nível 2 (Container)
```
┌─────────────────────────────────────────────────────────────┐
│                    Sistema RLPONTO                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │   Web Browser   │───▶│  Next.js App    │               │
│  │   (Container)   │    │  (Container)    │               │
│  └─────────────────┘    └─────────┬───────┘               │
│                                   │                       │
│  ┌─────────────────┐              ▼                       │
│  │  Mobile App     │    ┌─────────────────┐               │
│  │  (Container)    │───▶│   API Gateway   │               │
│  └─────────────────┘    │  (Container)    │               │
│                         └─────────┬───────┘               │
│                                   │                       │
│                                   ▼                       │
│                         ┌─────────────────┐               │
│                         │   Database      │               │
│                         │   MySQL         │               │
│                         │  (Container)    │               │
│                         └─────────────────┘               │
└─────────────────────────────────────────────────────────────┘
```

### Diagrama C4 - Nível 3 (Componente)
```
┌─────────────────────────────────────────────────────────────┐
│                   Next.js Application                      │
├─────────────────────────────────────────────────────────────┤
│  Frontend Components                                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Pages     │ │ Components  │ │   Hooks     │          │
│  │             │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  API Layer                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Auth API    │ │Employee API │ │Timecard API │          │
│  │             │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Business Logic                                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │Auth Service │ │Employee     │ │Timecard     │          │
│  │             │ │Service      │ │Service      │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Data Access                                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Prisma    │ │ Repositories│ │   Models    │          │
│  │   Client    │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Padrões de Design

### Repository Pattern
```typescript
// Interface do Repository
interface EmployeeRepository {
  findById(id: number): Promise<Employee | null>;
  findMany(params: FindManyParams): Promise<Employee[]>;
  create(data: CreateEmployeeData): Promise<Employee>;
  update(id: number, data: UpdateEmployeeData): Promise<Employee>;
  delete(id: number): Promise<void>;
}

// Implementação com Prisma
class PrismaEmployeeRepository implements EmployeeRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: number): Promise<Employee | null> {
    return this.prisma.employee.findUnique({ where: { id } });
  }

  async findMany(params: FindManyParams): Promise<Employee[]> {
    return this.prisma.employee.findMany({
      where: params.filters,
      orderBy: params.orderBy,
      skip: params.skip,
      take: params.take,
    });
  }

  // ... outras implementações
}
```

### Service Layer
```typescript
// Service com injeção de dependência
class EmployeeService {
  constructor(
    private employeeRepository: EmployeeRepository,
    private eventBus: EventBus,
    private validator: Validator
  ) {}

  async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    // Validação
    await this.validator.validate(createEmployeeSchema, data);

    // Regras de negócio
    if (await this.employeeRepository.findByCpf(data.cpf)) {
      throw new Error('CPF já cadastrado');
    }

    // Criação
    const employee = await this.employeeRepository.create(data);

    // Evento
    this.eventBus.emit('employee.created', employee);

    return employee;
  }
}
```

### Factory Pattern
```typescript
// Factory para dispositivos biométricos
class BiometricDeviceFactory {
  static create(type: string, config: DeviceConfig): BiometricDevice {
    switch (type) {
      case 'nitgen':
        return new NitgenDevice(config);
      case 'zkteco':
        return new ZKTecoDevice(config);
      case 'suprema':
        return new SupremaDevice(config);
      default:
        throw new Error(`Dispositivo não suportado: ${type}`);
    }
  }
}
```

## 📊 Considerações de Performance

### Estratégias de Cache
```typescript
// Cache em múltiplas camadas
class CacheStrategy {
  // Cache em memória (Redis)
  private memoryCache = new Redis(process.env.REDIS_URL);

  // Cache de aplicação (Map)
  private appCache = new Map<string, CacheItem>();

  async get<T>(key: string): Promise<T | null> {
    // 1. Verificar cache de aplicação
    const appCached = this.appCache.get(key);
    if (appCached && !this.isExpired(appCached)) {
      return appCached.data;
    }

    // 2. Verificar cache em memória
    const memoryCached = await this.memoryCache.get(key);
    if (memoryCached) {
      const data = JSON.parse(memoryCached);
      this.appCache.set(key, { data, expiry: Date.now() + 60000 });
      return data;
    }

    return null;
  }

  async set<T>(key: string, data: T, ttl = 300): Promise<void> {
    // Salvar em ambos os caches
    this.appCache.set(key, { data, expiry: Date.now() + ttl * 1000 });
    await this.memoryCache.setex(key, ttl, JSON.stringify(data));
  }
}
```

### Otimização de Queries
```typescript
// Query optimization com Prisma
class OptimizedQueries {
  // Incluir apenas campos necessários
  async getEmployeeSummary(id: number) {
    return prisma.employee.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        department: true,
        position: true,
        // Não incluir campos desnecessários
      },
    });
  }

  // Usar paginação eficiente
  async getEmployeesPaginated(page: number, limit: number) {
    const [employees, total] = await Promise.all([
      prisma.employee.findMany({
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { name: 'asc' },
      }),
      prisma.employee.count(),
    ]);

    return { employees, total, page, totalPages: Math.ceil(total / limit) };
  }
}
```

## 🔒 Segurança Arquitetural

### Defesa em Profundidade
```
┌─────────────────────────────────────────────────────────────┐
│  1. Network Security (HTTPS, Firewall)                     │
├─────────────────────────────────────────────────────────────┤
│  2. Application Security (Authentication, Authorization)    │
├─────────────────────────────────────────────────────────────┤
│  3. Input Validation (Sanitization, Validation)           │
├─────────────────────────────────────────────────────────────┤
│  4. Business Logic Security (Rate Limiting, CSRF)         │
├─────────────────────────────────────────────────────────────┤
│  5. Data Security (Encryption, Hashing)                   │
├─────────────────────────────────────────────────────────────┤
│  6. Infrastructure Security (Database, Server)            │
└─────────────────────────────────────────────────────────────┘
```

### Middleware de Segurança
```typescript
// Stack de middleware de segurança
const securityMiddleware = [
  helmet(), // Headers de segurança
  cors(corsOptions), // CORS configurado
  rateLimit(rateLimitOptions), // Rate limiting
  csrfProtection(), // Proteção CSRF
  inputSanitization(), // Sanitização de entrada
  authentication(), // Autenticação
  authorization(), // Autorização
  auditLogging(), // Log de auditoria
];
```

## 📈 Escalabilidade

### Estratégias de Escalabilidade
```typescript
// Preparação para escalabilidade horizontal
interface ScalabilityStrategy {
  // Database sharding por empresa
  getShardKey(companyId: number): string;

  // Load balancing
  getServerInstance(request: Request): string;

  // Cache distribuído
  getCacheCluster(key: string): CacheCluster;

  // Queue processing
  getQueueWorker(jobType: string): QueueWorker;
}
```

### Monitoramento de Performance
```typescript
// Métricas de performance
class PerformanceMonitor {
  trackApiCall(endpoint: string, duration: number, status: number) {
    metrics.histogram('api_duration_ms', duration, {
      endpoint,
      status: status.toString(),
    });
  }

  trackDatabaseQuery(query: string, duration: number) {
    metrics.histogram('db_query_duration_ms', duration, {
      query_type: this.getQueryType(query),
    });
  }

  trackMemoryUsage() {
    const usage = process.memoryUsage();
    metrics.gauge('memory_usage_mb', usage.heapUsed / 1024 / 1024);
  }
}
```

---

**Documento criado em**: [Data]
**Última atualização**: [Data]
**Versão**: 1.0
**Responsável**: [Nome do Arquiteto de Software]