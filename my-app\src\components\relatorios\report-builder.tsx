'use client';

import { useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import {
  Database,
  BarChart3,
  Calendar,
  User,
  Clock,
  TrendingUp,
  Eye,
  Trash2
} from 'lucide-react';

interface Field {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  category: 'funcionario' | 'ponto' | 'periodo' | 'calculo';
  icon: React.ReactNode;
}

interface ReportElement {
  id: string;
  fieldId: string;
  type: 'table' | 'chart' | 'card' | 'filter';
  config: Record<string, unknown>;
}

const availableFields: Field[] = [
  // Funcionário
  { id: 'func_nome', name: 'Nome do Funcionário', type: 'text', category: 'funcionario', icon: <User className="w-4 h-4" /> },
  { id: 'func_matricula', name: '<PERSON><PERSON><PERSON><PERSON>', type: 'text', category: 'funcionario', icon: <Database className="w-4 h-4" /> },
  { id: 'func_departamento', name: 'Departamento', type: 'text', category: 'funcionario', icon: <Database className="w-4 h-4" /> },
  { id: 'func_cargo', name: 'Cargo', type: 'text', category: 'funcionario', icon: <Database className="w-4 h-4" /> },
  
  // Ponto
  { id: 'ponto_data', name: 'Data', type: 'date', category: 'ponto', icon: <Calendar className="w-4 h-4" /> },
  { id: 'ponto_entrada', name: 'Horário de Entrada', type: 'text', category: 'ponto', icon: <Clock className="w-4 h-4" /> },
  { id: 'ponto_saida', name: 'Horário de Saída', type: 'text', category: 'ponto', icon: <Clock className="w-4 h-4" /> },
  { id: 'ponto_horas_normais', name: 'Horas Normais', type: 'number', category: 'ponto', icon: <Clock className="w-4 h-4" /> },
  { id: 'ponto_horas_extras', name: 'Horas Extras', type: 'number', category: 'ponto', icon: <TrendingUp className="w-4 h-4" /> },
  { id: 'ponto_atrasos', name: 'Atrasos (min)', type: 'number', category: 'ponto', icon: <Clock className="w-4 h-4" /> },
  
  // Período
  { id: 'periodo_inicio', name: 'Início do Período', type: 'date', category: 'periodo', icon: <Calendar className="w-4 h-4" /> },
  { id: 'periodo_fim', name: 'Fim do Período', type: 'date', category: 'periodo', icon: <Calendar className="w-4 h-4" /> },
  
  // Cálculos
  { id: 'calc_total_horas', name: 'Total de Horas', type: 'number', category: 'calculo', icon: <BarChart3 className="w-4 h-4" /> },
  { id: 'calc_percentual_presenca', name: 'Percentual de Presença', type: 'number', category: 'calculo', icon: <BarChart3 className="w-4 h-4" /> },
  { id: 'calc_media_horas_dia', name: 'Média Horas/Dia', type: 'number', category: 'calculo', icon: <BarChart3 className="w-4 h-4" /> },
];

const categoryColors = {
  funcionario: 'bg-blue-100 text-blue-800 border-blue-200',
  ponto: 'bg-green-100 text-green-800 border-green-200',
  periodo: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  calculo: 'bg-purple-100 text-purple-800 border-purple-200'
};

const categoryNames = {
  funcionario: 'Funcionário',
  ponto: 'Registros de Ponto',
  periodo: 'Período',
  calculo: 'Cálculos'
};

export function ReportBuilder() {
  const [selectedFields, setSelectedFields] = useState<ReportElement[]>([]);
  const [previewMode, setPreviewMode] = useState(false);

  const handleDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;

    // Se arrastou de availableFields para selectedFields
    if (source.droppableId === 'available' && destination.droppableId === 'selected') {
      const field = availableFields[source.index];
      const newElement: ReportElement = {
        id: `${field.id}_${Date.now()}`,
        fieldId: field.id,
        type: 'table', // Tipo padrão
        config: {}
      };
      
      const newSelected = [...selectedFields];
      newSelected.splice(destination.index, 0, newElement);
      setSelectedFields(newSelected);
    }
    
    // Se reordenou dentro de selectedFields
    if (source.droppableId === 'selected' && destination.droppableId === 'selected') {
      const newSelected = [...selectedFields];
      const [removed] = newSelected.splice(source.index, 1);
      newSelected.splice(destination.index, 0, removed);
      setSelectedFields(newSelected);
    }
  }, [selectedFields]);

  const removeField = useCallback((elementId: string) => {
    setSelectedFields(prev => prev.filter(el => el.id !== elementId));
  }, []);

  const updateElementType = useCallback((elementId: string, newType: ReportElement['type']) => {
    setSelectedFields(prev => prev.map(el => 
      el.id === elementId ? { ...el, type: newType } : el
    ));
  }, []);

  const getFieldById = (fieldId: string) => availableFields.find(f => f.id === fieldId);

  const renderFieldLibrary = () => (
    <div className="w-80 bg-gray-50 border-r border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Biblioteca de Campos</h3>
      
      {Object.entries(categoryNames).map(([category, name]) => (
        <div key={category} className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">{name}</h4>
          <Droppable droppableId="available" isDropDisabled>
            {(provided) => (
              <div ref={provided.innerRef} {...provided.droppableProps} className="space-y-2">
                {availableFields
                  .filter(field => field.category === category)
                  .map((field, index) => (
                    <Draggable key={field.id} draggableId={field.id} index={index}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className={`
                            p-3 rounded-lg border cursor-move transition-all
                            ${categoryColors[field.category]}
                            ${snapshot.isDragging ? 'shadow-lg scale-105' : 'hover:shadow-md'}
                          `}
                        >
                          <div className="flex items-center space-x-2">
                            {field.icon}
                            <span className="text-sm font-medium">{field.name}</span>
                          </div>
                          <div className="text-xs mt-1 opacity-75">{field.type}</div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </div>
      ))}
    </div>
  );

  const renderDesignArea = () => (
    <div className="flex-1 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Área de Design</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              previewMode 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <Eye className="w-4 h-4 mr-1 inline" />
            {previewMode ? 'Editar' : 'Preview'}
          </button>
        </div>
      </div>

      <Droppable droppableId="selected">
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`
              min-h-96 border-2 border-dashed rounded-lg p-4 transition-colors
              ${snapshot.isDraggingOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'}
              ${selectedFields.length === 0 ? 'flex items-center justify-center' : ''}
            `}
          >
            {selectedFields.length === 0 ? (
              <div className="text-center text-gray-500">
                <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">Arraste campos aqui</p>
                <p className="text-sm">Comece arrastando campos da biblioteca para criar seu relatório</p>
              </div>
            ) : (
              <div className="space-y-4">
                {selectedFields.map((element, index) => {
                  const field = getFieldById(element.fieldId);
                  if (!field) return null;

                  return (
                    <Draggable key={element.id} draggableId={element.id} index={index}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`
                            bg-white border rounded-lg p-4 transition-all
                            ${snapshot.isDragging ? 'shadow-lg scale-105' : 'shadow-sm hover:shadow-md'}
                          `}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2" {...provided.dragHandleProps}>
                              {field.icon}
                              <span className="font-medium">{field.name}</span>
                              <span className={`px-2 py-1 rounded text-xs ${categoryColors[field.category]}`}>
                                {categoryNames[field.category]}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <select
                                value={element.type}
                                onChange={(e) => updateElementType(element.id, e.target.value as ReportElement['type'])}
                                className="text-sm border border-gray-300 rounded px-2 py-1"
                              >
                                <option value="table">Tabela</option>
                                <option value="chart">Gráfico</option>
                                <option value="card">Card</option>
                                <option value="filter">Filtro</option>
                              </select>
                              <button
                                onClick={() => removeField(element.id)}
                                className="text-red-600 hover:text-red-800"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          
                          {previewMode && (
                            <div className="border-t pt-3 mt-3">
                              {element.type === 'table' && (
                                <div className="text-sm text-gray-600">
                                  📊 Tabela: {field.name}
                                </div>
                              )}
                              {element.type === 'chart' && (
                                <div className="text-sm text-gray-600">
                                  📈 Gráfico: {field.name}
                                </div>
                              )}
                              {element.type === 'card' && (
                                <div className="text-sm text-gray-600">
                                  🃏 Card: {field.name}
                                </div>
                              )}
                              {element.type === 'filter' && (
                                <div className="text-sm text-gray-600">
                                  🔍 Filtro: {field.name}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </Draggable>
                  );
                })}
              </div>
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="flex h-96 border border-gray-200 rounded-lg overflow-hidden">
        {renderFieldLibrary()}
        {renderDesignArea()}
      </div>
    </DragDropContext>
  );
}

